defmodule MisReports.Repo.Migrations.CreateTblLoansAdvances do
  use Ecto.Migration

  def change do
    create table(:tbl_loans_advances) do
      add :account_no, :string
      add :cust_no, :string
      add :account_name, :string
      add :regulatory_sector, :string
      add :account_ccy_code, :string
      add :scheme_code, :string
      add :charge_off_flag, :decimal, precision: 18, scale: 2
      add :lcy_exchange_rate, :decimal, precision: 18, scale: 2
      add :dr_book_bal, :decimal, precision: 18, scale: 2
      add :limit_amount, :decimal, precision: 18, scale: 2
      add :bus_unit_short_desc, :string
      add :past_due_days, :integer
      add :loan_category, :string
      add :cust_number_1, :string
      add :prudential_sector, :string
      add :scheme_code_desc, :string
      add :lcy_dr_book_bal, :decimal, precision: 18, scale: 2
      add :lcy_limit_amount, :decimal, precision: 18, scale: 2
      add :arrear_buckets, :string
      add :loan_type, :string
      add :arrear_buckets_2, :string
      add :days_past_due, :integer
      add :claims_split, :string
      add :credit_type, :string
      add :facility_type, :string
      add :prudential_sector_new_temp, :string
      add :stage, :string
      add :accraul_reinstatement, :string
      add :past_due_npl_return_class, :string
      add :specific_provisions, :decimal, precision: 18, scale: 2
      add :susp_interest, :decimal, precision: 18, scale: 2
      add :class_type, :string
      add :outstanding_amt, :decimal, precision: 18, scale: 2
      add :interest_rate, :decimal, precision: 18, scale: 2
      add :sub_sector_desc, :string
      add :institutional_units, :string
      add :effective_interest_rate, :decimal, precision: 18, scale: 2
      add :record_status, :string
      add :maker_id, references(:tbl_users, column: :id, on_delete: :nothing)
      add :checker_id, references(:tbl_users, column: :id, on_delete: :nothing)

      timestamps()
    end
  end
end
