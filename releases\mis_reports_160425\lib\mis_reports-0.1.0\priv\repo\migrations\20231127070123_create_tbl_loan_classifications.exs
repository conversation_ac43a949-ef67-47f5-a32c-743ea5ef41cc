defmodule MisReports.Repo.Migrations.CreateTblLoanClassifications do
  use Ecto.Migration

  def change do
    create table(:tbl_loan_classification) do
      add :loan_classification, :string
      add :days_past_due, :string
      add :past_due_npl_classification, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
