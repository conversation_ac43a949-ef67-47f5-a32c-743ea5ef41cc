defmodule MisReports.Repo.Migrations.CreateTblSubmissionDates do
  use Ecto.Migration

  def change do
    create table(:tbl_submission_dates) do
      add :submission_date, :date
      add :report_frequency, :string
      add :descript, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
