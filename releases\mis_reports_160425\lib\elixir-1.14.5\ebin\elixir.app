{application,elixir,
    [{description,"elixir"},
     {vsn,"1.14.5"},
     {modules,
         ['Elixir.Access','Elixir.Agent.Server','Elixir.Agent',
          'Elixir.Application','Elixir.ArgumentError',
          'Elixir.ArithmeticError','Elixir.Atom','Elixir.BadArityError',
          'Elixir.BadBooleanError','Elixir.BadFunctionError',
          'Elixir.BadMapError','Elixir.BadStructError','Elixir.Base',
          'Elixir.Behaviour','Elixir.Bitwise','Elixir.Calendar.ISO',
          'Elixir.Calendar.TimeZoneDatabase',
          'Elixir.Calendar.UTCOnlyTimeZoneDatabase','Elixir.Calendar',
          'Elixir.CaseClauseError','Elixir.Code.Formatter',
          'Elixir.Code.Fragment','Elixir.Code.Identifier',
          'Elixir.Code.LoadError','Elixir.Code.Normalizer',
          'Elixir.Code.Typespec','Elixir.Code','Elixir.Collectable.BitString',
          'Elixir.Collectable.File.Stream','Elixir.Collectable.HashDict',
          'Elixir.Collectable.HashSet','Elixir.Collectable.IO.Stream',
          'Elixir.Collectable.List','Elixir.Collectable.Map',
          'Elixir.Collectable.MapSet','Elixir.Collectable',
          'Elixir.CompileError','Elixir.CondClauseError',
          'Elixir.Config.Provider','Elixir.Config.Reader','Elixir.Config',
          'Elixir.Date.Range','Elixir.Date','Elixir.DateTime','Elixir.Dict',
          'Elixir.DynamicSupervisor','Elixir.Enum.EmptyError',
          'Elixir.Enum.OutOfBoundsError','Elixir.Enum',
          'Elixir.Enumerable.Date.Range','Elixir.Enumerable.File.Stream',
          'Elixir.Enumerable.Function','Elixir.Enumerable.GenEvent.Stream',
          'Elixir.Enumerable.HashDict','Elixir.Enumerable.HashSet',
          'Elixir.Enumerable.IO.Stream','Elixir.Enumerable.List',
          'Elixir.Enumerable.Map','Elixir.Enumerable.MapSet',
          'Elixir.Enumerable.Range','Elixir.Enumerable.Stream',
          'Elixir.Enumerable','Elixir.ErlangError','Elixir.Exception',
          'Elixir.File.CopyError','Elixir.File.Error','Elixir.File.LinkError',
          'Elixir.File.RenameError','Elixir.File.Stat','Elixir.File.Stream',
          'Elixir.File','Elixir.Float','Elixir.Function',
          'Elixir.FunctionClauseError','Elixir.GenEvent.Stream',
          'Elixir.GenEvent','Elixir.GenServer','Elixir.HashDict',
          'Elixir.HashSet','Elixir.IO.ANSI.Docs','Elixir.IO.ANSI.Sequence',
          'Elixir.IO.ANSI','Elixir.IO.Stream','Elixir.IO.StreamError',
          'Elixir.IO','Elixir.Inspect.Algebra','Elixir.Inspect.Any',
          'Elixir.Inspect.Atom','Elixir.Inspect.BitString',
          'Elixir.Inspect.Date.Range','Elixir.Inspect.Date',
          'Elixir.Inspect.DateTime','Elixir.Inspect.Error',
          'Elixir.Inspect.Float','Elixir.Inspect.Function',
          'Elixir.Inspect.HashDict','Elixir.Inspect.HashSet',
          'Elixir.Inspect.Inspect.Error','Elixir.Inspect.Integer',
          'Elixir.Inspect.List','Elixir.Inspect.Macro.Env',
          'Elixir.Inspect.Map','Elixir.Inspect.MapSet',
          'Elixir.Inspect.NaiveDateTime','Elixir.Inspect.Opts',
          'Elixir.Inspect.PID','Elixir.Inspect.Port','Elixir.Inspect.Range',
          'Elixir.Inspect.Reference','Elixir.Inspect.Regex',
          'Elixir.Inspect.Stream','Elixir.Inspect.Time',
          'Elixir.Inspect.Tuple','Elixir.Inspect.URI',
          'Elixir.Inspect.Version.Requirement','Elixir.Inspect.Version',
          'Elixir.Inspect','Elixir.Integer','Elixir.Kernel.CLI',
          'Elixir.Kernel.ErrorHandler','Elixir.Kernel.LexicalTracker',
          'Elixir.Kernel.ParallelCompiler','Elixir.Kernel.ParallelRequire',
          'Elixir.Kernel.SpecialForms','Elixir.Kernel.Typespec',
          'Elixir.Kernel.Utils','Elixir.Kernel','Elixir.KeyError',
          'Elixir.Keyword','Elixir.List.Chars.Atom',
          'Elixir.List.Chars.BitString','Elixir.List.Chars.Float',
          'Elixir.List.Chars.Integer','Elixir.List.Chars.List',
          'Elixir.List.Chars','Elixir.List','Elixir.Macro.Env','Elixir.Macro',
          'Elixir.Map','Elixir.MapSet','Elixir.MatchError',
          'Elixir.Module.LocalsTracker','Elixir.Module.ParallelChecker',
          'Elixir.Module.Types.Error','Elixir.Module.Types.Expr',
          'Elixir.Module.Types.Helpers','Elixir.Module.Types.Of',
          'Elixir.Module.Types.Pattern','Elixir.Module.Types.Unify',
          'Elixir.Module.Types','Elixir.Module','Elixir.NaiveDateTime',
          'Elixir.Node','Elixir.OptionParser.ParseError',
          'Elixir.OptionParser','Elixir.PartitionSupervisor',
          'Elixir.Path.Wildcard','Elixir.Path','Elixir.Port','Elixir.Process',
          'Elixir.Protocol.UndefinedError','Elixir.Protocol','Elixir.Range',
          'Elixir.Record.Extractor','Elixir.Record',
          'Elixir.Regex.CompileError','Elixir.Regex',
          'Elixir.Registry.Partition','Elixir.Registry.Supervisor',
          'Elixir.Registry','Elixir.RuntimeError','Elixir.Set',
          'Elixir.Stream.Reducers','Elixir.Stream','Elixir.String.Break',
          'Elixir.String.Chars.Atom','Elixir.String.Chars.BitString',
          'Elixir.String.Chars.Date','Elixir.String.Chars.DateTime',
          'Elixir.String.Chars.Float','Elixir.String.Chars.Integer',
          'Elixir.String.Chars.List','Elixir.String.Chars.NaiveDateTime',
          'Elixir.String.Chars.Time','Elixir.String.Chars.URI',
          'Elixir.String.Chars.Version.Requirement',
          'Elixir.String.Chars.Version','Elixir.String.Chars',
          'Elixir.String.Tokenizer.ScriptSet',
          'Elixir.String.Tokenizer.Security','Elixir.String.Tokenizer',
          'Elixir.String.Unicode','Elixir.String','Elixir.StringIO',
          'Elixir.Supervisor.Default','Elixir.Supervisor.Spec',
          'Elixir.Supervisor','Elixir.SyntaxError','Elixir.System.EnvError',
          'Elixir.System.SignalHandler','Elixir.System',
          'Elixir.SystemLimitError','Elixir.Task.Supervised',
          'Elixir.Task.Supervisor','Elixir.Task','Elixir.Time',
          'Elixir.TokenMissingError','Elixir.TryClauseError','Elixir.Tuple',
          'Elixir.URI.Error','Elixir.URI','Elixir.UndefinedFunctionError',
          'Elixir.UnicodeConversionError',
          'Elixir.Version.InvalidRequirementError',
          'Elixir.Version.InvalidVersionError','Elixir.Version.Parser',
          'Elixir.Version.Requirement','Elixir.Version',
          'Elixir.WithClauseError',elixir,elixir_aliases,elixir_bitstring,
          elixir_bootstrap,elixir_clauses,elixir_code_server,elixir_compiler,
          elixir_config,elixir_def,elixir_dispatch,elixir_env,elixir_erl,
          elixir_erl_clauses,elixir_erl_compiler,elixir_erl_for,
          elixir_erl_pass,elixir_erl_try,elixir_erl_var,elixir_errors,
          elixir_expand,elixir_fn,elixir_import,elixir_interpolation,
          elixir_lexical,elixir_locals,elixir_map,elixir_module,
          elixir_overridable,elixir_parser,elixir_quote,elixir_rewrite,
          elixir_sup,elixir_tokenizer,elixir_utils]},
     {registered,[elixir_sup,elixir_config,elixir_code_server]},
     {applications,[kernel,stdlib,compiler]},
     {mod,{elixir,[]}},
     {env,
         [{ansi_enabled,false},
          {ansi_syntax_colors,
              [{atom,cyan},
               {binary,default_color},
               {boolean,magenta},
               {charlist,yellow},
               {list,default_color},
               {map,default_color},
               {nil,magenta},
               {number,yellow},
               {string,green},
               {tuple,default_color}]},
          {check_endianness,true},
          {dbg_callback,{'Elixir.Macro',dbg,[]}},
          {time_zone_database,'Elixir.Calendar.UTCOnlyTimeZoneDatabase'}]}]}.
