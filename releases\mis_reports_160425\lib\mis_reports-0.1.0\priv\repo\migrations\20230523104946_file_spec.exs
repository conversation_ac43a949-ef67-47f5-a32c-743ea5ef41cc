defmodule MisReports.Repo.Migrations.CreateTempName do
  use Ecto.Migration

  def change do
    create table(:file_specifications) do
      add :temp_name, :string
      add :read_line, :integer
      add :maker_dt, :naive_datetime
      add :checker_dt, :naive_datetime
      add :auth_status, :string
      add :columns, :map
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:file_specifications, [:maker_id])
    create index(:file_specifications, [:checker_id])
    create unique_index(:file_specifications, [:temp_name], name: :unique_temp_name)
  end
end
