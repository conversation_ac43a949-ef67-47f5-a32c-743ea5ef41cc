defmodule MisReports.Repo.Migrations.CreateTblCustomerDetails do
  use Ecto.Migration

  def change do
    create table(:tbl_customer_details) do
      add :account_no, :string
      add :cust_name, :string
      add :cust_type, :string
      add :economic_sector, :string
      add :economic_sub_sector, :string
      add :institutional_units_and_sectors, :string
      add :non_resident_type, :string
      add :deposit_type, :string
      add :time_deposit_type, :string
      add :currency, :string
      add :interconnectedness, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create unique_index(:tbl_customer_details, [:account_no])
    create index(:tbl_customer_details, [:maker_id])
    create index(:tbl_customer_details, [:checker_id])
  end
end
