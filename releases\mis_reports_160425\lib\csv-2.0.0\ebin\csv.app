{application,csv,
             [{applications,[kernel,stdlib,elixir,parallel_stream]},
              {description,"CSV Decoding and Encoding for Elixir"},
              {modules,['Elixir.CSV','Elixir.CSV.Decoding.Decoder',
                        'Elixir.CSV.Decoding.Lexer',
                        'Elixir.CSV.Decoding.Parser',
                        'Elixir.CSV.Decoding.Preprocessing.Lines',
                        'Elixir.CSV.Decoding.Preprocessing.None',
                        'Elixir.CSV.Defaults','Elixir.CSV.Encode',
                        'Elixir.CSV.Encode.Any','Elixir.CSV.Encode.BitString',
                        'Elixir.CSV.Encoding.Encoder',
                        'Elixir.CSV.EncodingError',
                        'Elixir.CSV.EscapeSequenceError',
                        'Elixir.CSV.RowLengthError']},
              {registered,[]},
              {vsn,"2.0.0"}]}.
