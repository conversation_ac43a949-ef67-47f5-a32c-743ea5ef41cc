defmodule MisReports.Repo.Migrations.AlterTblLoanProducts do
  use Ecto.Migration

  def up do
    alter table(:tbl_loan_products) do
      add :type_of_security, :string
      add :security_value_ratio, :decimal, precision: 18, scale: 2
      add :collateral_type, :string
    end
  end

  def down do
    alter table(:tbl_loan_products) do
      remove :type_of_security
      remove :security_value_ratio
      remove :collateral_type
    end
  end
end
