defmodule MisReports.Repo.Migrations.AlterTblmAllowances do
  use Ecto.Migration

  def up do
    alter table(:tblm_allowances) do
      add :recoveries_description, :string
      add :recoveries_currency, :string
      add :recoveries_amount, :decimal, precision: 18, scale: 2
      add :recoveries_comment, :string
      add :write_offs_description, :string
      add :write_offs_currency, :string
      add :write_offs_amount, :decimal, precision: 18, scale: 2
      add :write_offs_comment, :string
      add :any_other_adjustment_description, :string
      add :any_other_adjustment_currency, :string
      add :any_other_adjustment_amount, :decimal, precision: 18, scale: 2
      add :any_other_adjustment_comment, :string
      add :general_bal_allowance, :decimal, precision: 18, scale: 2
      modify(:general_exchange_differences, :decimal, precision: 18, scale: 2)
    end
  end

  def down do
    alter table(:tblm_allowances) do
      remove :recoveries_description
      remove :recoveries_currency
      remove :recoveries_amount
      remove :recoveries_comment
      remove :write_offs_description
      remove :write_offs_currency
      remove :write_offs_amount
      remove :write_offs_comment
      remove :any_other_adjustment_description
      remove :any_other_adjustment_currency
      remove :any_other_adjustment_amount
      remove :any_other_adjustment_comment
      remove :general_bal_allowance
      # remove :general_exchange_differences
    end
  end
end
