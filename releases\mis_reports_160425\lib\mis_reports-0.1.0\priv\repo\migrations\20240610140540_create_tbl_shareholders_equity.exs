defmodule MisReports.Repo.Migrations.CreateTblShareholdersEquity do
  use Ecto.Migration

  def change do
    create table(:tbl_shareholders_equity) do
      add :retain_blc_month, :decimal
      add :retain_net_income_month, :decimal
      add :retain_period_adjust, :decimal
      add :retain_fair_com_income, :decimal
      add :retain_cash_net_tax, :decimal
      add :retain_tranfers_retained, :decimal
      add :retain_divedends, :decimal
      add :retain_oth_incre_month, :decimal
      add :fair_blc_month, :decimal
      add :fair_net_income_month, :decimal
      add :fair_period_adjust, :decimal
      add :fair_fair_com_income, :decimal
      add :fair_cash_net_tax, :decimal
      add :fair_tranfers_retained, :decimal
      add :fair_divedends, :decimal
      add :fair_oth_incre_month, :decimal
      add :rev_blc_month, :decimal
      add :rev_net_income_month, :decimal
      add :rev_period_adjust, :decimal
      add :rev_fair_com_income, :decimal
      add :rev_cash_net_tax, :decimal
      add :rev_tranfers_retained, :decimal
      add :rev_divedends, :decimal
      add :rev_oth_incre_month, :decimal
      add :statu_blc_month, :decimal
      add :statu_net_income_month, :decimal
      add :statu_period_adjust, :decimal
      add :statu_fair_com_income, :decimal
      add :statu_cash_net_tax, :decimal
      add :statu_tranfers_retained, :decimal
      add :statu_divedends, :decimal
      add :statu_oth_incre_month, :decimal
      add :oth_blc_month, :decimal
      add :oth_net_income_month, :decimal
      add :oth_period_adjust, :decimal
      add :oth_fair_com_income, :decimal
      add :oth_cash_net_tax, :decimal
      add :oth_tranfers_retained, :decimal
      add :oth_divedends, :decimal
      add :oth_oth_incre_month, :decimal
      add :report_date, :date
      add :maker_id, :integer
      add :checker_id, :integer

      timestamps()
    end
  end
end
