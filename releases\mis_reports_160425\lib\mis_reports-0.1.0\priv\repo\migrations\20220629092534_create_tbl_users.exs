defmodule MisReports.Repo.Migrations.CreateTblUsers do
  use Ecto.Migration

  def change do
    create table(:tbl_users) do
      add :email, :string
      add :first_name, :string
      add :last_name, :string
      add :mobile, :string
      add :status, :string, default: "D"
      add :username, :string
      add :password_hash, :string
      add :auto_password, :string
      add :login_attempt, :integer
      add :remote_ip, :string
      add :last_login, :date
      add :otp_secret, :string

      timestamps()
    end

    create unique_index(:tbl_users, [:username])
    create unique_index(:tbl_users, [:email])
  end
end
