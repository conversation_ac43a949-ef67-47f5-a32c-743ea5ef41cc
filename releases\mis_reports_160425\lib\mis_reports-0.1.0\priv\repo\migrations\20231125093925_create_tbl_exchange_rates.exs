defmodule MisReports.Repo.Migrations.CreateTblExchangeRates do
  use Ecto.Migration

  def change do
    create table(:tbl_exchange_rates) do
      add :currency_code, :string
      add :currency_description, :string
      add :amount, :decimal, precision: 18, scale: 2
      add :exchange_rate_lcy, :decimal, precision: 18, scale: 4
      add :month, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
