defmodule MisReports.Repo.Migrations.AddStatusTblBsaMaster do
  use Ecto.Migration

  def up do
    alter table(:tbl_bsa_master) do
      add :status, :string
      add :auth_status, :string
      add :response, :string
      add :start_date, :date
      add :end_date, :date
      remove :report_date
    end
  end

  def down do
    alter table(:tbl_bsa_master) do
      remove :status
      remove :auth_status
      remove :response
      remove :start_date
      remove :end_date
      add :report_date, :date
    end
  end
end
