defmodule MisReports.Repo.Migrations.CreateTblBalanceDueDomestic do
  use Ecto.Migration

  def change do
    create table(:tbl_balance_due_domestic) do
      add :a16, :string
      add :b16, :string
      add :c16, :string
      add :d16, :string
      add :e16, :string
      add :f16, :string
      add :g16, :decimal
      add :h16, :decimal
      add :i16, :string
      add :j16, :string
      add :k16, :date
      add :l16, :date
      add :m16, :string
      add :status, :string

      timestamps()
    end
  end
end
