
defmodule MisReports.Repo.Migrations.CreateTblGovernmentExposureList do
  use Ecto.Migration

  def change do
    create table(:tbl_government_exposure_list) do
      add :account_no, :string
      add :cif, :string
      add :customer_name, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
