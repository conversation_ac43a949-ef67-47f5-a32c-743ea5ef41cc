defmodule MisReports.Repo.Migrations.CreateTblmSecurity do
  use Ecto.Migration

  def change do
    create table(:tblm_security) do
      add :counter_name, :string
      add :expiry_date, :date
      add :pledge_date, :date
      add :security_type, :string
      add :purpose_pledge, :string
      add :cost_security, :decimal
      add :security_pledge, :decimal
      add :currency, :string
      add :total_secured, :decimal
      add :report_date, :date

      timestamps()
    end
  end
end
