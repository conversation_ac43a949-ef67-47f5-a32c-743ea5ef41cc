defmodule MisReports.Repo.Migrations.CreateTblEmployeeBenefit do
  use Ecto.Migration

  def change do
    create table(:tbl_employee_benefit) do
      add :descript, :string
      add :month, :string
      add :year, :string
      add :exec_manag, :string
      add :snr_manag, :string
      add :managers, :string
      add :other_staff, :string
      add :gen_staff, :string
      add :status, :string
      add :maker_date, :naive_datetime
      add :checker_date, :naive_datetime
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
