defmodule MisReports.Repo.Migrations.CreateTblTax do
  use Ecto.Migration

  def change do
    create table(:tbl_tax) do
      add :carry_fwd_opening, :decimal, precision: 18, scale: 2
      add :carry_mvt_month, :decimal, precision: 18, scale: 2
      add :carry_mvt_equity, :decimal, precision: 18, scale: 2
      add :prop_opening_asset, :decimal, precision: 18, scale: 2
      add :prop_mvt_month, :decimal, precision: 18, scale: 2
      add :prop_mvt_equity, :decimal, precision: 18, scale: 2
      add :ifrs_opening_assets, :decimal, precision: 18, scale: 2
      add :ifrs_mvt_month, :decimal, precision: 18, scale: 2
      add :ifrs_mvt_equity, :decimal, precision: 18, scale: 2
      add :fair_opening_assets, :decimal, precision: 18, scale: 2
      add :fair_mvt_month, :decimal, precision: 18, scale: 2
      add :fair_mvt_equity, :decimal, precision: 18, scale: 2
      add :oth_opening_asset, :decimal, precision: 18, scale: 2
      add :oth_mvt_month, :decimal, precision: 18, scale: 2
      add :oth_mvt_equity, :decimal, precision: 18, scale: 2
      add :deffered_descr, :string
      add :deffered_amunt, :decimal
      add :deffered_descr1, :string
      add :deffered_amunt1, :decimal
      add :deffered_descr2, :string
      add :deffered_amunt2, :decimal
      add :deffered_descr3, :string
      add :deffered_amunt3, :decimal
      add :deffered_descr4, :string
      add :deffered_amunt4, :decimal

      timestamps()
    end
  end
end
