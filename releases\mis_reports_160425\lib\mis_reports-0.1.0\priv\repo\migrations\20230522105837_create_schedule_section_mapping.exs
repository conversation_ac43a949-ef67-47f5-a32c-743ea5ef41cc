defmodule MisReports.Repo.Migrations.CreateScheduleSectionMapping do
  use Ecto.Migration

  def change do
    create table(:sched_section_mappings) do
      add :temp_name, :string, size: 100
      add :schedule_name, :string, size: 100
      add :schedule_line_index, :integer
      add :accounts, :map
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:sched_section_mappings, [:maker_id])
    create index(:sched_section_mappings, [:checker_id])

    create unique_index(
             :sched_section_mappings,
             [:temp_name, :schedule_name, :schedule_line_index],
             name: :unique_schedule_mapping
           )
  end
end
