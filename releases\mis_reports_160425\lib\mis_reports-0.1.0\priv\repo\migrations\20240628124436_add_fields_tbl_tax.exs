defmodule MisReports.Repo.Migrations.AddFieldsTblTax do
  use Ecto.Migration

  def up do
    # alter table(:tbl_tax) do
    #   add :b41, :decimal
    #   add :b42, :decimal
    #   add :b43, :decimal
    #   add :b44, :decimal
    #   add :b45, :decimal
    #   add :b46, :decimal
    #   add :b47, :decimal
    #   add :c41, :decimal
    #   add :c42, :decimal
    #   add :c43, :decimal
    #   add :c44, :decimal
    #   add :c45, :decimal
    #   add :c46, :decimal
    #   add :c47, :decimal
    #   add :status, :string


    # end
  end

  def down do
    # alter table(:tbl_tax) do
    #   remove  :b41
    #   remove  :b42
    #   remove  :b43
    #   remove  :b44
    #   remove  :b45
    #   remove  :b46
    #   remove  :b47
    #   remove  :c41
    #   remove  :c42
    #   remove  :c43
    #   remove  :c44
    #   remove  :c45
    #   remove  :c46
    #   remove  :c47
    #   remove  :status
    # end
  end
end
