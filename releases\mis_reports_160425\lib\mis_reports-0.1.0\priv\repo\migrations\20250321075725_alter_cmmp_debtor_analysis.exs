defmodule MisReports.Repo.Migrations.AlterCmmpDebtorAnalysis do
  use Ecto.Migration

  def up do
    alter table(:tbl_debtors_book_analysis) do
      add :allowance_for_losses_mortgages, :decimal, precision: 18, scale: 2
      add :allowance_for_losses_leases, :decimal, precision: 18, scale: 2
      add :allowance_for_losses_unsecured, :decimal, precision: 18, scale: 2
      add :allowance_for_losses_revolving, :decimal, precision: 18, scale: 2

      add :number_of_accounts_mortgages, :decimal, precision: 18, scale: 2
      add :number_of_accounts_leases, :decimal, precision: 18, scale: 2
      add :number_of_accounts_unsecured, :decimal, precision: 18, scale: 2
      add :number_of_accounts_revolving, :decimal, precision: 18, scale: 2
    end
  end

  def down do
    alter table(:tbl_debtors_book_analysis) do
      remove :allowance_for_losses_mortgages
      remove :allowance_for_losses_leases
      remove :allowance_for_losses_unsecured
      remove :allowance_for_losses_revolving

      remove :number_of_accounts_mortgages
      remove :number_of_accounts_leases
      remove :number_of_accounts_unsecured
      remove :number_of_accounts_revolving
    end
  end
end
