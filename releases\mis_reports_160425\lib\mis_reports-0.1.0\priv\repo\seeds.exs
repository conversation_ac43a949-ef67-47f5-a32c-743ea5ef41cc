# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     MisReports.Repo.insert!(%MisReports.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

role_str = %{
  "user" => %{
    "edit" => "Y",
    "index" => "Y",
    "new" => "Y",
    "update_status" => "Y"
  },
  "user_role" => %{
    "change_status" => "Y",
    "delete" => "Y",
    "edit" => "Y",
    "index" => "Y",
    "new" => "Y"
  }
}

MisReports.Accounts.create_user_role(%{role_desc: "Admin", role_str: role_str, status: "A"})

[roles | _] = MisReports.Accounts.list_user_roles()

MisReports.Accounts.create_user(%{
  first_name: "Ad<PERSON>",
  last_name: "User",
  email: "<EMAIL>",
  password_hash: "P@ssw0rd",
  status: "A",
  username: "admin",
  role_id: roles.id
})
