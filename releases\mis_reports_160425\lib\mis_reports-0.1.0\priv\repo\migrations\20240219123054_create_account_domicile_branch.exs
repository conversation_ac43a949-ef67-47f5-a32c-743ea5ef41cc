defmodule MisReports.Repo.Migrations.CreateAccountDomicileBranch do
  use Ecto.Migration

  def change do
    create table(:tbl_account_domicile_branch) do
      add :account_domicile_branch, :string
      add :province, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

  end
end
