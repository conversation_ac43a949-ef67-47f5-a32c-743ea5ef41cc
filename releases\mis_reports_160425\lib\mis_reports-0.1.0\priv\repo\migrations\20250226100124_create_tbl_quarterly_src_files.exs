defmodule MisReports.Repo.Migrations.CreateTblQuarterlySrcFiles do
  use Ecto.Migration

  def change do
    create table(:tbl_quarterly_src_files) do
      add :filename, :string
      add :date, :date
      add :month, :string
      add :year, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:tbl_quarterly_src_files, [:maker_id])
    create index(:tbl_quarterly_src_files, [:checker_id])
  end
end
