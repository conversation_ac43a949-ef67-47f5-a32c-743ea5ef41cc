defmodule MisReports.Repo.Migrations.CreateTblCompanySettings do
  use Ecto.Migration

  def change do
    create table(:tbl_company_settings) do
      add :param_name, :string
      add :param_value, :string
      add :modifier_id, references(:tbl_users, on_delete: :nothing)
      add :maker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:tbl_company_settings, [:modifier_id])
    create index(:tbl_company_settings, [:maker_id])
  end
end
