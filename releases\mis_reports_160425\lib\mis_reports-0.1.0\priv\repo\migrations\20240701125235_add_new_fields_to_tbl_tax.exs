defmodule MisReports.Repo.Migrations.AddNewFieldsToTblTax do
  use Ecto.Migration

  def up do
    alter table(:tbl_tax) do
      add :status, :string
      add :b41, :string
      add :c41, :decimal
      add :b42, :string
      add :c42, :decimal
      add :b43, :string
      add :c43, :decimal
      add :b44, :string
      add :c44, :decimal
      add :b45, :string
      add :c45, :decimal
      add :b46, :string
      add :c46, :decimal
    end
  end

  def down do
    alter table(:tbl_tax) do
      remove :status
      remove :b41
      remove :c41
      remove :b42
      remove :c42
      remove :b43
      remove :c43
      remove :b44
      remove :c44
      remove :b45
      remove :c45
      remove :b46
      remove :c46
    end
  end
end
