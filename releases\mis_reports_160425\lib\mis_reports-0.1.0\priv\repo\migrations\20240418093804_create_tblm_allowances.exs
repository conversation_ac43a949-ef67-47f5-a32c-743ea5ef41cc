defmodule MisReports.Repo.Migrations.CreateTblmAllowances do
  use Ecto.Migration

  def change do
    create table(:tblm_allowances) do
      add :decription, :string
      add :currency, :string
      add :type_adjust, :string
      add :amount, :decimal
      add :status, :string
      add :maker_id, :integer
      add :checker_id, :integer
      add :report_date, :date
      add :specific_bal_allowaance, :decimal
      add :specific_add_provision, :decimal
      add :specific_exchange_difference, :decimal
      add :general_add_provision, :decimal
      add :general_exchange_differences, :string
      add :general_any_adjust, :decimal
      add :comment, :string

      timestamps()
    end
  end
end
