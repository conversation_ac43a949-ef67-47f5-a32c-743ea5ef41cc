
defmodule MisReports.Repo.Migrations.CreateTblLoanProducts do
  use Ecto.Migration

  def change do
    create table(:tbl_loan_products) do
      add :product, :string
      add :product_type, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end

    create index(:tbl_loan_products, [:maker_id])
    create index(:tbl_loan_products, [:checker_id])
  end
end
