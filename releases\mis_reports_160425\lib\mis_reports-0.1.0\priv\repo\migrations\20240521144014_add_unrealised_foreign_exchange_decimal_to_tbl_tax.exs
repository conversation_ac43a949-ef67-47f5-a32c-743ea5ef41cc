defmodule MisReports.Repo.Migrations.AddUnrealisedForeignExchangeDecimalToTblTax do
  use Ecto.Migration

  def up do
    alter table(:tbl_tax) do
      add :Unrealised_opening_asset, :decimal
      add :Unrealised_mvt_month, :decimal
      add :Unrealised_mvt_equity, :decimal

    end
  end

  def down do
    alter table(:tbl_tax) do
      remove :Unrealised_opening_asset
      remove :Unrealised_mvt_month, :decimal
      remove :Unrealised_mvt_equity, :decimal

    end
  end
end
