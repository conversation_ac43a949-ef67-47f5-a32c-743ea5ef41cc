
defmodule MisReports.Repo.Migrations.CreateTblBusinessUnits do
  use Ecto.Migration

  def change do
    create table(:tbl_loan_business_units) do
      add :business_unit, :string
      add :facility_category, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end

    create index(:tbl_loan_business_units, [:maker_id])
    create index(:tbl_loan_business_units, [:checker_id])
  end
end
