defmodule MisReports.Repo.Migrations.CreateTblEmployeeStats do
  use Ecto.Migration

  def change do
    create table(:tbl_employee_stats) do
      add :descript, :string
      add :lusaka, :string, size: :max
      add :central, :string, size: :max
      add :eastern, :string, size: :max
      add :cb, :string, size: :max
      add :southern, :string, size: :max
      add :luapula, :string, size: :max
      add :western, :string, size: :max
      add :northern, :string, size: :max
      add :north_west, :string, size: :max
      add :muchinga, :string, size: :max
      add :status, :string
      add :maker_date, :naive_datetime
      add :checker_date, :naive_datetime
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
