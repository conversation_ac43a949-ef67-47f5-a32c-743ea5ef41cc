defmodule MisReports.Repo.Migrations.AddColumnsLoansAdvances do
  use Ecto.Migration

  def up do
    alter table(:tbl_loans_advances) do
      add :account_domicile_branch_name, :string
      add :type_of_facility_2, :string
      add :province, :string
      add :debit_book_balance_zmw_previous_month, :decimal, precision: 18, scale: 2
      add :debit_book_balance_zmw_movement, :decimal, precision: 18, scale: 2
      add :movement_status, :string
      add :arrear_buckets2_previous, :string
    end
  end

  def down do
    alter table(:tbl_loans_advances) do
      remove :account_domicile_branch_name, :string
      remove :type_of_facility_2
      remove :province
      remove :debit_book_balance_zmw_previous_month
      remove :debit_book_balance_zmw_movement
      remove :movement_status
      remove :arrear_buckets2_previous
    end
  end
end
