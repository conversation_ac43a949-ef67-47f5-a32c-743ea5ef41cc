
defmodule MisReports.Repo.Migrations.CreateTblBranch do
  use Ecto.Migration

  def change do
    create table(:tbl_branch) do
      add :type, :string
      add :code, :string
      add :number, :string
      add :name, :string
      add :town, :string
      add :province, :string
      add :email, :string
      add :phone, :string
      add :phy_addr, :string
      add :post_addr, :string
      add :manager_name, :string
      add :status, :string
      add :maker_date, :naive_datetime
      add :checker_date, :naive_datetime
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      add :last_updated_by, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end

    create unique_index(:tbl_branch, [:code])
    create unique_index(:tbl_branch, [:number])
  end
end
