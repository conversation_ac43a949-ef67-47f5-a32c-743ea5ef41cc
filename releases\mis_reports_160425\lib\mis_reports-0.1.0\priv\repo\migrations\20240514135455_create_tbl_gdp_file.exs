defmodule MisReports.Repo.Migrations.CreateTblGdpFile do
  use Ecto.Migration

  def change do
    create table(:tbl_gdp_file) do
      add :cur_month_narration, :string
      add :cur_month_stage, :string
      add :cur_month_amount, :decimal, precision: 18, scale: 2
      add :pre_month_narration, :string
      add :pre_month_stage, :string
      add :pre_month_amount, :decimal, precision: 18, scale: 2
      add :src_filename, :string
      add :date, :date
      timestamps()
    end
  end
end
