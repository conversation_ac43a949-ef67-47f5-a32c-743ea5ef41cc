defmodule MisReports.Repo.Migrations.CreateTblCmmpBranches do
  use Ecto.Migration

  def change do
    create table(:tbl_cmmp_branches) do
      add :acc_dom_branch_name, :string
      add :geo_dist, :string
      add :district, :string
      add :province, :string
      add :report_date, :date
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      add :src_file_id, :bigint

      timestamps()
    end

    create index(:tbl_cmmp_branches, [:maker_id])
    create index(:tbl_cmmp_branches, [:checker_id])
  end
end
