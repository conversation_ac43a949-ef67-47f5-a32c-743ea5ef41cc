
defmodule MisReports.Repo.Migrations.CreateTblLoanSectors do
  use Ecto.Migration

  def change do
    create table(:tbl_loan_sectors) do
      add :system_sector, :string
      add :prudential_sector, :string
      add :prudential_sub_sector, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end

    create unique_index(:tbl_loan_sectors, [:system_sector, :prudential_sector, :prudential_sub_sector])
    create index(:tbl_loan_sectors, [:maker_id])
    create index(:tbl_loan_sectors, [:checker_id])
  end
end
