defmodule MisReports.Repo.Migrations.AddMakerCheckerToTblTax do
  use Ecto.Migration

  def up do
    alter table(:tbl_tax) do
      add :maker_id, references(:tbl_users, column: :id, on_delete: :nothing)
      add :checker_id, references(:tbl_users, column: :id, on_delete: :nothing)
    end

    create index(:tbl_tax, [:maker_id])
    create index(:tbl_tax, [:checker_id])
  end

  def down do
    alter table(:tbl_tax) do
      remove :maker_id
      remove :checker_id
    end
  end

end
