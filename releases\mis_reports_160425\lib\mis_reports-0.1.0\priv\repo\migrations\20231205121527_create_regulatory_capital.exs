defmodule MisReports.Repo.Migrations.CreateRegulatoryCapital do
  use Ecto.Migration

  def change do
    create table(:tbl_regulatory_capital) do
      add :regulatory_capital, :decimal, precision: 18, scale: 2
      add :date, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
