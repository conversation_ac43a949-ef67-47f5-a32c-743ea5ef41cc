defmodule MisReports.Repo.Migrations.CreateTblSysDirectories do
  use Ecto.Migration

  def change do
    create table(:tbl_sys_directories) do
      add :param_name, :string, size: 70
      add :param_value, :string
      add :modified_by, :string, size: 150
      add :maker_id, references(:tbl_users, column: :id)
      add :last_user_id, references(:tbl_users, column: :id)

      timestamps()
    end

    create unique_index(:tbl_sys_directories, [:param_name], name: :unique_param_name)
    create index(:tbl_sys_directories, [:maker_id])
  end
end
