{application,calendar,
             [{applications,[kernel,stdlib,elixir,logger,tzdata]},
              {description,"Calendar is a datetime library for Elixir.\n\nTimezone support via its sister package `tzdata`.\n\nSafe parsing and formatting of standard formats (ISO, RFC, etc.), strftime formatting. Interoperability with erlang style\ndatetime tuples. Extendable through protocols.\n"},
              {modules,['Elixir.Calendar.AmbiguousDateTime',
                        'Elixir.Calendar.ContainsDate',
                        'Elixir.Calendar.ContainsDate.Calendar.Date',
                        'Elixir.Calendar.ContainsDate.Calendar.DateTime',
                        'Elixir.Calendar.ContainsDate.Calendar.NaiveDateTime',
                        'Elixir.Calendar.ContainsDate.Date',
                        'Elixir.Calendar.ContainsDate.DateTime',
                        'Elixir.Calendar.ContainsDate.NaiveDateTime',
                        'Elixir.Calendar.ContainsDate.Tuple',
                        'Elixir.Calendar.ContainsDateTime',
                        'Elixir.Calendar.ContainsDateTime.Calendar.DateTime',
                        'Elixir.Calendar.ContainsDateTime.DateTime',
                        'Elixir.Calendar.ContainsNaiveDateTime',
                        'Elixir.Calendar.ContainsNaiveDateTime.Calendar.DateTime',
                        'Elixir.Calendar.ContainsNaiveDateTime.DateTime',
                        'Elixir.Calendar.ContainsNaiveDateTime.NaiveDateTime',
                        'Elixir.Calendar.ContainsNaiveDateTime.Tuple',
                        'Elixir.Calendar.ContainsTime',
                        'Elixir.Calendar.ContainsTime.Calendar.DateTime',
                        'Elixir.Calendar.ContainsTime.Calendar.NaiveDateTime',
                        'Elixir.Calendar.ContainsTime.DateTime',
                        'Elixir.Calendar.ContainsTime.NaiveDateTime',
                        'Elixir.Calendar.ContainsTime.Time',
                        'Elixir.Calendar.ContainsTime.Tuple',
                        'Elixir.Calendar.Date','Elixir.Calendar.Date.Format',
                        'Elixir.Calendar.Date.Parse',
                        'Elixir.Calendar.DateTime',
                        'Elixir.Calendar.DateTime.Format',
                        'Elixir.Calendar.DateTime.Interval',
                        'Elixir.Calendar.DateTime.Parse',
                        'Elixir.Calendar.DateTime.TzPeriod',
                        'Elixir.Calendar.DefaultTranslations',
                        'Elixir.Calendar.NaiveDateTime',
                        'Elixir.Calendar.NaiveDateTime.Format',
                        'Elixir.Calendar.NaiveDateTime.Interval',
                        'Elixir.Calendar.NaiveDateTime.Parse',
                        'Elixir.Calendar.ParseUtil',
                        'Elixir.Calendar.Strftime','Elixir.Calendar.Time',
                        'Elixir.Calendar.Time.Format',
                        'Elixir.Calendar.Time.Parse',
                        'Elixir.Calendar.TimeZoneData']},
              {registered,[]},
              {vsn,"0.17.6"}]}.
