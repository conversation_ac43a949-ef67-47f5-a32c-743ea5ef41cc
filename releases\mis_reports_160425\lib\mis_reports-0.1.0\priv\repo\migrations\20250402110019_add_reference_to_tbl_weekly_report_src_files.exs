defmodule MisReports.Repo.Migrations.AddReferenceToTblWeeklyReportSrcFiles do
  use Ecto.Migration

  def up do
    alter table(:weekly_report_src_files) do
      add :reference, :string
      add :inserted_count, :integer
      add :processed_count, :integer
    end
  end

  def down do
    alter table(:weekly_report_src_files) do
      remove :reference
      remove :inserted_count
      remove :processed_count
    end
  end
end
