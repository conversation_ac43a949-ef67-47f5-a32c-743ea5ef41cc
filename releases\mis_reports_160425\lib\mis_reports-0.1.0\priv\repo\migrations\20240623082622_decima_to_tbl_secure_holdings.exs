defmodule MisReports.Repo.Migrations.DecimaToTblSecureHoldings do
  use Ecto.Migration

  def up do
    alter table(:tbl_secure_holdings) do
      add :b17, :decimal
      add :b18, :decimal
      add :b22, :decimal
      add :b23, :decimal
      add :b27, :decimal
      add :b29, :decimal
      add :b33, :decimal
      add :b34, :decimal
      add :b35, :decimal
      add :b36, :decimal
      add :b37, :decimal
      add :b38, :decimal
      add :b39, :decimal
      add :b43, :decimal
      add :b44, :decimal
      add :b45, :decimal
      add :b46, :decimal
      add :b47, :decimal
      add :b48, :decimal
      add :b49, :decimal
      add :b50, :decimal
      add :b51, :decimal
      add :b52, :decimal
      add :b53, :decimal
      add :b54, :decimal
      add :b55, :decimal
      add :b56, :decimal
      add :b57, :decimal
      add :b58, :decimal
      add :b59, :decimal
      add :c17, :decimal
      add :c18, :decimal
      add :c22, :decimal
      add :c23, :decimal
      add :c27, :decimal
      add :c28, :decimal
      add :c29, :decimal
      add :c33, :decimal
      add :c34, :decimal
      add :c35, :decimal
      add :c36, :decimal
      add :c37, :decimal
      add :c38, :decimal
      add :c39, :decimal
      add :c43, :decimal
      add :c44, :decimal
      add :c45, :decimal
      add :c46, :decimal
      add :c47, :decimal
      add :c48, :decimal
      add :c49, :decimal
      add :c50, :decimal
      add :c51, :decimal
      add :c52, :decimal
      add :c53, :decimal
      add :c54, :decimal
      add :c55, :decimal
      add :c56, :decimal
      add :c57, :decimal
      add :c58, :decimal
      add :c59, :decimal
      add :c61, :decimal
      add :d17, :decimal
      add :d18, :decimal
      add :d22, :decimal
      add :d23, :decimal
      add :d27, :decimal
      add :d28, :decimal
      add :d29, :decimal
      add :d33, :decimal
      add :d34, :decimal
      add :d35, :decimal
      add :d36, :decimal
      add :d37, :decimal
      add :d38, :decimal
      add :d39, :decimal
      add :d43, :decimal
      add :d44, :decimal
      add :d45, :decimal
      add :d46, :decimal
      add :d47, :decimal
      add :d48, :decimal
      add :d49, :decimal
      add :d50, :decimal
      add :d51, :decimal
      add :d52, :decimal
      add :d53, :decimal
      add :d54, :decimal
      add :d55, :decimal
      add :d56, :decimal
      add :d57, :decimal
      add :d58, :decimal
      add :d59, :decimal
      add :d61, :decimal
      add :e17, :decimal
      add :e18, :decimal
      add :e22, :decimal
      add :e23, :decimal
      add :e27, :decimal
      add :e29, :decimal
      add :e33, :decimal
      add :e34, :decimal
      add :e35, :decimal
      add :e36, :decimal
      add :e37, :decimal
      add :e38, :decimal
      add :e39, :decimal
      add :e43, :decimal
      add :e44, :decimal
      add :e45, :decimal
      add :e46, :decimal
      add :e47, :decimal
      add :e48, :decimal
      add :e49, :decimal
      add :e50, :decimal
      add :e51, :decimal
      add :e52, :decimal
      add :e53, :decimal
      add :e54, :decimal
      add :e55, :decimal
      add :e56, :decimal
      add :e57, :decimal
      add :e58, :decimal
      add :e59, :decimal
      add :e61, :decimal
      add :f17, :decimal
      add :f18, :decimal
      add :f22, :decimal
      add :f23, :decimal
      add :f27, :decimal
      add :f28, :decimal
      add :f29, :decimal
      add :f33, :decimal
      add :f34, :decimal
      add :f35, :decimal
      add :f36, :decimal
      add :f37, :decimal
      add :f38, :decimal
      add :f39, :decimal
      add :f43, :decimal
      add :f44, :decimal
      add :f45, :decimal
      add :f46, :decimal
      add :f47, :decimal
      add :f48, :decimal
      add :f49, :decimal
      add :f50, :decimal
      add :f51, :decimal
      add :f52, :decimal
      add :f53, :decimal
      add :f54, :decimal
      add :f55, :decimal
      add :f56, :decimal
      add :f57, :decimal
      add :f58, :decimal
      add :f61, :decimal
    end
  end

  def down do
      alter table(:tbl_secure_holdings) do
        remove :b17
        remove :b18
        remove :b22
        remove :b23
        remove :b27
        remove :b29
        remove :b33
        remove :b34
        remove :b35
        remove :b36
        remove :b37
        remove :b38
        remove :b39
        remove :b43
        remove :b44
        remove :b45
        remove :b46
        remove :b47
        remove :b48
        remove :b49
        remove :b50
        remove :b51
        remove :b52
        remove :b53
        remove :b54
        remove :b55
        remove :b56
        remove :b57
        remove :b58
        remove :b59
        remove :c17
        remove :c18
        remove :c22
        remove :c23
        remove :c27
        remove :c28
        remove :c29
        remove :c33
        remove :c34
        remove :c35
        remove :c36
        remove :c37
        remove :c38
        remove :c39
        remove :c43
        remove :c44
        remove :c45
        remove :c46
        remove :c47
        remove :c48
        remove :c49
        remove :c50
        remove :c51
        remove :c52
        remove :c53
        remove :c54
        remove :c55
        remove :c56
        remove :c57
        remove :c58
        remove :c59
        remove :c61
        remove :d17
        remove :d18
        remove :d22
        remove :d23
        remove :d27
        remove :d28
        remove :d29
        remove :d33
        remove :d34
        remove :d35
        remove :d36
        remove :d37
        remove :d38
        remove :d39
        remove :d43
        remove :d44
        remove :d45
        remove :d46
        remove :d47
        remove :d48
        remove :d49
        remove :d50
        remove :d51
        remove :d52
        remove :d53
        remove :d54
        remove :d55
        remove :d56
        remove :d57
        remove :d58
        remove :d59
        remove :d61
        remove :e17
        remove :e18
        remove :e22
        remove :e23
        remove :e27
        remove :e29
        remove :e33
        remove :e34
        remove :e35
        remove :e36
        remove :e37
        remove :e38
        remove :e39
        remove :e43
        remove :e44
        remove :e45
        remove :e46
        remove :e47
        remove :e48
        remove :e49
        remove :e50
        remove :e51
        remove :e52
        remove :e53
        remove :e54
        remove :e55
        remove :e56
        remove :e57
        remove :e58
        remove :e59
        remove :e61
        remove :f17
        remove :f18
        remove :f22
        remove :f23
        remove :f27
        remove :f28
        remove :f29
        remove :f33
        remove :f34
        remove :f35
        remove :f36
        remove :f37
        remove :f38
        remove :f39
        remove :f43
        remove :f44
        remove :f45
        remove :f46
        remove :f47
        remove :f48
        remove :f49
        remove :f50
        remove :f51
        remove :f52
        remove :f53
        remove :f54
        remove :f55
        remove :f56
        remove :f57
        remove :f58
        remove :f61
    end
  end
end
