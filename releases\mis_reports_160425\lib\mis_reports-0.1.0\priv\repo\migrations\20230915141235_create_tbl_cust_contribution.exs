defmodule MisReports.Repo.Migrations.CreateTblCustContribution do
  use Ecto.Migration

  def change do
    create table(:tbl_cust_contribution) do
      add :year_period, :string
      add :Month_period, :string
      add :source_system, :string
      add :branch_number, :string
      add :branch_name, :string
      add :business_unit, :string
      add :product_house, :string
      add :customer_segment, :string
      add :subsegment_code_source, :string
      add :subsegment_descr_source, :string
      add :sector_code_and_description, :string
      add :customer_number_global_cif, :string
      add :customer_number_local_cif, :string
      add :return_classification_code, :string
      add :reserve_bank_code, :string
      add :customer_name, :string
      add :form_50_code, :string
      add :industry_description, :string
      add :customer_relationship_manager, :string
      add :custrelationship_start_date,:string
      add :product_code_source, :string
      add :product_description_source, :string
      add :product_category_sap, :string
      add :portfolio_code, :string
      add :account_number, :string
      add :account_name, :string
      add :account_relationship_manager, :string
      add :account_open_date,:string
      add :account_closed_date,:string
      add :account_maturity_date,:string
      add :account_status, :string
      add :off_balance_sheet_flag, :string
      add :lockup_account_flag, :string
      add :currency_code, :string
      add :currency_category, :string
      add :actual_balance, :decimal, precision: 18, scale: 2
      add :actual_debit_balance, :decimal, precision: 18, scale: 2
      add :actual_debit_balance_movement, :decimal, precision: 18, scale: 2
      add :actual_credit_balance, :decimal, precision: 18, scale: 2
      add :actual_credit_balance_movement, :decimal, precision: 18, scale: 2
      add :total_credit_limits, :decimal, precision: 18, scale: 2
      add :average_debit_balance_mtd, :decimal, precision: 18, scale: 2
      add :average_credit_balance_mtd, :decimal, precision: 18, scale: 2
      add :average_debit_balance_ytd, :decimal, precision: 18, scale: 2
      add :average_credit_balance_ytd, :decimal, precision: 18, scale: 2
      add :effective_debit_rate, :decimal, precision: 18, scale: 2
      add :effective_credit_rate, :decimal, precision: 18, scale: 2
      add :applied_funding_rate, :decimal, precision: 18, scale: 2
      add :interest_payable_amount, :decimal, precision: 18, scale: 2
      add :interest_receivable_amount, :decimal, precision: 18, scale: 2
      add :interest_in_suspense_amount, :decimal, precision: 18, scale: 2
      add :total_impairment_amount, :decimal, precision: 18, scale: 2
      add :debit_turnover, :decimal, precision: 18, scale: 2
      add :credit_turnover, :decimal, precision: 18, scale: 2
      add :net_turnover, :decimal, precision: 18, scale: 2
      add :number_of_credit_transactions, :decimal, precision: 18, scale: 2
      add :number_of_debit_transactions, :decimal, precision: 18, scale: 2
      add :interest_income_mtd, :decimal, precision: 18, scale: 2
      add :interest_expense_mtd, :decimal, precision: 18, scale: 2
      add :interest_income_ytd, :decimal, precision: 18, scale: 2
      add :interest_expense_ytd, :decimal, precision: 18, scale: 2
      add :funding_cost, :decimal, precision: 18, scale: 2
      add :funding_benefit, :decimal, precision: 18, scale: 2
      add :fees_and_commission_mtd, :string
      add :fees_and_commission_ytd, :decimal, precision: 18, scale: 2
      add :monthly_impairment_amount, :decimal, precision: 18, scale: 2
      add :source_system_ledger_glsh, :string
      add :balance_sap_ledger, :string
      add :interest_receivable_sap_ledger, :string
      add :interest_payable_sap_ledger, :string
      add :impairments_bs_sap_ledger, :string
      add :interest_income_sap_ledger, :string
      add :interest_expense_sap_ledger, :string
      add :impairments_is_sap_ledger, :string
      add :fmi_profit_center_number, :string
      add :fmi_profit_center_name, :string
      add :src_filename, :string

      timestamps()
    end
  end
end
