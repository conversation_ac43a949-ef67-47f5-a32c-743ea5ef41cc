defmodule MisReports.Repo.Migrations.CreateTblCreditCards do
  use Ecto.Migration

  def change do
    create table(:tbl_credit_cards) do
      add :account_no, :string
      add :account_name, :string
      add :days_past_due, :integer
      add :pl_npl, :string
      add :pl_wo, :integer
      add :stage, :string
      add :limit, :decimal, precision: 18, scale: 2
      add :utilization, :decimal, precision: 18, scale: 2
      add :date, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
