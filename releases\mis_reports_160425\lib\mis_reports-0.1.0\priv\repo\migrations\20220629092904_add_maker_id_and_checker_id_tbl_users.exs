defmodule MisReports.Repo.Migrations.AddMakerIdAndCheckerIdTblUsers do
  use Ecto.Migration

  def up do
    alter table(:tbl_users) do
      add :maker_id, references(:tbl_users, column: :id, on_delete: :nothing)
      add :checker_id, references(:tbl_users, column: :id, on_delete: :nothing)
    end
  end

  def down do
    drop constraint(:videos, "tbl_users_maker_id_fkey")
    drop constraint(:videos, "tbl_users_checker_id_fkey")

    alter table(:tbl_users) do
      remove :maker_id
      remove :checker_id
    end
  end
end
