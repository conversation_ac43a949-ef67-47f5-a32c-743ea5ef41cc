defmodule MisReports.Repo.Migrations.CreateTblCustSegmentation do
  use Ecto.Migration

  def change do
    create table(:tbl_cust_segmentation) do
      add :country, :string
      add :legal_entity, :string
      add :company_code, :string
      add :business_unit_grp, :string
      add :year, :string
      add :month, :string
      add :sap_gl_acc_no, :string
      add :sap_gl_acc_name, :string
      add :sap_prft_ctr_no, :string
      add :sap_prft_ctr_name, :string
      add :ccy_code, :string
      add :day, :string
      add :ccy_cat, :string
      add :acc_bal_in_lcy, :decimal, precision: 18, scale: 2
      add :acc_bal_in_ccy, :decimal, precision: 18, scale: 2
      add :avg_bal_in_lcy, :decimal, precision: 18, scale: 2
      add :avg_bal_in_ccy, :decimal, precision: 18, scale: 2
      add :daily_mvmt_in_ccy, :decimal, precision: 18, scale: 2
      add :daily_mvmt_in_lcy, :decimal, precision: 18, scale: 2
      add :mdt_mvmt_in_ccy, :decimal, precision: 18, scale: 2
      add :mdt_mvmt_in_lcy, :decimal, precision: 18, scale: 2

      timestamps()
    end
  end
end
