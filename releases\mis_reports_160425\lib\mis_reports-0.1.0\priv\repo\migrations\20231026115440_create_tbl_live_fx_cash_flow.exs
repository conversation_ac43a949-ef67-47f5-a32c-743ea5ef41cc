defmodule MisReports.Repo.Migrations.CreateTblLiveFxCashFlow do
  use Ecto.Migration

  def change do
    create table(:tbl_live_fx_cash_flow) do
      add :report_date, :date
      add :country, :string
      add :product_type, :string
      add :trade_id, :string
      add :book, :string
      add :trade_date, :date
      add :cash_flow_date, :date
      add :counter_party, :string
      add :near_rate, :decimal
      add :far_rate, :decimal
      add :fx_swap_leg, :string
      add :pay_ccy, :string
      add :pay_amount, :decimal
      add :receive_ccy, :string
      add :receive_amount, :decimal
      add :mtm_cur, :string
      add :mtm, :decimal
      add :mtm_base, :decimal
      add :near_leg_dt, :date

      timestamps()
    end
  end
end
