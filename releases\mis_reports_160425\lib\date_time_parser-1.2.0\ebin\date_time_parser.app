{application,date_time_parser,
             [{compile_env,[{date_time_parser,[include_zones_from],error}]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"Parse a string into DateTime, NaiveDateTime, Time, or Date struct."},
              {modules,['Elixir.DateTimeParser',
                        'Elixir.DateTimeParser.Combinators',
                        'Elixir.DateTimeParser.Formatters',
                        'Elixir.DateTimeParser.ParseError',
                        'Elixir.DateTimeParser.Parser',
                        'Elixir.DateTimeParser.Parser.Date',
                        'Elixir.DateTimeParser.Parser.DateTime',
                        'Elixir.DateTimeParser.Parser.DateTimeUS',
                        'Elixir.DateTimeParser.Parser.DateUS',
                        'Elixir.DateTimeParser.Parser.Epoch',
                        'Elixir.DateTimeParser.Parser.Serial',
                        'Elixir.DateTimeParser.Parser.Time',
                        'Elixir.DateTimeParser.Parser.Tokenizer',
                        'Elixir.DateTimeParser.TimezoneAbbreviations',
                        'Elixir.DateTimeParser.TimezoneParser',
                        'Elixir.DateTimeParser.TimezoneParser.Rule',
                        'Elixir.DateTimeParser.TimezoneParser.Zone',
                        'Elixir.Inspect.DateTimeParser.TimezoneParser.Rule',
                        'Elixir.Inspect.DateTimeParser.TimezoneParser.Zone']},
              {registered,[]},
              {vsn,"1.2.0"}]}.
