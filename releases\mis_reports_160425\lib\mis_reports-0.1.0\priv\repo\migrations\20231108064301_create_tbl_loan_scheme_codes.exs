
defmodule MisReports.Repo.Migrations.CreateTblLoanSchemeCodes do
  use Ecto.Migration

  def change do
    create table(:tbl_loan_scheme_codes) do
      add :scheme_code, :string
      add :facility_type, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end

    create index(:tbl_loan_scheme_codes, [:maker_id])
    create index(:tbl_loan_scheme_codes, [:checker_id])
  end
end
