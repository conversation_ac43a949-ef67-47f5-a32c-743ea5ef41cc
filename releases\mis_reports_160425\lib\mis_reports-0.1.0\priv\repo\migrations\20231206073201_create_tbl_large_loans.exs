defmodule MisReports.Repo.Migrations.CreateTblLargeLoans do
  use Ecto.Migration

  def change do
    create table(:tbl_large_loans) do
      add :cif, :string
      add :group_no, :string
      add :month, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
