defmodule MisReports.Repo.Migrations.AddSrcFileIdTblTrialBalance do
  use Ecto.Migration

  def up do
    alter table(:tbl_trial_balance_entries) do
      add :src_file_id, references(:tbl_trial_bal_src_files, column: :id, on_delete: :nothing)
    end
  end


  def down do
    drop constraint(:tbl_trial_balance_entries, "tbl_trial_balance_entries_src_file_id_fkey")
    alter table(:tbl_trial_balance_entries) do
      remove  :src_file_id
    end
  end
end
