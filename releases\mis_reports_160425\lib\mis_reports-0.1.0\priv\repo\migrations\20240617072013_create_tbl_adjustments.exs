defmodule MisReports.Repo.Migrations.CreateTblAdjustments do
  use Ecto.Migration

  def change do
    create table(:tbl_adjustments) do
      add :demand, :decimal, precision: 18, scale: 2
      add :savings, :decimal, precision: 18, scale: 2
      add :time, :decimal, precision: 18, scale: 2
      add :status, :string
      add :report_date, :date
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      add :maker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:tbl_adjustments, [:checker_id])
    create index(:tbl_adjustments, [:maker_id])
  end
end
