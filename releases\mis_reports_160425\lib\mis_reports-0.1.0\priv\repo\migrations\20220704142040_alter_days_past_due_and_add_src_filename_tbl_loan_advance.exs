defmodule MisReports.Repo.Migrations.AlterDaysPastDueAndAddSrcFilenameTblLoanAdvance do
  use Ecto.Migration

  def up do
    alter table(:tbl_loans_advances) do
      modify :days_past_due, :string
      add :src_filename, :string
      add :maker_dt, :naive_datetime
      add :checker_dt, :naive_datetime
    end
  end

  def down do
    alter table(:tbl_loans_advances) do
      modify :days_past_due, :integer
      remove :src_filename
      remove :maker_dt
      remove :checker_dt
    end
  end
end
