defmodule MisReports.Repo.Migrations.CreateWkltaskHeadersHistory do
  use Ecto.Migration

  def change do
    create table(:wkltask_headers_history) do
      add :header_id_history, :integer
      add :header_id, :integer
      add :ack_id, :integer
      add :process_id, :integer
      add :start_date, :date
      add :end_date, :date
      add :previous_ack_id, :integer
      add :started_by, :string

      timestamps()
    end
  end
end
