defmodule MisReports.Repo.Migrations.CreateTblGlMappings do
  use Ecto.Migration

  def change do
    create table(:tbl_gl_mappings) do
      add :report_type, :string
      add :schedule, :string
      add :gl_names, :map
      add :col_index, :string
      add :auth_status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create unique_index(:tbl_gl_mappings, [:report_type, :schedule], name: :unique_gl_mapping)
  end
end
