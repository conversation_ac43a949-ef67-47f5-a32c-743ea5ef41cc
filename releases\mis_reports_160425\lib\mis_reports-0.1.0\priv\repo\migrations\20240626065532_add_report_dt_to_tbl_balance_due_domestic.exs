defmodule MisReports.Repo.Migrations.AddReportDtToTblBalanceDueDomestic do
  use Ecto.Migration

  def up do
    alter table(:tbl_balance_due_domestic) do
      remove_if_exists(:maker_id, :integer)
      remove_if_exists(:checker_id, :integer)
      add :maker_id, references(:tbl_users, column: :id)
      add :checker_id, references(:tbl_users, column: :id)
      add :report_dt, :date

    end

    create index(:tbl_balance_due_domestic, [:maker_id])
    create index(:tbl_balance_due_domestic, [:checker_id])
  end

  def down do
    alter table(:tbl_balance_due_domestic) do
      remove_if_exists(:maker_id, references(:tbl_users, column: :id))
      remove_if_exists(:checker_id, references(:tbl_users, column: :id))
      remove :report_dt
      add_if_not_exists(:maker_id, :integer)
      add_if_not_exists(:checker_id, :integer)

    end
  end
end
