defmodule MisReports.Repo.Migrations.CreateTblObddr do
  use Ecto.Migration

  def change do
    create table(:tbl_obddr) do
      add :finnacle_account_code, :string
      add :customer_name, :string
      add :business_segment, :string
      add :business_segment2, :string
      add :currency, :string
      add :risk_grade, :string
      add :account_code, :string
      add :branch, :string
      add :security_status, :string
      add :scheme_code, :string
      add :product_description, :string
      add :industry_sector, :string
      add :lock_up_date, :string
      add :days_in_lu, :string
      add :mortgage_security_value_actual, :decimal, precision: 18, scale: 2
      add :mortgage_security_value_discounted, :decimal, precision: 18, scale: 2
      add :debenture_security_value_actual, :decimal, precision: 18, scale: 2
      add :debenture_security_value_discounted, :decimal, precision: 18, scale: 2
      add :interest_in_suspense, :decimal, precision: 18, scale: 2
      add :debit_interest, :decimal, precision: 18, scale: 2
      add :write_off_ytd, :decimal, precision: 18, scale: 2
      add :net_debit_balance, :decimal, precision: 18, scale: 2
      add :provisions_held, :decimal, precision: 18, scale: 2
      add :expected_provisions_at_365, :decimal, precision: 18, scale: 2
      add :provision_adequacy, :string
      add :variance_interest_suspense, :decimal, precision: 18, scale: 2
      add :variance_provision, :decimal, precision: 18, scale: 2
      add :additional_provisions, :decimal, precision: 18, scale: 2
      add :provision_write_backs, :decimal, precision: 18, scale: 2
      add :new_old, :string
      add :status, :string
      add :excess_provisions, :string
      add :ifrs_test, :string
      add :lock_up_indicator, :string
      add :interest_in_suspense_zmw, :decimal, precision: 18, scale: 2
      add :debit_interest_zmw, :decimal, precision: 18, scale: 2
      add :write_off_ytd_zmw, :decimal, precision: 18, scale: 2
      add :net_debit_balance_zmw, :decimal, precision: 18, scale: 2
      add :provisions_held_zmw, :decimal, precision: 18, scale: 2
      add :expected_provisions_at_365_zmw, :decimal, precision: 18, scale: 2
      add :additional_provisions_zmw, :decimal, precision: 18, scale: 2
      add :provision_write_backs_zmw, :decimal, precision: 18, scale: 2
      add :month, :date
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
