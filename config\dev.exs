import Config

# Configure your database
config :mis_reports, MisReports.Repo,
  adapter: Ecto.Adapters.Tds,
  database: "mis_reports",
   username: "sa",
  password: "mwan<PERSON><PERSON>2050",
  # hostname: "************",
  hostname: "************",
  # hostname: "************",
  # hostname: "***********",
  # hostname: "**********",
  # password: "1234",
  # hostname: "************",

  pool_size: 100,
  timeout: 7_100_000,
  pool_timeout: 7_100_000,
  # Handles connection queueing
  queue_target: 5_000,
  # Controls queue check frequency
  queue_interval: 5_000,
  # Closes idle connections gracefully
  idle_timeout: 30_000,
  backoff: [
    # Changed from :exponential to :rand_exp
    type: :rand_exp,
    # 1 second minimum
    min: 1_000,
    # 30 seconds maximum
    max: 30_000
  ]

# For development, we disable any cache and enabletbl_reminders
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we use it
# with esbuild to bundle .js and .css sources.
config :mis_reports, MisReportsWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  # http: [ip: {127, 0, 0, 1}, port: 4000],
  # http: [port: 4000],
  http: [port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "BfMknUg35zxAKkWjL7J3oH5FOFrHjcqLqhLKNzED+5Bfgz6k70o9bNJ/zHurvoML",
  watchers: [
    # Start the esbuild watcher by calling Esbuild.install_and_run(:default, args)
    # esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:default, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Note that this task requires Erlang/OTP 20 or later.
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :mis_reports, MisReportsWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/.*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/mis_reports_web/(live|views)/.*(ex)$",
      ~r"lib/mis_reports_web/templates/.*(eex)$"
    ]
  ]

# Do not include metadata nor timestamps in development logs
config :logger, :console, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime
