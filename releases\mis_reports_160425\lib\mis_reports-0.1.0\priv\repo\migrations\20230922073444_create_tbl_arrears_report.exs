defmodule MisReports.Repo.Migrations.CreateTblArrearsReport do
  use Ecto.Migration

  def change do
    create table(:tbl_arrears_report) do
      add :account_no, :string
      add :customer_number, :string
      add :old_account_number, :string
      add :customer_name, :string
      add :account_currency, :string
      add :loan_amount, :decimal, precision: 18, scale: 2
      add :account_balance, :decimal, precision: 18, scale: 2
      add :account_balance_lcy, :decimal, precision: 18, scale: 2
      add :deliquent_amount, :decimal, precision: 18, scale: 2
      add :exchange_rate, :decimal, precision: 18, scale: 2
      add :scheme_code_desc, :string
      add :scheme_code, :string
      add :segment_desc, :string
      add :branch_name, :string
      add :branch_code, :string
      add :portfolio_code, :string
      add :business_unit, :string
      add :relationship_manager_name, :string
      add :relationship_manager_id, :string
      add :first_payment_default, :date
      add :last_payment_date, :date
      add :next_payment_date, :date
      add :last_payment_amount, :decimal, precision: 18, scale: 2
      add :next_payment_amount, :decimal, precision: 18, scale: 2
      add :loan_term, :integer
      add :payments_remaining, :integer
      add :loan_value_date, :date
      add :loan_maturity_date, :date
      add :interest_rate, :decimal, precision: 18, scale: 2
      add :settlement_account, :string
      add :period, :string
      add :address, :string
      add :email, :string
      add :phone, :string
      add :days_past_due, :integer
      add :arrear_bucket, :string
      add :projected_npl, :string
      add :model_npl_status, :string
      add :employer, :string
      add :extract_date, :naive_datetime
      add :report_date, :date
      add :month, :string
      add :year, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
