defmodule MisReports.Repo.Migrations.AlterLoanMappingTables do
  use Ecto.Migration

  def up do
    alter table(:tbl_loan_business_units) do
      add :ccr_business_unit, :string
    end
    alter table(:tbl_loan_sectors) do
      add :ccr_sector, :string
    end
    alter table(:tbl_loan_scheme_codes) do
      add :scheme_type, :string
      add :scheme_type_description, :string
      add :scheme_code_description, :string
    end
  end

  def down do
    alter table(:tbl_loan_business_units) do
      remove :ccr_business_unit
    end
    alter table(:tbl_loan_sectors) do
      remove :ccr_sector
    end
    alter table(:tbl_loan_scheme_codes) do
      remove :scheme_type
      remove :scheme_type_description
      remove :scheme_code_description
    end
  end
end
