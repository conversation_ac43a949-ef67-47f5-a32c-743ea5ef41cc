defmodule MisReports.Repo.Migrations.CreateTblDaysPastDue do
  use Ecto.Migration

  def change do
    create table(:tbl_days_past_due) do
      add :account_no, :string
      add :account_name, :string
      add :customer_no, :string
      add :days_past_due, :integer
      add :date, :date
      add :src_filename, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
