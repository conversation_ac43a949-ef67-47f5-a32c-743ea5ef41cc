defmodule MisReports.Repo.Migrations.AddOpenBalanceDecimalIncomeTaxDecimalTaxEffectNonDecimalTaxEffectDeductibleDecimalOtherAdjustmentsDecimalTaxPaymentsDecimalUnrealisedForeignExchangeDecimalToTblTax do
  use Ecto.Migration

  def up do
    alter table(:tbl_tax) do
      add :open_balance, :decimal
      add :income_tax, :decimal
      add :tax_effect_non, :decimal
      add :tax_effect_deductible, :decimal
      add :other_adjustments, :decimal
      add :tax_payments, :decimal
      add :unrealised_foreign_exchange, :decimal
    end
  end

  def down do
    alter table(:tbl_tax) do
      remove :open_balance
      remove :income_tax
      remove :tax_effect_non
      remove :tax_effect_deductible
      remove :other_adjustments
      remove :tax_payments
      remove :unrealised_foreign_exchange

    end
  end
end
