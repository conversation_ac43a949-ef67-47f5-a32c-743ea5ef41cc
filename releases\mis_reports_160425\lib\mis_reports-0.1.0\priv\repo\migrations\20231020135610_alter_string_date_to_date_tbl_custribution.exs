defmodule MisReports.Repo.Migrations.AlterStringDateToDateTblCustribution do
  use Ecto.Migration

  def up do
    alter table(:tbl_cust_contribution) do
      remove :account_maturity_date
      add :account_maturity_date, :date
      remove :account_closed_date
      add :account_closed_date, :date
      remove :account_open_date
      add :account_open_date, :date
    end
  end

  def down do
    alter table(:tbl_cust_contribution) do
      remove :account_maturity_date
      add :account_maturity_date, :string
      remove :account_closed_date
      add :account_closed_date, :string
      remove :account_open_date
      add :account_open_date, :string
    end
  end
end
