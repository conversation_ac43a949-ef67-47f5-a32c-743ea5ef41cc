defmodule MisReports.Repo.Migrations.CreateBankAccounts do
  use Ecto.Migration

  def change do
    create table(:bank_accounts) do
      add :acc_name, :string
      add :acc_no, :string
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:bank_accounts, [:maker_id])
    create index(:bank_accounts, [:checker_id])
  end
end
