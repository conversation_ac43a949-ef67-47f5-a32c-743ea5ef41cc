defmodule MisReports.Repo.Migrations.CreateTrialBalMapping do
  use Ecto.Migration

  def change do
    # create table(:trial_bal_mapping) do
    #   add :schedule_name, :string, size: 150
    #   add :schedule_line_index, :integer
    #   add :tb_line_index, :integer

    #   timestamps()
    end

  #   create unique_index(
  #            :trial_bal_mapping,
  #            [:schedule_line_index, :tb_line_index, :schedule_name],
  #            name: :unique_schedule_mapping
  #          )
  # end
end
