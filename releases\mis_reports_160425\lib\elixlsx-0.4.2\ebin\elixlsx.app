{application,elixlsx,
             [{applications,[kernel,stdlib,elixir]},
              {description,"a writer for XLSX spreadsheet files"},
              {modules,['Elixir.Elixlsx','Elixir.Elixlsx.Color',
                        'Elixir.Elixlsx.Compiler',
                        'Elixir.Elixlsx.Compiler.BorderStyleDB',
                        'Elixir.Elixlsx.Compiler.CellStyleDB',
                        'Elixir.Elixlsx.Compiler.DBUtil',
                        'Elixir.Elixlsx.Compiler.FillDB',
                        'Elixir.Elixlsx.Compiler.FontDB',
                        'Elixir.Elixlsx.Compiler.NumFmtDB',
                        'Elixir.Elixlsx.Compiler.SheetCompInfo',
                        'Elixir.Elixlsx.Compiler.StringDB',
                        'Elixir.Elixlsx.Compiler.WorkbookCompInfo',
                        'Elixir.Elixlsx.Sheet','Elixir.Elixlsx.Style.Border',
                        'Elixir.Elixlsx.Style.BorderStyle',
                        'Elixir.Elixlsx.Style.CellStyle',
                        'Elixir.Elixlsx.Style.Fill',
                        'Elixir.Elixlsx.Style.Font',
                        'Elixir.Elixlsx.Style.NumFmt','Elixir.Elixlsx.Util',
                        'Elixir.Elixlsx.Workbook','Elixir.Elixlsx.Writer',
                        'Elixir.Elixlsx.XMLTemplates','Elixir.XML']},
              {registered,[]},
              {vsn,"0.4.2"}]}.
