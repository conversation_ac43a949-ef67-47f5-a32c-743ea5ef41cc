defmodule MisReports.Repo.Migrations.CreateTblDebtorsBookAnalysis do
  use Ecto.Migration

  def change do
    create table(:tbl_debtors_book_analysis) do
      add :type, :string
      add :allowances_for_losses, :map
      add :num_of_acc_with_allowances, :map
      add :status, :string
      add :report_date, :date
      add :reference, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
