defmodule MisReports.Repo.Migrations.AddScheduleContentTblBsaMaster do
  use Ecto.Migration

  def up do
    alter table(:tbl_bsa_master) do
      add :schedule, :string
      add :content, :text
      remove :response
      remove :request
    end
  end

  def down do
    alter table(:tbl_bsa_master) do
      remove :schedule
      remove :content
      add :response, :text
      add :request, :text
    end
  end
end
