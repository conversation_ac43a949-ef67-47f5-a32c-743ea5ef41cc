defmodule MisReports.Repo.Migrations.CreateTblSecureHoldings do
  use Ecto.Migration

  def change do
    create table(:tbl_secure_holdings) do
      add :cost_treasury_bills, :decimal
      add :cost_govern_bonds, :decimal
      add :cost_all_other, :decimal
      add :amortised_treasury_bills, :decimal
      add :amortised_govern_bonds, :decimal
      add :fair_treasury_bills, :decimal
      add :fair_govern_bonds, :decimal
      add :fair_value_bonds, :decimal
      add :report_date, :date
      add :status, :string

      timestamps()
    end
  end
end
