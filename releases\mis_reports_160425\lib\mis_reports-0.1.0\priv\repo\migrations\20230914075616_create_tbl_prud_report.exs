defmodule MisReports.Repo.Migrations.CreateTblPrudReport do
  use Ecto.Migration

  def change do
    create table(:tbl_prud_report) do
      add :ref, :string
      add :type, :string
      add :month, :string
      add :year, :string
      add :start_date, :date
      add :end_date, :date
      add :status, :string
      add :descript, :string
      add :maker_comment, :string
      add :checker_comment, :string
      add :maker_date, :naive_datetime
      add :checker_date, :naive_datetime
      add :income_stmt, :string, size: :max
      add :bal_sheet, :string, size: :max
      add :schedule_01c, :string, size: :max
      add :schedule_01d, :string, size: :max
      add :schedule_01e, :string, size: :max
      add :schedule_01f, :string, size: :max
      add :schedule_02a1, :string, size: :max
      add :schedule_02c, :string, size: :max
      add :schedule_02d, :string, size: :max
      add :schedule_02e, :string, size: :max
      add :schedule_02f, :string, size: :max
      add :schedule_02g, :string, size: :max
      add :schedule_02h, :string, size: :max
      add :schedule_02j, :string, size: :max
      add :schedule_03a, :string, size: :max
      add :schedule_03b, :string, size: :max
      add :schedule_04b, :string, size: :max
      add :schedule_04c, :string, size: :max
      add :schedule_04d, :string, size: :max
      add :schedule_05b, :string, size: :max
      add :schedule_05c, :string, size: :max
      add :schedule_05d, :string, size: :max
      add :schedule_06a, :string, size: :max
      add :schedule_06b, :string, size: :max
      add :schedule_07a, :string, size: :max
      add :schedule_08a, :string, size: :max
      add :schedule_08b, :string, size: :max
      add :schedule_09a, :string, size: :max
      add :schedule_10c, :string, size: :max
      add :schedule_11d, :string, size: :max
      add :schedule_11e, :string, size: :max
      add :schedule_11f, :string, size: :max
      add :schedule_11g, :string, size: :max
      add :schedule_11h, :string, size: :max
      add :schedule_11j, :string, size: :max
      add :schedule_11k, :string, size: :max
      add :schedule_12, :string, size: :max
      add :schedule_13, :string, size: :max
      add :schedule_14, :string, size: :max
      add :schedule_15, :string, size: :max
      add :schedule_17a, :string, size: :max
      add :schedule_17b, :string, size: :max
      add :schedule_17c, :string, size: :max
      add :schedule_17e, :string, size: :max
      add :schedule_18a, :string, size: :max
      add :schedule_18b, :string, size: :max
      add :schedule_18c, :string, size: :max
      add :schedule_18d, :string, size: :max
      add :schedule_19, :string, size: :max
      add :schedule_20a, :string, size: :max
      add :schedule_21b, :string, size: :max
      add :schedule_21c, :string, size: :max
      add :schedule_22a, :string, size: :max
      add :schedule_22b, :string, size: :max
      add :schedule_23a, :string, size: :max
      add :schedule_23b, :string, size: :max
      add :schedule_24, :string, size: :max
      add :schedule_25, :string, size: :max
      add :schedule_26, :string, size: :max
      add :schedule_27, :string, size: :max
      add :schedule_27a, :string, size: :max
      add :schedule_28a, :string, size: :max
      add :schedule_28b, :string, size: :max
      add :schedule_29a, :string, size: :max
      add :schedule_30b, :string, size: :max
      add :schedule_30b1, :string, size: :max
      add :schedule_30c, :string, size: :max
      add :schedule_30c1, :string, size: :max
      add :schedule_30d, :string, size: :max
      add :schedule_31c, :string, size: :max
      add :schedule_31d, :string, size: :max
      add :schedule_31e, :string, size: :max
      add :schedule_31f, :string, size: :max
      add :schedule_32a, :string, size: :max
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
