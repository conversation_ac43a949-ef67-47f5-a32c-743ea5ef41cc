defmodule MisReports.Repo.Migrations.CreateTblTrialBalance do
  use Ecto.Migration

  def change do
    create table(:tbl_trial_balance_entries) do
      add :gl_no, :string
      add :gl_desc, :string
      add :actual_this_year, :decimal, precision: 18, scale: 2
      add :actual_last_year, :decimal, precision: 18, scale: 2
      add :variance, :decimal, precision: 18, scale: 2
      add :date, :date
      # add :src_file_id, references(:tbl_doc_storage, on_delete: :nothing)

      timestamps()
    end

    # create index(:tbl_trial_balance, [:src_file_id])
  end
end
