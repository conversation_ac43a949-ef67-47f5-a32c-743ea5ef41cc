defmodule MisReports.Repo.Migrations.CreateTblReliefList do
  use Ecto.Migration

  def change do
    create table(:tbl_relief_list) do
      add :cif, :string
      add :account_no, :string
      add :customer_name, :string
      add :amount, :decimal, precision: 18, scale: 2
      add :currency, :string
      add :sector, :string
      add :amount_balance, :decimal, precision: 18, scale: 2
      add :classification, :string
      add :date, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      timestamps()
    end
  end
end
