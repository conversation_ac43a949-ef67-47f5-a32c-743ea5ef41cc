defmodule MisReports.Repo.Migrations.CreateTblCmmpProduct do
  use Ecto.Migration

  def change do
    create table(:tbl_cmmp_product) do
      add :prod_desc_src, :string
      add :type_2, :string
      add :type_1, :string
      add :debtors_book_analysis, :string
      add :report_date, :date
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
