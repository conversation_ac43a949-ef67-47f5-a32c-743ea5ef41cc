defmodule MisReports.Repo.Migrations.CreateTblRepossessedProperties do
  use Ecto.Migration

  def change do
    create table(:tbl_repossessed_properties) do
      add :name_of_borrower, :string
      add :nature_of_property_rep, :string
      add :location_of_the_property, :string
      add :date_rep, :date
      add :type_of_security_held, :string
      add :owner_of_the_property, :string
      add :type_of_facility, :string
      add :amount_outstanding, :decimal, precision: 18, scale: 2
      add :estimated_market_value, :decimal, precision: 18, scale: 2
      add :net_outstanding_amount, :decimal, precision: 18, scale: 2
      add :provisions, :decimal, precision: 18, scale: 2
      add :date_of_last_valuation, :date
      add :report, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
