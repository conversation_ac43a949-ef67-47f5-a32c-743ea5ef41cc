defmodule MisReports.Repo.Migrations.CreateScheduleSectionMappings do
  use Ecto.Migration

  def change do
    create table(:schedule_section_mappings) do
      add :template_name, :string
      add :schedule_name, :string
      add :status, :string
      add :schedule_cell_index, :string
      add :accounts, :map
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:schedule_section_mappings, [:maker_id])
    create index(:schedule_section_mappings, [:checker_id])
  end
end
