<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://www.gexf.net/1.2draft" xmlns:ns1="http://www.gexf.net/1.2draft" xmlns:viz="http://www.gexf.net/1.2draft/viz">
  <xs:include schemaLocation="data.xsd"/>
  <xs:include schemaLocation="dynamics.xsd"/>
  <xs:include schemaLocation="hierarchy.xsd"/>
  <xs:include schemaLocation="phylogenics.xsd"/>
  <xs:import namespace="http://www.gexf.net/1.2draft/viz" schemaLocation="viz.xsd"/>
  <xs:element name="gexf" type="ns1:gexf-content"/>
  <xs:complexType name="gexf-content">
    <xs:annotation>
      <xs:documentation>Tree</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" ref="ns1:meta"/>
      <xs:element ref="ns1:graph"/>
    </xs:sequence>
    <xs:attribute name="version" use="required">
      <xs:simpleType>
        <xs:restriction base="xs:string">
          <xs:enumeration value="1.2"/>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
    <xs:attribute name="variant" type="xs:string"/>
  </xs:complexType>
  <xs:element name="meta" type="ns1:meta-content"/>
  <xs:element name="graph" type="ns1:graph-content"/>
  <xs:complexType name="meta-content">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element ref="ns1:creator"/>
      <xs:element ref="ns1:keywords"/>
      <xs:element ref="ns1:description"/>
    </xs:choice>
    <xs:attribute name="lastmodifieddate" type="xs:date"/>
  </xs:complexType>
  <xs:element name="creator" type="xs:string"/>
  <xs:element name="keywords" type="xs:string"/>
  <xs:element name="description" type="xs:string"/>
  <xs:complexType name="nodes-content">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" ref="ns1:node"/>
    </xs:sequence>
    <xs:attribute name="count" type="xs:nonNegativeInteger"/>
  </xs:complexType>
  <xs:element name="node" type="ns1:node-content"/>
  <xs:complexType name="edges-content">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" ref="ns1:edge"/>
    </xs:sequence>
    <xs:attribute name="count" type="xs:nonNegativeInteger"/>
  </xs:complexType>
  <xs:element name="edge" type="ns1:edge-content"/>
  <xs:simpleType name="defaultedgetype-type">
    <xs:annotation>
      <xs:documentation>Datatypes</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="directed"/>
      <xs:enumeration value="undirected"/>
      <xs:enumeration value="mutual"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="edgetype-type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="directed"/>
      <xs:enumeration value="undirected"/>
      <xs:enumeration value="mutual"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="id-type">
    <xs:union memberTypes="xs:string xs:integer"/>
  </xs:simpleType>
  <xs:simpleType name="idtype-type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="integer"/>
      <xs:enumeration value="string"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="mode-type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="static"/>
      <xs:enumeration value="dynamic"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="weight-type">
    <xs:restriction base="xs:float"/>
  </xs:simpleType>
</xs:schema>
