defmodule MisReports.Repo.Migrations.AmendTblShareholdersequity do
  use Ecto.Migration


  def up do
    alter table(:tbl_shareholders_equity) do
      remove_if_exists(:oth_net_income_month, :decimal)
      remove_if_exists(:fair_blc_month, :decimal)
      remove_if_exists(:retain_tranfers_retained, :decimal)
      remove_if_exists(:retain_cash_net_tax, :decimal)
      remove_if_exists(:fair_fair_com_income, :decimal)
      remove_if_exists(:fair_tranfers_retained, :decimal)
      remove_if_exists(:rev_fair_com_income, :decimal)
      remove_if_exists(:fair_oth_incre_month, :decimal)
      remove_if_exists(:rev_divedends, :decimal)
      remove_if_exists(:statu_period_adjust, :decimal)
      remove_if_exists(:retain_oth_incre_month, :decimal)
      remove_if_exists(:statu_net_income_month, :decimal)
      remove_if_exists(:rev_net_income_month, :decimal)
      remove_if_exists(:statu_tranfers_retained, :decimal)
      remove_if_exists(:rev_oth_incre_month, :decimal)
      remove_if_exists(:report_date, :date)
      remove_if_exists(:rev_cash_net_tax, :decimal)
      remove_if_exists(:statu_divedends, :decimal)
      remove_if_exists(:fair_divedends, :decimal)
      remove_if_exists(:oth_blc_month, :decimal)
      remove_if_exists(:retain_divedends, :decimal)
      remove_if_exists(:retain_fair_com_income, :decimal)
      remove_if_exists(:retain_net_income_month, :decimal)
      remove_if_exists(:statu_cash_net_tax, :decimal)
      remove_if_exists(:retain_blc_month, :decimal)
      remove_if_exists(:oth_cash_net_tax, :decimal)
      remove_if_exists(:statu_oth_incre_month, :decimal)
      remove_if_exists(:oth_tranfers_retained, :decimal)
      remove_if_exists(:retain_period_adjust, :decimal)
      remove_if_exists(:fair_period_adjust, :decimal)
      remove_if_exists(:oth_oth_incre_month, :decimal)
      remove_if_exists(:oth_divedends, :decimal)
      remove_if_exists(:rev_period_adjust, :decimal)
      remove_if_exists(:rev_tranfers_retained, :decimal)
      remove_if_exists(:oth_period_adjust, :decimal)
      remove_if_exists(:rev_blc_month, :decimal)
      remove_if_exists(:fair_cash_net_tax, :decimal)
      remove_if_exists(:oth_fair_com_income, :decimal)
      remove_if_exists(:statu_fair_com_income, :decimal)
      remove_if_exists(:statu_blc_month, :decimal)
      remove_if_exists(:fair_net_income_month, :decimal)
    end
    alter table(:tbl_shareholders_equity) do
      add :c22, :decimal
      add :d22, :decimal
      add :e22, :decimal
      add :f22, :decimal
      add :g22, :decimal
      add :c24, :decimal
      add :d24, :decimal
      add :e24, :decimal
      add :f24, :decimal
      add :g24, :decimal
      add :c26, :decimal
      add :d26, :decimal
      add :e26, :decimal
      add :f26, :decimal
      add :g26, :decimal
      add :c28, :decimal
      add :d28, :decimal
      add :e28, :decimal
      add :f28, :decimal
      add :g28, :decimal
      add :c31, :decimal
      add :d31, :decimal
      add :e31, :decimal
      add :f31, :decimal
      add :g31, :decimal
      add :c33, :decimal
      add :d33, :decimal
      add :e33, :decimal
      add :f33, :decimal
      add :g33, :decimal
      add :c35, :decimal
      add :d35, :decimal
      add :e35, :decimal
      add :f35, :decimal
      add :g35, :decimal
      add :c37, :decimal
      add :d37, :decimal
      add :e37, :decimal
      add :f37, :decimal
      add :g37, :decimal
    end
  end
  def down do
  end

end
