(()=>{var vu=Object.create;var pr=Object.defineProperty;var mu=Object.getOwnPropertyDescriptor;var yu=Object.getOwnPropertyNames,rn=Object.getOwnPropertySymbols,bu=Object.getPrototypeOf,gr=Object.prototype.hasOwnProperty,lo=Object.prototype.propertyIsEnumerable;var ao=(e,t,i)=>t in e?pr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,dt=(e,t)=>{for(var i in t||(t={}))gr.call(t,i)&&ao(e,i,t[i]);if(rn)for(var i of rn(t))lo.call(t,i)&&ao(e,i,t[i]);return e};var uo=(e,t)=>{var i={};for(var s in e)gr.call(e,s)&&t.indexOf(s)<0&&(i[s]=e[s]);if(e!=null&&rn)for(var s of rn(e))t.indexOf(s)<0&&lo.call(e,s)&&(i[s]=e[s]);return i};var co=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var xu=(e,t,i,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of yu(t))!gr.call(e,o)&&o!==i&&pr(e,o,{get:()=>t[o],enumerable:!(s=mu(t,o))||s.enumerable});return e};var vr=(e,t,i)=>(i=e!=null?vu(bu(e)):{},xu(t||!e||!e.__esModule?pr(i,"default",{value:e,enumerable:!0}):i,e));var mr=co((fo,sn)=>{(function(e,t){"use strict";typeof sn=="object"&&typeof sn.exports=="object"?sn.exports=e.document?t(e,!0):function(i){if(!i.document)throw new Error("jQuery requires a window with a document");return t(i)}:t(e)})(typeof window!="undefined"?window:fo,function(e,t){"use strict";var i=[],s=Object.getPrototypeOf,o=i.slice,l=i.flat?function(n){return i.flat.call(n)}:function(n){return i.concat.apply([],n)},f=i.push,g=i.indexOf,v={},w=v.toString,S=v.hasOwnProperty,N=S.toString,R=N.call(Object),P={},j=function(r){return typeof r=="function"&&typeof r.nodeType!="number"&&typeof r.item!="function"},z=function(r){return r!=null&&r===r.window},I=e.document,k={type:!0,src:!0,nonce:!0,noModule:!0};function U(n,r,a){a=a||I;var u,d,h=a.createElement("script");if(h.text=n,r)for(u in k)d=r[u]||r.getAttribute&&r.getAttribute(u),d&&h.setAttribute(u,d);a.head.appendChild(h).parentNode.removeChild(h)}function G(n){return n==null?n+"":typeof n=="object"||typeof n=="function"?v[w.call(n)]||"object":typeof n}var oe="3.7.1",Le=/HTML$/i,c=function(n,r){return new c.fn.init(n,r)};c.fn=c.prototype={jquery:oe,constructor:c,length:0,toArray:function(){return o.call(this)},get:function(n){return n==null?o.call(this):n<0?this[n+this.length]:this[n]},pushStack:function(n){var r=c.merge(this.constructor(),n);return r.prevObject=this,r},each:function(n){return c.each(this,n)},map:function(n){return this.pushStack(c.map(this,function(r,a){return n.call(r,a,r)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(c.grep(this,function(n,r){return(r+1)%2}))},odd:function(){return this.pushStack(c.grep(this,function(n,r){return r%2}))},eq:function(n){var r=this.length,a=+n+(n<0?r:0);return this.pushStack(a>=0&&a<r?[this[a]]:[])},end:function(){return this.prevObject||this.constructor()},push:f,sort:i.sort,splice:i.splice},c.extend=c.fn.extend=function(){var n,r,a,u,d,h,p=arguments[0]||{},b=1,y=arguments.length,_=!1;for(typeof p=="boolean"&&(_=p,p=arguments[b]||{},b++),typeof p!="object"&&!j(p)&&(p={}),b===y&&(p=this,b--);b<y;b++)if((n=arguments[b])!=null)for(r in n)u=n[r],!(r==="__proto__"||p===u)&&(_&&u&&(c.isPlainObject(u)||(d=Array.isArray(u)))?(a=p[r],d&&!Array.isArray(a)?h=[]:!d&&!c.isPlainObject(a)?h={}:h=a,d=!1,p[r]=c.extend(_,h,u)):u!==void 0&&(p[r]=u));return p},c.extend({expando:"jQuery"+(oe+Math.random()).replace(/\D/g,""),isReady:!0,error:function(n){throw new Error(n)},noop:function(){},isPlainObject:function(n){var r,a;return!n||w.call(n)!=="[object Object]"?!1:(r=s(n),r?(a=S.call(r,"constructor")&&r.constructor,typeof a=="function"&&N.call(a)===R):!0)},isEmptyObject:function(n){var r;for(r in n)return!1;return!0},globalEval:function(n,r,a){U(n,{nonce:r&&r.nonce},a)},each:function(n,r){var a,u=0;if(Je(n))for(a=n.length;u<a&&r.call(n[u],u,n[u])!==!1;u++);else for(u in n)if(r.call(n[u],u,n[u])===!1)break;return n},text:function(n){var r,a="",u=0,d=n.nodeType;if(!d)for(;r=n[u++];)a+=c.text(r);return d===1||d===11?n.textContent:d===9?n.documentElement.textContent:d===3||d===4?n.nodeValue:a},makeArray:function(n,r){var a=r||[];return n!=null&&(Je(Object(n))?c.merge(a,typeof n=="string"?[n]:n):f.call(a,n)),a},inArray:function(n,r,a){return r==null?-1:g.call(r,n,a)},isXMLDoc:function(n){var r=n&&n.namespaceURI,a=n&&(n.ownerDocument||n).documentElement;return!Le.test(r||a&&a.nodeName||"HTML")},merge:function(n,r){for(var a=+r.length,u=0,d=n.length;u<a;u++)n[d++]=r[u];return n.length=d,n},grep:function(n,r,a){for(var u,d=[],h=0,p=n.length,b=!a;h<p;h++)u=!r(n[h],h),u!==b&&d.push(n[h]);return d},map:function(n,r,a){var u,d,h=0,p=[];if(Je(n))for(u=n.length;h<u;h++)d=r(n[h],h,a),d!=null&&p.push(d);else for(h in n)d=r(n[h],h,a),d!=null&&p.push(d);return l(p)},guid:1,support:P}),typeof Symbol=="function"&&(c.fn[Symbol.iterator]=i[Symbol.iterator]),c.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(n,r){v["[object "+r+"]"]=r.toLowerCase()});function Je(n){var r=!!n&&"length"in n&&n.length,a=G(n);return j(n)||z(n)?!1:a==="array"||r===0||typeof r=="number"&&r>0&&r-1 in n}function ae(n,r){return n.nodeName&&n.nodeName.toLowerCase()===r.toLowerCase()}var gt=i.pop,ci=i.sort,me=i.splice,re="[\\x20\\t\\r\\n\\f]",vt=new RegExp("^"+re+"+|((?:^|[^\\\\])(?:\\\\.)*)"+re+"+$","g");c.contains=function(n,r){var a=r&&r.parentNode;return n===a||!!(a&&a.nodeType===1&&(n.contains?n.contains(a):n.compareDocumentPosition&&n.compareDocumentPosition(a)&16))};var fi=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Vn(n,r){return r?n==="\0"?"\uFFFD":n.slice(0,-1)+"\\"+n.charCodeAt(n.length-1).toString(16)+" ":"\\"+n}c.escapeSelector=function(n){return(n+"").replace(fi,Vn)};var De=I,Y=f;(function(){var n,r,a,u,d,h=Y,p,b,y,_,T,O=c.expando,C=0,M=0,X=Zi(),ne=Zi(),Q=Zi(),ye=Zi(),ge=function(m,x){return m===x&&(d=!0),0},Ge="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Ye="(?:\\\\[\\da-fA-F]{1,6}"+re+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",ie="\\["+re+"*("+Ye+")(?:"+re+"*([*^$|!~]?=)"+re+`*(?:'((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)"|(`+Ye+"))|)"+re+"*\\]",Ot=":("+Ye+`)(?:\\((('((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)")|((?:\\\\.|[^\\\\()[\\]]|`+ie+")*)|.*)\\)|)",se=new RegExp(re+"+","g"),he=new RegExp("^"+re+"*,"+re+"*"),xi=new RegExp("^"+re+"*([>+~]|"+re+")"+re+"*"),ar=new RegExp(re+"|>"),Qe=new RegExp(Ot),_i=new RegExp("^"+Ye+"$"),Ze={ID:new RegExp("^#("+Ye+")"),CLASS:new RegExp("^\\.("+Ye+")"),TAG:new RegExp("^("+Ye+"|[*])"),ATTR:new RegExp("^"+ie),PSEUDO:new RegExp("^"+Ot),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+re+"*(even|odd|(([+-]|)(\\d*)n|)"+re+"*(?:([+-]|)"+re+"*(\\d+)|))"+re+"*\\)|)","i"),bool:new RegExp("^(?:"+Ge+")$","i"),needsContext:new RegExp("^"+re+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+re+"*((?:-\\d)?\\d*)"+re+"*\\)|)(?=[^-]|$)","i")},yt=/^(?:input|select|textarea|button)$/i,bt=/^h\d$/i,Me=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,lr=/[+~]/,ct=new RegExp("\\\\[\\da-fA-F]{1,6}"+re+"?|\\\\([^\\r\\n\\f])","g"),ft=function(m,x){var E="0x"+m.slice(1)-65536;return x||(E<0?String.fromCharCode(E+65536):String.fromCharCode(E>>10|55296,E&1023|56320))},uu=function(){xt()},cu=tn(function(m){return m.disabled===!0&&ae(m,"fieldset")},{dir:"parentNode",next:"legend"});function fu(){try{return p.activeElement}catch(m){}}try{h.apply(i=o.call(De.childNodes),De.childNodes),i[De.childNodes.length].nodeType}catch(m){h={apply:function(x,E){Y.apply(x,o.call(E))},call:function(x){Y.apply(x,o.call(arguments,1))}}}function le(m,x,E,A){var L,H,$,B,F,Z,J,K=x&&x.ownerDocument,ee=x?x.nodeType:9;if(E=E||[],typeof m!="string"||!m||ee!==1&&ee!==9&&ee!==11)return E;if(!A&&(xt(x),x=x||p,y)){if(ee!==11&&(F=Me.exec(m)))if(L=F[1]){if(ee===9)if($=x.getElementById(L)){if($.id===L)return h.call(E,$),E}else return E;else if(K&&($=K.getElementById(L))&&le.contains(x,$)&&$.id===L)return h.call(E,$),E}else{if(F[2])return h.apply(E,x.getElementsByTagName(m)),E;if((L=F[3])&&x.getElementsByClassName)return h.apply(E,x.getElementsByClassName(L)),E}if(!ye[m+" "]&&(!_||!_.test(m))){if(J=m,K=x,ee===1&&(ar.test(m)||xi.test(m))){for(K=lr.test(m)&&ur(x.parentNode)||x,(K!=x||!P.scope)&&((B=x.getAttribute("id"))?B=c.escapeSelector(B):x.setAttribute("id",B=O)),Z=wi(m),H=Z.length;H--;)Z[H]=(B?"#"+B:":scope")+" "+en(Z[H]);J=Z.join(",")}try{return h.apply(E,K.querySelectorAll(J)),E}catch(W){ye(m,!0)}finally{B===O&&x.removeAttribute("id")}}}return oo(m.replace(vt,"$1"),x,E,A)}function Zi(){var m=[];function x(E,A){return m.push(E+" ")>r.cacheLength&&delete x[m.shift()],x[E+" "]=A}return x}function Be(m){return m[O]=!0,m}function ei(m){var x=p.createElement("fieldset");try{return!!m(x)}catch(E){return!1}finally{x.parentNode&&x.parentNode.removeChild(x),x=null}}function du(m){return function(x){return ae(x,"input")&&x.type===m}}function hu(m){return function(x){return(ae(x,"input")||ae(x,"button"))&&x.type===m}}function ro(m){return function(x){return"form"in x?x.parentNode&&x.disabled===!1?"label"in x?"label"in x.parentNode?x.parentNode.disabled===m:x.disabled===m:x.isDisabled===m||x.isDisabled!==!m&&cu(x)===m:x.disabled===m:"label"in x?x.disabled===m:!1}}function It(m){return Be(function(x){return x=+x,Be(function(E,A){for(var L,H=m([],E.length,x),$=H.length;$--;)E[L=H[$]]&&(E[L]=!(A[L]=E[L]))})})}function ur(m){return m&&typeof m.getElementsByTagName!="undefined"&&m}function xt(m){var x,E=m?m.ownerDocument||m:De;return E==p||E.nodeType!==9||!E.documentElement||(p=E,b=p.documentElement,y=!c.isXMLDoc(p),T=b.matches||b.webkitMatchesSelector||b.msMatchesSelector,b.msMatchesSelector&&De!=p&&(x=p.defaultView)&&x.top!==x&&x.addEventListener("unload",uu),P.getById=ei(function(A){return b.appendChild(A).id=c.expando,!p.getElementsByName||!p.getElementsByName(c.expando).length}),P.disconnectedMatch=ei(function(A){return T.call(A,"*")}),P.scope=ei(function(){return p.querySelectorAll(":scope")}),P.cssHas=ei(function(){try{return p.querySelector(":has(*,:jqfake)"),!1}catch(A){return!0}}),P.getById?(r.filter.ID=function(A){var L=A.replace(ct,ft);return function(H){return H.getAttribute("id")===L}},r.find.ID=function(A,L){if(typeof L.getElementById!="undefined"&&y){var H=L.getElementById(A);return H?[H]:[]}}):(r.filter.ID=function(A){var L=A.replace(ct,ft);return function(H){var $=typeof H.getAttributeNode!="undefined"&&H.getAttributeNode("id");return $&&$.value===L}},r.find.ID=function(A,L){if(typeof L.getElementById!="undefined"&&y){var H,$,B,F=L.getElementById(A);if(F){if(H=F.getAttributeNode("id"),H&&H.value===A)return[F];for(B=L.getElementsByName(A),$=0;F=B[$++];)if(H=F.getAttributeNode("id"),H&&H.value===A)return[F]}return[]}}),r.find.TAG=function(A,L){return typeof L.getElementsByTagName!="undefined"?L.getElementsByTagName(A):L.querySelectorAll(A)},r.find.CLASS=function(A,L){if(typeof L.getElementsByClassName!="undefined"&&y)return L.getElementsByClassName(A)},_=[],ei(function(A){var L;b.appendChild(A).innerHTML="<a id='"+O+"' href='' disabled='disabled'></a><select id='"+O+"-\r\\' disabled='disabled'><option selected=''></option></select>",A.querySelectorAll("[selected]").length||_.push("\\["+re+"*(?:value|"+Ge+")"),A.querySelectorAll("[id~="+O+"-]").length||_.push("~="),A.querySelectorAll("a#"+O+"+*").length||_.push(".#.+[+~]"),A.querySelectorAll(":checked").length||_.push(":checked"),L=p.createElement("input"),L.setAttribute("type","hidden"),A.appendChild(L).setAttribute("name","D"),b.appendChild(A).disabled=!0,A.querySelectorAll(":disabled").length!==2&&_.push(":enabled",":disabled"),L=p.createElement("input"),L.setAttribute("name",""),A.appendChild(L),A.querySelectorAll("[name='']").length||_.push("\\["+re+"*name"+re+"*="+re+`*(?:''|"")`)}),P.cssHas||_.push(":has"),_=_.length&&new RegExp(_.join("|")),ge=function(A,L){if(A===L)return d=!0,0;var H=!A.compareDocumentPosition-!L.compareDocumentPosition;return H||(H=(A.ownerDocument||A)==(L.ownerDocument||L)?A.compareDocumentPosition(L):1,H&1||!P.sortDetached&&L.compareDocumentPosition(A)===H?A===p||A.ownerDocument==De&&le.contains(De,A)?-1:L===p||L.ownerDocument==De&&le.contains(De,L)?1:u?g.call(u,A)-g.call(u,L):0:H&4?-1:1)}),p}le.matches=function(m,x){return le(m,null,null,x)},le.matchesSelector=function(m,x){if(xt(m),y&&!ye[x+" "]&&(!_||!_.test(x)))try{var E=T.call(m,x);if(E||P.disconnectedMatch||m.document&&m.document.nodeType!==11)return E}catch(A){ye(x,!0)}return le(x,p,null,[m]).length>0},le.contains=function(m,x){return(m.ownerDocument||m)!=p&&xt(m),c.contains(m,x)},le.attr=function(m,x){(m.ownerDocument||m)!=p&&xt(m);var E=r.attrHandle[x.toLowerCase()],A=E&&S.call(r.attrHandle,x.toLowerCase())?E(m,x,!y):void 0;return A!==void 0?A:m.getAttribute(x)},le.error=function(m){throw new Error("Syntax error, unrecognized expression: "+m)},c.uniqueSort=function(m){var x,E=[],A=0,L=0;if(d=!P.sortStable,u=!P.sortStable&&o.call(m,0),ci.call(m,ge),d){for(;x=m[L++];)x===m[L]&&(A=E.push(L));for(;A--;)me.call(m,E[A],1)}return u=null,m},c.fn.uniqueSort=function(){return this.pushStack(c.uniqueSort(o.apply(this)))},r=c.expr={cacheLength:50,createPseudo:Be,match:Ze,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(m){return m[1]=m[1].replace(ct,ft),m[3]=(m[3]||m[4]||m[5]||"").replace(ct,ft),m[2]==="~="&&(m[3]=" "+m[3]+" "),m.slice(0,4)},CHILD:function(m){return m[1]=m[1].toLowerCase(),m[1].slice(0,3)==="nth"?(m[3]||le.error(m[0]),m[4]=+(m[4]?m[5]+(m[6]||1):2*(m[3]==="even"||m[3]==="odd")),m[5]=+(m[7]+m[8]||m[3]==="odd")):m[3]&&le.error(m[0]),m},PSEUDO:function(m){var x,E=!m[6]&&m[2];return Ze.CHILD.test(m[0])?null:(m[3]?m[2]=m[4]||m[5]||"":E&&Qe.test(E)&&(x=wi(E,!0))&&(x=E.indexOf(")",E.length-x)-E.length)&&(m[0]=m[0].slice(0,x),m[2]=E.slice(0,x)),m.slice(0,3))}},filter:{TAG:function(m){var x=m.replace(ct,ft).toLowerCase();return m==="*"?function(){return!0}:function(E){return ae(E,x)}},CLASS:function(m){var x=X[m+" "];return x||(x=new RegExp("(^|"+re+")"+m+"("+re+"|$)"))&&X(m,function(E){return x.test(typeof E.className=="string"&&E.className||typeof E.getAttribute!="undefined"&&E.getAttribute("class")||"")})},ATTR:function(m,x,E){return function(A){var L=le.attr(A,m);return L==null?x==="!=":x?(L+="",x==="="?L===E:x==="!="?L!==E:x==="^="?E&&L.indexOf(E)===0:x==="*="?E&&L.indexOf(E)>-1:x==="$="?E&&L.slice(-E.length)===E:x==="~="?(" "+L.replace(se," ")+" ").indexOf(E)>-1:x==="|="?L===E||L.slice(0,E.length+1)===E+"-":!1):!0}},CHILD:function(m,x,E,A,L){var H=m.slice(0,3)!=="nth",$=m.slice(-4)!=="last",B=x==="of-type";return A===1&&L===0?function(F){return!!F.parentNode}:function(F,Z,J){var K,ee,W,ce,ke,be=H!==$?"nextSibling":"previousSibling",je=F.parentNode,et=B&&F.nodeName.toLowerCase(),ti=!J&&!B,we=!1;if(je){if(H){for(;be;){for(W=F;W=W[be];)if(B?ae(W,et):W.nodeType===1)return!1;ke=be=m==="only"&&!ke&&"nextSibling"}return!0}if(ke=[$?je.firstChild:je.lastChild],$&&ti){for(ee=je[O]||(je[O]={}),K=ee[m]||[],ce=K[0]===C&&K[1],we=ce&&K[2],W=ce&&je.childNodes[ce];W=++ce&&W&&W[be]||(we=ce=0)||ke.pop();)if(W.nodeType===1&&++we&&W===F){ee[m]=[C,ce,we];break}}else if(ti&&(ee=F[O]||(F[O]={}),K=ee[m]||[],ce=K[0]===C&&K[1],we=ce),we===!1)for(;(W=++ce&&W&&W[be]||(we=ce=0)||ke.pop())&&!((B?ae(W,et):W.nodeType===1)&&++we&&(ti&&(ee=W[O]||(W[O]={}),ee[m]=[C,we]),W===F)););return we-=L,we===A||we%A===0&&we/A>=0}}},PSEUDO:function(m,x){var E,A=r.pseudos[m]||r.setFilters[m.toLowerCase()]||le.error("unsupported pseudo: "+m);return A[O]?A(x):A.length>1?(E=[m,m,"",x],r.setFilters.hasOwnProperty(m.toLowerCase())?Be(function(L,H){for(var $,B=A(L,x),F=B.length;F--;)$=g.call(L,B[F]),L[$]=!(H[$]=B[F])}):function(L){return A(L,0,E)}):A}},pseudos:{not:Be(function(m){var x=[],E=[],A=hr(m.replace(vt,"$1"));return A[O]?Be(function(L,H,$,B){for(var F,Z=A(L,null,B,[]),J=L.length;J--;)(F=Z[J])&&(L[J]=!(H[J]=F))}):function(L,H,$){return x[0]=L,A(x,null,$,E),x[0]=null,!E.pop()}}),has:Be(function(m){return function(x){return le(m,x).length>0}}),contains:Be(function(m){return m=m.replace(ct,ft),function(x){return(x.textContent||c.text(x)).indexOf(m)>-1}}),lang:Be(function(m){return _i.test(m||"")||le.error("unsupported lang: "+m),m=m.replace(ct,ft).toLowerCase(),function(x){var E;do if(E=y?x.lang:x.getAttribute("xml:lang")||x.getAttribute("lang"))return E=E.toLowerCase(),E===m||E.indexOf(m+"-")===0;while((x=x.parentNode)&&x.nodeType===1);return!1}}),target:function(m){var x=e.location&&e.location.hash;return x&&x.slice(1)===m.id},root:function(m){return m===b},focus:function(m){return m===fu()&&p.hasFocus()&&!!(m.type||m.href||~m.tabIndex)},enabled:ro(!1),disabled:ro(!0),checked:function(m){return ae(m,"input")&&!!m.checked||ae(m,"option")&&!!m.selected},selected:function(m){return m.parentNode&&m.parentNode.selectedIndex,m.selected===!0},empty:function(m){for(m=m.firstChild;m;m=m.nextSibling)if(m.nodeType<6)return!1;return!0},parent:function(m){return!r.pseudos.empty(m)},header:function(m){return bt.test(m.nodeName)},input:function(m){return yt.test(m.nodeName)},button:function(m){return ae(m,"input")&&m.type==="button"||ae(m,"button")},text:function(m){var x;return ae(m,"input")&&m.type==="text"&&((x=m.getAttribute("type"))==null||x.toLowerCase()==="text")},first:It(function(){return[0]}),last:It(function(m,x){return[x-1]}),eq:It(function(m,x,E){return[E<0?E+x:E]}),even:It(function(m,x){for(var E=0;E<x;E+=2)m.push(E);return m}),odd:It(function(m,x){for(var E=1;E<x;E+=2)m.push(E);return m}),lt:It(function(m,x,E){var A;for(E<0?A=E+x:E>x?A=x:A=E;--A>=0;)m.push(A);return m}),gt:It(function(m,x,E){for(var A=E<0?E+x:E;++A<x;)m.push(A);return m})}},r.pseudos.nth=r.pseudos.eq;for(n in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[n]=du(n);for(n in{submit:!0,reset:!0})r.pseudos[n]=hu(n);function so(){}so.prototype=r.filters=r.pseudos,r.setFilters=new so;function wi(m,x){var E,A,L,H,$,B,F,Z=ne[m+" "];if(Z)return x?0:Z.slice(0);for($=m,B=[],F=r.preFilter;$;){(!E||(A=he.exec($)))&&(A&&($=$.slice(A[0].length)||$),B.push(L=[])),E=!1,(A=xi.exec($))&&(E=A.shift(),L.push({value:E,type:A[0].replace(vt," ")}),$=$.slice(E.length));for(H in r.filter)(A=Ze[H].exec($))&&(!F[H]||(A=F[H](A)))&&(E=A.shift(),L.push({value:E,type:H,matches:A}),$=$.slice(E.length));if(!E)break}return x?$.length:$?le.error(m):ne(m,B).slice(0)}function en(m){for(var x=0,E=m.length,A="";x<E;x++)A+=m[x].value;return A}function tn(m,x,E){var A=x.dir,L=x.next,H=L||A,$=E&&H==="parentNode",B=M++;return x.first?function(F,Z,J){for(;F=F[A];)if(F.nodeType===1||$)return m(F,Z,J);return!1}:function(F,Z,J){var K,ee,W=[C,B];if(J){for(;F=F[A];)if((F.nodeType===1||$)&&m(F,Z,J))return!0}else for(;F=F[A];)if(F.nodeType===1||$)if(ee=F[O]||(F[O]={}),L&&ae(F,L))F=F[A]||F;else{if((K=ee[H])&&K[0]===C&&K[1]===B)return W[2]=K[2];if(ee[H]=W,W[2]=m(F,Z,J))return!0}return!1}}function cr(m){return m.length>1?function(x,E,A){for(var L=m.length;L--;)if(!m[L](x,E,A))return!1;return!0}:m[0]}function pu(m,x,E){for(var A=0,L=x.length;A<L;A++)le(m,x[A],E);return E}function nn(m,x,E,A,L){for(var H,$=[],B=0,F=m.length,Z=x!=null;B<F;B++)(H=m[B])&&(!E||E(H,A,L))&&($.push(H),Z&&x.push(B));return $}function fr(m,x,E,A,L,H){return A&&!A[O]&&(A=fr(A)),L&&!L[O]&&(L=fr(L,H)),Be(function($,B,F,Z){var J,K,ee,W,ce=[],ke=[],be=B.length,je=$||pu(x||"*",F.nodeType?[F]:F,[]),et=m&&($||!x)?nn(je,ce,m,F,Z):je;if(E?(W=L||($?m:be||A)?[]:B,E(et,W,F,Z)):W=et,A)for(J=nn(W,ke),A(J,[],F,Z),K=J.length;K--;)(ee=J[K])&&(W[ke[K]]=!(et[ke[K]]=ee));if($){if(L||m){if(L){for(J=[],K=W.length;K--;)(ee=W[K])&&J.push(et[K]=ee);L(null,W=[],J,Z)}for(K=W.length;K--;)(ee=W[K])&&(J=L?g.call($,ee):ce[K])>-1&&($[J]=!(B[J]=ee))}}else W=nn(W===B?W.splice(be,W.length):W),L?L(null,B,W,Z):h.apply(B,W)})}function dr(m){for(var x,E,A,L=m.length,H=r.relative[m[0].type],$=H||r.relative[" "],B=H?1:0,F=tn(function(K){return K===x},$,!0),Z=tn(function(K){return g.call(x,K)>-1},$,!0),J=[function(K,ee,W){var ce=!H&&(W||ee!=a)||((x=ee).nodeType?F(K,ee,W):Z(K,ee,W));return x=null,ce}];B<L;B++)if(E=r.relative[m[B].type])J=[tn(cr(J),E)];else{if(E=r.filter[m[B].type].apply(null,m[B].matches),E[O]){for(A=++B;A<L&&!r.relative[m[A].type];A++);return fr(B>1&&cr(J),B>1&&en(m.slice(0,B-1).concat({value:m[B-2].type===" "?"*":""})).replace(vt,"$1"),E,B<A&&dr(m.slice(B,A)),A<L&&dr(m=m.slice(A)),A<L&&en(m))}J.push(E)}return cr(J)}function gu(m,x){var E=x.length>0,A=m.length>0,L=function(H,$,B,F,Z){var J,K,ee,W=0,ce="0",ke=H&&[],be=[],je=a,et=H||A&&r.find.TAG("*",Z),ti=C+=je==null?1:Math.random()||.1,we=et.length;for(Z&&(a=$==p||$||Z);ce!==we&&(J=et[ce])!=null;ce++){if(A&&J){for(K=0,!$&&J.ownerDocument!=p&&(xt(J),B=!y);ee=m[K++];)if(ee(J,$||p,B)){h.call(F,J);break}Z&&(C=ti)}E&&((J=!ee&&J)&&W--,H&&ke.push(J))}if(W+=ce,E&&ce!==W){for(K=0;ee=x[K++];)ee(ke,be,$,B);if(H){if(W>0)for(;ce--;)ke[ce]||be[ce]||(be[ce]=gt.call(F));be=nn(be)}h.apply(F,be),Z&&!H&&be.length>0&&W+x.length>1&&c.uniqueSort(F)}return Z&&(C=ti,a=je),ke};return E?Be(L):L}function hr(m,x){var E,A=[],L=[],H=Q[m+" "];if(!H){for(x||(x=wi(m)),E=x.length;E--;)H=dr(x[E]),H[O]?A.push(H):L.push(H);H=Q(m,gu(L,A)),H.selector=m}return H}function oo(m,x,E,A){var L,H,$,B,F,Z=typeof m=="function"&&m,J=!A&&wi(m=Z.selector||m);if(E=E||[],J.length===1){if(H=J[0]=J[0].slice(0),H.length>2&&($=H[0]).type==="ID"&&x.nodeType===9&&y&&r.relative[H[1].type]){if(x=(r.find.ID($.matches[0].replace(ct,ft),x)||[])[0],x)Z&&(x=x.parentNode);else return E;m=m.slice(H.shift().value.length)}for(L=Ze.needsContext.test(m)?0:H.length;L--&&($=H[L],!r.relative[B=$.type]);)if((F=r.find[B])&&(A=F($.matches[0].replace(ct,ft),lr.test(H[0].type)&&ur(x.parentNode)||x))){if(H.splice(L,1),m=A.length&&en(H),!m)return h.apply(E,A),E;break}}return(Z||hr(m,J))(A,x,!y,E,!x||lr.test(m)&&ur(x.parentNode)||x),E}P.sortStable=O.split("").sort(ge).join("")===O,xt(),P.sortDetached=ei(function(m){return m.compareDocumentPosition(p.createElement("fieldset"))&1}),c.find=le,c.expr[":"]=c.expr.pseudos,c.unique=c.uniqueSort,le.compile=hr,le.select=oo,le.setDocument=xt,le.tokenize=wi,le.escape=c.escapeSelector,le.getText=c.text,le.isXML=c.isXMLDoc,le.selectors=c.expr,le.support=c.support,le.uniqueSort=c.uniqueSort})();var V=function(n,r,a){for(var u=[],d=a!==void 0;(n=n[r])&&n.nodeType!==9;)if(n.nodeType===1){if(d&&c(n).is(a))break;u.push(n)}return u},fe=function(n,r){for(var a=[];n;n=n.nextSibling)n.nodeType===1&&n!==r&&a.push(n);return a},te=c.expr.match.needsContext,ue=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function ze(n,r,a){return j(r)?c.grep(n,function(u,d){return!!r.call(u,d,u)!==a}):r.nodeType?c.grep(n,function(u){return u===r!==a}):typeof r!="string"?c.grep(n,function(u){return g.call(r,u)>-1!==a}):c.filter(r,n,a)}c.filter=function(n,r,a){var u=r[0];return a&&(n=":not("+n+")"),r.length===1&&u.nodeType===1?c.find.matchesSelector(u,n)?[u]:[]:c.find.matches(n,c.grep(r,function(d){return d.nodeType===1}))},c.fn.extend({find:function(n){var r,a,u=this.length,d=this;if(typeof n!="string")return this.pushStack(c(n).filter(function(){for(r=0;r<u;r++)if(c.contains(d[r],this))return!0}));for(a=this.pushStack([]),r=0;r<u;r++)c.find(n,d[r],a);return u>1?c.uniqueSort(a):a},filter:function(n){return this.pushStack(ze(this,n||[],!1))},not:function(n){return this.pushStack(ze(this,n||[],!0))},is:function(n){return!!ze(this,typeof n=="string"&&te.test(n)?c(n):n||[],!1).length}});var $e,mt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Vt=c.fn.init=function(n,r,a){var u,d;if(!n)return this;if(a=a||$e,typeof n=="string")if(n[0]==="<"&&n[n.length-1]===">"&&n.length>=3?u=[null,n,null]:u=mt.exec(n),u&&(u[1]||!r))if(u[1]){if(r=r instanceof c?r[0]:r,c.merge(this,c.parseHTML(u[1],r&&r.nodeType?r.ownerDocument||r:I,!0)),ue.test(u[1])&&c.isPlainObject(r))for(u in r)j(this[u])?this[u](r[u]):this.attr(u,r[u]);return this}else return d=I.getElementById(u[2]),d&&(this[0]=d,this.length=1),this;else return!r||r.jquery?(r||a).find(n):this.constructor(r).find(n);else{if(n.nodeType)return this[0]=n,this.length=1,this;if(j(n))return a.ready!==void 0?a.ready(n):n(c)}return c.makeArray(n,this)};Vt.prototype=c.fn,$e=c(I);var Xe=/^(?:parents|prev(?:Until|All))/,Jt={children:!0,contents:!0,next:!0,prev:!0};c.fn.extend({has:function(n){var r=c(n,this),a=r.length;return this.filter(function(){for(var u=0;u<a;u++)if(c.contains(this,r[u]))return!0})},closest:function(n,r){var a,u=0,d=this.length,h=[],p=typeof n!="string"&&c(n);if(!te.test(n)){for(;u<d;u++)for(a=this[u];a&&a!==r;a=a.parentNode)if(a.nodeType<11&&(p?p.index(a)>-1:a.nodeType===1&&c.find.matchesSelector(a,n))){h.push(a);break}}return this.pushStack(h.length>1?c.uniqueSort(h):h)},index:function(n){return n?typeof n=="string"?g.call(c(n),this[0]):g.call(this,n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,r){return this.pushStack(c.uniqueSort(c.merge(this.get(),c(n,r))))},addBack:function(n){return this.add(n==null?this.prevObject:this.prevObject.filter(n))}});function Fe(n,r){for(;(n=n[r])&&n.nodeType!==1;);return n}c.each({parent:function(n){var r=n.parentNode;return r&&r.nodeType!==11?r:null},parents:function(n){return V(n,"parentNode")},parentsUntil:function(n,r,a){return V(n,"parentNode",a)},next:function(n){return Fe(n,"nextSibling")},prev:function(n){return Fe(n,"previousSibling")},nextAll:function(n){return V(n,"nextSibling")},prevAll:function(n){return V(n,"previousSibling")},nextUntil:function(n,r,a){return V(n,"nextSibling",a)},prevUntil:function(n,r,a){return V(n,"previousSibling",a)},siblings:function(n){return fe((n.parentNode||{}).firstChild,n)},children:function(n){return fe(n.firstChild)},contents:function(n){return n.contentDocument!=null&&s(n.contentDocument)?n.contentDocument:(ae(n,"template")&&(n=n.content||n),c.merge([],n.childNodes))}},function(n,r){c.fn[n]=function(a,u){var d=c.map(this,r,a);return n.slice(-5)!=="Until"&&(u=a),u&&typeof u=="string"&&(d=c.filter(u,d)),this.length>1&&(Jt[n]||c.uniqueSort(d),Xe.test(n)&&d.reverse()),this.pushStack(d)}});var Ee=/[^\x20\t\r\n\f]+/g;function Vi(n){var r={};return c.each(n.match(Ee)||[],function(a,u){r[u]=!0}),r}c.Callbacks=function(n){n=typeof n=="string"?Vi(n):c.extend({},n);var r,a,u,d,h=[],p=[],b=-1,y=function(){for(d=d||n.once,u=r=!0;p.length;b=-1)for(a=p.shift();++b<h.length;)h[b].apply(a[0],a[1])===!1&&n.stopOnFalse&&(b=h.length,a=!1);n.memory||(a=!1),r=!1,d&&(a?h=[]:h="")},_={add:function(){return h&&(a&&!r&&(b=h.length-1,p.push(a)),function T(O){c.each(O,function(C,M){j(M)?(!n.unique||!_.has(M))&&h.push(M):M&&M.length&&G(M)!=="string"&&T(M)})}(arguments),a&&!r&&y()),this},remove:function(){return c.each(arguments,function(T,O){for(var C;(C=c.inArray(O,h,C))>-1;)h.splice(C,1),C<=b&&b--}),this},has:function(T){return T?c.inArray(T,h)>-1:h.length>0},empty:function(){return h&&(h=[]),this},disable:function(){return d=p=[],h=a="",this},disabled:function(){return!h},lock:function(){return d=p=[],!a&&!r&&(h=a=""),this},locked:function(){return!!d},fireWith:function(T,O){return d||(O=O||[],O=[T,O.slice?O.slice():O],p.push(O),r||y()),this},fire:function(){return _.fireWith(this,arguments),this},fired:function(){return!!u}};return _};function zt(n){return n}function Ji(n){throw n}function As(n,r,a,u){var d;try{n&&j(d=n.promise)?d.call(n).done(r).fail(a):n&&j(d=n.then)?d.call(n,r,a):r.apply(void 0,[n].slice(u))}catch(h){a.apply(void 0,[h])}}c.extend({Deferred:function(n){var r=[["notify","progress",c.Callbacks("memory"),c.Callbacks("memory"),2],["resolve","done",c.Callbacks("once memory"),c.Callbacks("once memory"),0,"resolved"],["reject","fail",c.Callbacks("once memory"),c.Callbacks("once memory"),1,"rejected"]],a="pending",u={state:function(){return a},always:function(){return d.done(arguments).fail(arguments),this},catch:function(h){return u.then(null,h)},pipe:function(){var h=arguments;return c.Deferred(function(p){c.each(r,function(b,y){var _=j(h[y[4]])&&h[y[4]];d[y[1]](function(){var T=_&&_.apply(this,arguments);T&&j(T.promise)?T.promise().progress(p.notify).done(p.resolve).fail(p.reject):p[y[0]+"With"](this,_?[T]:arguments)})}),h=null}).promise()},then:function(h,p,b){var y=0;function _(T,O,C,M){return function(){var X=this,ne=arguments,Q=function(){var ge,Ge;if(!(T<y)){if(ge=C.apply(X,ne),ge===O.promise())throw new TypeError("Thenable self-resolution");Ge=ge&&(typeof ge=="object"||typeof ge=="function")&&ge.then,j(Ge)?M?Ge.call(ge,_(y,O,zt,M),_(y,O,Ji,M)):(y++,Ge.call(ge,_(y,O,zt,M),_(y,O,Ji,M),_(y,O,zt,O.notifyWith))):(C!==zt&&(X=void 0,ne=[ge]),(M||O.resolveWith)(X,ne))}},ye=M?Q:function(){try{Q()}catch(ge){c.Deferred.exceptionHook&&c.Deferred.exceptionHook(ge,ye.error),T+1>=y&&(C!==Ji&&(X=void 0,ne=[ge]),O.rejectWith(X,ne))}};T?ye():(c.Deferred.getErrorHook?ye.error=c.Deferred.getErrorHook():c.Deferred.getStackHook&&(ye.error=c.Deferred.getStackHook()),e.setTimeout(ye))}}return c.Deferred(function(T){r[0][3].add(_(0,T,j(b)?b:zt,T.notifyWith)),r[1][3].add(_(0,T,j(h)?h:zt)),r[2][3].add(_(0,T,j(p)?p:Ji))}).promise()},promise:function(h){return h!=null?c.extend(h,u):u}},d={};return c.each(r,function(h,p){var b=p[2],y=p[5];u[p[1]]=b.add,y&&b.add(function(){a=y},r[3-h][2].disable,r[3-h][3].disable,r[0][2].lock,r[0][3].lock),b.add(p[3].fire),d[p[0]]=function(){return d[p[0]+"With"](this===d?void 0:this,arguments),this},d[p[0]+"With"]=b.fireWith}),u.promise(d),n&&n.call(d,d),d},when:function(n){var r=arguments.length,a=r,u=Array(a),d=o.call(arguments),h=c.Deferred(),p=function(b){return function(y){u[b]=this,d[b]=arguments.length>1?o.call(arguments):y,--r||h.resolveWith(u,d)}};if(r<=1&&(As(n,h.done(p(a)).resolve,h.reject,!r),h.state()==="pending"||j(d[a]&&d[a].then)))return h.then();for(;a--;)As(d[a],p(a),h.reject);return h.promise()}});var xl=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;c.Deferred.exceptionHook=function(n,r){e.console&&e.console.warn&&n&&xl.test(n.name)&&e.console.warn("jQuery.Deferred exception: "+n.message,n.stack,r)},c.readyException=function(n){e.setTimeout(function(){throw n})};var Jn=c.Deferred();c.fn.ready=function(n){return Jn.then(n).catch(function(r){c.readyException(r)}),this},c.extend({isReady:!1,readyWait:1,ready:function(n){(n===!0?--c.readyWait:c.isReady)||(c.isReady=!0,!(n!==!0&&--c.readyWait>0)&&Jn.resolveWith(I,[c]))}}),c.ready.then=Jn.then;function zi(){I.removeEventListener("DOMContentLoaded",zi),e.removeEventListener("load",zi),c.ready()}I.readyState==="complete"||I.readyState!=="loading"&&!I.documentElement.doScroll?e.setTimeout(c.ready):(I.addEventListener("DOMContentLoaded",zi),e.addEventListener("load",zi));var lt=function(n,r,a,u,d,h,p){var b=0,y=n.length,_=a==null;if(G(a)==="object"){d=!0;for(b in a)lt(n,r,b,a[b],!0,h,p)}else if(u!==void 0&&(d=!0,j(u)||(p=!0),_&&(p?(r.call(n,u),r=null):(_=r,r=function(T,O,C){return _.call(c(T),C)})),r))for(;b<y;b++)r(n[b],a,p?u:u.call(n[b],b,r(n[b],a)));return d?n:_?r.call(n):y?r(n[0],a):h},_l=/^-ms-/,wl=/-([a-z])/g;function Sl(n,r){return r.toUpperCase()}function Ke(n){return n.replace(_l,"ms-").replace(wl,Sl)}var di=function(n){return n.nodeType===1||n.nodeType===9||!+n.nodeType};function hi(){this.expando=c.expando+hi.uid++}hi.uid=1,hi.prototype={cache:function(n){var r=n[this.expando];return r||(r={},di(n)&&(n.nodeType?n[this.expando]=r:Object.defineProperty(n,this.expando,{value:r,configurable:!0}))),r},set:function(n,r,a){var u,d=this.cache(n);if(typeof r=="string")d[Ke(r)]=a;else for(u in r)d[Ke(u)]=r[u];return d},get:function(n,r){return r===void 0?this.cache(n):n[this.expando]&&n[this.expando][Ke(r)]},access:function(n,r,a){return r===void 0||r&&typeof r=="string"&&a===void 0?this.get(n,r):(this.set(n,r,a),a!==void 0?a:r)},remove:function(n,r){var a,u=n[this.expando];if(u!==void 0){if(r!==void 0)for(Array.isArray(r)?r=r.map(Ke):(r=Ke(r),r=r in u?[r]:r.match(Ee)||[]),a=r.length;a--;)delete u[r[a]];(r===void 0||c.isEmptyObject(u))&&(n.nodeType?n[this.expando]=void 0:delete n[this.expando])}},hasData:function(n){var r=n[this.expando];return r!==void 0&&!c.isEmptyObject(r)}};var q=new hi,Ce=new hi,El=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Cl=/[A-Z]/g;function Al(n){return n==="true"?!0:n==="false"?!1:n==="null"?null:n===+n+""?+n:El.test(n)?JSON.parse(n):n}function Ts(n,r,a){var u;if(a===void 0&&n.nodeType===1)if(u="data-"+r.replace(Cl,"-$&").toLowerCase(),a=n.getAttribute(u),typeof a=="string"){try{a=Al(a)}catch(d){}Ce.set(n,r,a)}else a=void 0;return a}c.extend({hasData:function(n){return Ce.hasData(n)||q.hasData(n)},data:function(n,r,a){return Ce.access(n,r,a)},removeData:function(n,r){Ce.remove(n,r)},_data:function(n,r,a){return q.access(n,r,a)},_removeData:function(n,r){q.remove(n,r)}}),c.fn.extend({data:function(n,r){var a,u,d,h=this[0],p=h&&h.attributes;if(n===void 0){if(this.length&&(d=Ce.get(h),h.nodeType===1&&!q.get(h,"hasDataAttrs"))){for(a=p.length;a--;)p[a]&&(u=p[a].name,u.indexOf("data-")===0&&(u=Ke(u.slice(5)),Ts(h,u,d[u])));q.set(h,"hasDataAttrs",!0)}return d}return typeof n=="object"?this.each(function(){Ce.set(this,n)}):lt(this,function(b){var y;if(h&&b===void 0)return y=Ce.get(h,n),y!==void 0||(y=Ts(h,n),y!==void 0)?y:void 0;this.each(function(){Ce.set(this,n,b)})},null,r,arguments.length>1,null,!0)},removeData:function(n){return this.each(function(){Ce.remove(this,n)})}}),c.extend({queue:function(n,r,a){var u;if(n)return r=(r||"fx")+"queue",u=q.get(n,r),a&&(!u||Array.isArray(a)?u=q.access(n,r,c.makeArray(a)):u.push(a)),u||[]},dequeue:function(n,r){r=r||"fx";var a=c.queue(n,r),u=a.length,d=a.shift(),h=c._queueHooks(n,r),p=function(){c.dequeue(n,r)};d==="inprogress"&&(d=a.shift(),u--),d&&(r==="fx"&&a.unshift("inprogress"),delete h.stop,d.call(n,p,h)),!u&&h&&h.empty.fire()},_queueHooks:function(n,r){var a=r+"queueHooks";return q.get(n,a)||q.access(n,a,{empty:c.Callbacks("once memory").add(function(){q.remove(n,[r+"queue",a])})})}}),c.fn.extend({queue:function(n,r){var a=2;return typeof n!="string"&&(r=n,n="fx",a--),arguments.length<a?c.queue(this[0],n):r===void 0?this:this.each(function(){var u=c.queue(this,n,r);c._queueHooks(this,n),n==="fx"&&u[0]!=="inprogress"&&c.dequeue(this,n)})},dequeue:function(n){return this.each(function(){c.dequeue(this,n)})},clearQueue:function(n){return this.queue(n||"fx",[])},promise:function(n,r){var a,u=1,d=c.Deferred(),h=this,p=this.length,b=function(){--u||d.resolveWith(h,[h])};for(typeof n!="string"&&(r=n,n=void 0),n=n||"fx";p--;)a=q.get(h[p],n+"queueHooks"),a&&a.empty&&(u++,a.empty.add(b));return b(),d.promise(r)}});var ks=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pi=new RegExp("^(?:([+-])=|)("+ks+")([a-z%]*)$","i"),ut=["Top","Right","Bottom","Left"],Lt=I.documentElement,Xt=function(n){return c.contains(n.ownerDocument,n)},Tl={composed:!0};Lt.getRootNode&&(Xt=function(n){return c.contains(n.ownerDocument,n)||n.getRootNode(Tl)===n.ownerDocument});var Xi=function(n,r){return n=r||n,n.style.display==="none"||n.style.display===""&&Xt(n)&&c.css(n,"display")==="none"};function Ps(n,r,a,u){var d,h,p=20,b=u?function(){return u.cur()}:function(){return c.css(n,r,"")},y=b(),_=a&&a[3]||(c.cssNumber[r]?"":"px"),T=n.nodeType&&(c.cssNumber[r]||_!=="px"&&+y)&&pi.exec(c.css(n,r));if(T&&T[3]!==_){for(y=y/2,_=_||T[3],T=+y||1;p--;)c.style(n,r,T+_),(1-h)*(1-(h=b()/y||.5))<=0&&(p=0),T=T/h;T=T*2,c.style(n,r,T+_),a=a||[]}return a&&(T=+T||+y||0,d=a[1]?T+(a[1]+1)*a[2]:+a[2],u&&(u.unit=_,u.start=T,u.end=d)),d}var Ls={};function kl(n){var r,a=n.ownerDocument,u=n.nodeName,d=Ls[u];return d||(r=a.body.appendChild(a.createElement(u)),d=c.css(r,"display"),r.parentNode.removeChild(r),d==="none"&&(d="block"),Ls[u]=d,d)}function Kt(n,r){for(var a,u,d=[],h=0,p=n.length;h<p;h++)u=n[h],u.style&&(a=u.style.display,r?(a==="none"&&(d[h]=q.get(u,"display")||null,d[h]||(u.style.display="")),u.style.display===""&&Xi(u)&&(d[h]=kl(u))):a!=="none"&&(d[h]="none",q.set(u,"display",a)));for(h=0;h<p;h++)d[h]!=null&&(n[h].style.display=d[h]);return n}c.fn.extend({show:function(){return Kt(this,!0)},hide:function(){return Kt(this)},toggle:function(n){return typeof n=="boolean"?n?this.show():this.hide():this.each(function(){Xi(this)?c(this).show():c(this).hide()})}});var gi=/^(?:checkbox|radio)$/i,Ds=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Rs=/^$|^module$|\/(?:java|ecma)script/i;(function(){var n=I.createDocumentFragment(),r=n.appendChild(I.createElement("div")),a=I.createElement("input");a.setAttribute("type","radio"),a.setAttribute("checked","checked"),a.setAttribute("name","t"),r.appendChild(a),P.checkClone=r.cloneNode(!0).cloneNode(!0).lastChild.checked,r.innerHTML="<textarea>x</textarea>",P.noCloneChecked=!!r.cloneNode(!0).lastChild.defaultValue,r.innerHTML="<option></option>",P.option=!!r.lastChild})();var Ne={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Ne.tbody=Ne.tfoot=Ne.colgroup=Ne.caption=Ne.thead,Ne.th=Ne.td,P.option||(Ne.optgroup=Ne.option=[1,"<select multiple='multiple'>","</select>"]);function Ae(n,r){var a;return typeof n.getElementsByTagName!="undefined"?a=n.getElementsByTagName(r||"*"):typeof n.querySelectorAll!="undefined"?a=n.querySelectorAll(r||"*"):a=[],r===void 0||r&&ae(n,r)?c.merge([n],a):a}function zn(n,r){for(var a=0,u=n.length;a<u;a++)q.set(n[a],"globalEval",!r||q.get(r[a],"globalEval"))}var Pl=/<|&#?\w+;/;function Os(n,r,a,u,d){for(var h,p,b,y,_,T,O=r.createDocumentFragment(),C=[],M=0,X=n.length;M<X;M++)if(h=n[M],h||h===0)if(G(h)==="object")c.merge(C,h.nodeType?[h]:h);else if(!Pl.test(h))C.push(r.createTextNode(h));else{for(p=p||O.appendChild(r.createElement("div")),b=(Ds.exec(h)||["",""])[1].toLowerCase(),y=Ne[b]||Ne._default,p.innerHTML=y[1]+c.htmlPrefilter(h)+y[2],T=y[0];T--;)p=p.lastChild;c.merge(C,p.childNodes),p=O.firstChild,p.textContent=""}for(O.textContent="",M=0;h=C[M++];){if(u&&c.inArray(h,u)>-1){d&&d.push(h);continue}if(_=Xt(h),p=Ae(O.appendChild(h),"script"),_&&zn(p),a)for(T=0;h=p[T++];)Rs.test(h.type||"")&&a.push(h)}return O}var Is=/^([^.]*)(?:\.(.+)|)/;function Gt(){return!0}function Yt(){return!1}function Xn(n,r,a,u,d,h){var p,b;if(typeof r=="object"){typeof a!="string"&&(u=u||a,a=void 0);for(b in r)Xn(n,b,a,u,r[b],h);return n}if(u==null&&d==null?(d=a,u=a=void 0):d==null&&(typeof a=="string"?(d=u,u=void 0):(d=u,u=a,a=void 0)),d===!1)d=Yt;else if(!d)return n;return h===1&&(p=d,d=function(y){return c().off(y),p.apply(this,arguments)},d.guid=p.guid||(p.guid=c.guid++)),n.each(function(){c.event.add(this,r,d,u,a)})}c.event={global:{},add:function(n,r,a,u,d){var h,p,b,y,_,T,O,C,M,X,ne,Q=q.get(n);if(!!di(n))for(a.handler&&(h=a,a=h.handler,d=h.selector),d&&c.find.matchesSelector(Lt,d),a.guid||(a.guid=c.guid++),(y=Q.events)||(y=Q.events=Object.create(null)),(p=Q.handle)||(p=Q.handle=function(ye){return typeof c!="undefined"&&c.event.triggered!==ye.type?c.event.dispatch.apply(n,arguments):void 0}),r=(r||"").match(Ee)||[""],_=r.length;_--;)b=Is.exec(r[_])||[],M=ne=b[1],X=(b[2]||"").split(".").sort(),M&&(O=c.event.special[M]||{},M=(d?O.delegateType:O.bindType)||M,O=c.event.special[M]||{},T=c.extend({type:M,origType:ne,data:u,handler:a,guid:a.guid,selector:d,needsContext:d&&c.expr.match.needsContext.test(d),namespace:X.join(".")},h),(C=y[M])||(C=y[M]=[],C.delegateCount=0,(!O.setup||O.setup.call(n,u,X,p)===!1)&&n.addEventListener&&n.addEventListener(M,p)),O.add&&(O.add.call(n,T),T.handler.guid||(T.handler.guid=a.guid)),d?C.splice(C.delegateCount++,0,T):C.push(T),c.event.global[M]=!0)},remove:function(n,r,a,u,d){var h,p,b,y,_,T,O,C,M,X,ne,Q=q.hasData(n)&&q.get(n);if(!(!Q||!(y=Q.events))){for(r=(r||"").match(Ee)||[""],_=r.length;_--;){if(b=Is.exec(r[_])||[],M=ne=b[1],X=(b[2]||"").split(".").sort(),!M){for(M in y)c.event.remove(n,M+r[_],a,u,!0);continue}for(O=c.event.special[M]||{},M=(u?O.delegateType:O.bindType)||M,C=y[M]||[],b=b[2]&&new RegExp("(^|\\.)"+X.join("\\.(?:.*\\.|)")+"(\\.|$)"),p=h=C.length;h--;)T=C[h],(d||ne===T.origType)&&(!a||a.guid===T.guid)&&(!b||b.test(T.namespace))&&(!u||u===T.selector||u==="**"&&T.selector)&&(C.splice(h,1),T.selector&&C.delegateCount--,O.remove&&O.remove.call(n,T));p&&!C.length&&((!O.teardown||O.teardown.call(n,X,Q.handle)===!1)&&c.removeEvent(n,M,Q.handle),delete y[M])}c.isEmptyObject(y)&&q.remove(n,"handle events")}},dispatch:function(n){var r,a,u,d,h,p,b=new Array(arguments.length),y=c.event.fix(n),_=(q.get(this,"events")||Object.create(null))[y.type]||[],T=c.event.special[y.type]||{};for(b[0]=y,r=1;r<arguments.length;r++)b[r]=arguments[r];if(y.delegateTarget=this,!(T.preDispatch&&T.preDispatch.call(this,y)===!1)){for(p=c.event.handlers.call(this,y,_),r=0;(d=p[r++])&&!y.isPropagationStopped();)for(y.currentTarget=d.elem,a=0;(h=d.handlers[a++])&&!y.isImmediatePropagationStopped();)(!y.rnamespace||h.namespace===!1||y.rnamespace.test(h.namespace))&&(y.handleObj=h,y.data=h.data,u=((c.event.special[h.origType]||{}).handle||h.handler).apply(d.elem,b),u!==void 0&&(y.result=u)===!1&&(y.preventDefault(),y.stopPropagation()));return T.postDispatch&&T.postDispatch.call(this,y),y.result}},handlers:function(n,r){var a,u,d,h,p,b=[],y=r.delegateCount,_=n.target;if(y&&_.nodeType&&!(n.type==="click"&&n.button>=1)){for(;_!==this;_=_.parentNode||this)if(_.nodeType===1&&!(n.type==="click"&&_.disabled===!0)){for(h=[],p={},a=0;a<y;a++)u=r[a],d=u.selector+" ",p[d]===void 0&&(p[d]=u.needsContext?c(d,this).index(_)>-1:c.find(d,this,null,[_]).length),p[d]&&h.push(u);h.length&&b.push({elem:_,handlers:h})}}return _=this,y<r.length&&b.push({elem:_,handlers:r.slice(y)}),b},addProp:function(n,r){Object.defineProperty(c.Event.prototype,n,{enumerable:!0,configurable:!0,get:j(r)?function(){if(this.originalEvent)return r(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[n]},set:function(a){Object.defineProperty(this,n,{enumerable:!0,configurable:!0,writable:!0,value:a})}})},fix:function(n){return n[c.expando]?n:new c.Event(n)},special:{load:{noBubble:!0},click:{setup:function(n){var r=this||n;return gi.test(r.type)&&r.click&&ae(r,"input")&&Ki(r,"click",!0),!1},trigger:function(n){var r=this||n;return gi.test(r.type)&&r.click&&ae(r,"input")&&Ki(r,"click"),!0},_default:function(n){var r=n.target;return gi.test(r.type)&&r.click&&ae(r,"input")&&q.get(r,"click")||ae(r,"a")}},beforeunload:{postDispatch:function(n){n.result!==void 0&&n.originalEvent&&(n.originalEvent.returnValue=n.result)}}}};function Ki(n,r,a){if(!a){q.get(n,r)===void 0&&c.event.add(n,r,Gt);return}q.set(n,r,!1),c.event.add(n,r,{namespace:!1,handler:function(u){var d,h=q.get(this,r);if(u.isTrigger&1&&this[r]){if(h)(c.event.special[r]||{}).delegateType&&u.stopPropagation();else if(h=o.call(arguments),q.set(this,r,h),this[r](),d=q.get(this,r),q.set(this,r,!1),h!==d)return u.stopImmediatePropagation(),u.preventDefault(),d}else h&&(q.set(this,r,c.event.trigger(h[0],h.slice(1),this)),u.stopPropagation(),u.isImmediatePropagationStopped=Gt)}})}c.removeEvent=function(n,r,a){n.removeEventListener&&n.removeEventListener(r,a)},c.Event=function(n,r){if(!(this instanceof c.Event))return new c.Event(n,r);n&&n.type?(this.originalEvent=n,this.type=n.type,this.isDefaultPrevented=n.defaultPrevented||n.defaultPrevented===void 0&&n.returnValue===!1?Gt:Yt,this.target=n.target&&n.target.nodeType===3?n.target.parentNode:n.target,this.currentTarget=n.currentTarget,this.relatedTarget=n.relatedTarget):this.type=n,r&&c.extend(this,r),this.timeStamp=n&&n.timeStamp||Date.now(),this[c.expando]=!0},c.Event.prototype={constructor:c.Event,isDefaultPrevented:Yt,isPropagationStopped:Yt,isImmediatePropagationStopped:Yt,isSimulated:!1,preventDefault:function(){var n=this.originalEvent;this.isDefaultPrevented=Gt,n&&!this.isSimulated&&n.preventDefault()},stopPropagation:function(){var n=this.originalEvent;this.isPropagationStopped=Gt,n&&!this.isSimulated&&n.stopPropagation()},stopImmediatePropagation:function(){var n=this.originalEvent;this.isImmediatePropagationStopped=Gt,n&&!this.isSimulated&&n.stopImmediatePropagation(),this.stopPropagation()}},c.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},c.event.addProp),c.each({focus:"focusin",blur:"focusout"},function(n,r){function a(u){if(I.documentMode){var d=q.get(this,"handle"),h=c.event.fix(u);h.type=u.type==="focusin"?"focus":"blur",h.isSimulated=!0,d(u),h.target===h.currentTarget&&d(h)}else c.event.simulate(r,u.target,c.event.fix(u))}c.event.special[n]={setup:function(){var u;if(Ki(this,n,!0),I.documentMode)u=q.get(this,r),u||this.addEventListener(r,a),q.set(this,r,(u||0)+1);else return!1},trigger:function(){return Ki(this,n),!0},teardown:function(){var u;if(I.documentMode)u=q.get(this,r)-1,u?q.set(this,r,u):(this.removeEventListener(r,a),q.remove(this,r));else return!1},_default:function(u){return q.get(u.target,n)},delegateType:r},c.event.special[r]={setup:function(){var u=this.ownerDocument||this.document||this,d=I.documentMode?this:u,h=q.get(d,r);h||(I.documentMode?this.addEventListener(r,a):u.addEventListener(n,a,!0)),q.set(d,r,(h||0)+1)},teardown:function(){var u=this.ownerDocument||this.document||this,d=I.documentMode?this:u,h=q.get(d,r)-1;h?q.set(d,r,h):(I.documentMode?this.removeEventListener(r,a):u.removeEventListener(n,a,!0),q.remove(d,r))}}}),c.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(n,r){c.event.special[n]={delegateType:r,bindType:r,handle:function(a){var u,d=this,h=a.relatedTarget,p=a.handleObj;return(!h||h!==d&&!c.contains(d,h))&&(a.type=p.origType,u=p.handler.apply(this,arguments),a.type=r),u}}}),c.fn.extend({on:function(n,r,a,u){return Xn(this,n,r,a,u)},one:function(n,r,a,u){return Xn(this,n,r,a,u,1)},off:function(n,r,a){var u,d;if(n&&n.preventDefault&&n.handleObj)return u=n.handleObj,c(n.delegateTarget).off(u.namespace?u.origType+"."+u.namespace:u.origType,u.selector,u.handler),this;if(typeof n=="object"){for(d in n)this.off(d,r,n[d]);return this}return(r===!1||typeof r=="function")&&(a=r,r=void 0),a===!1&&(a=Yt),this.each(function(){c.event.remove(this,n,a,r)})}});var Ll=/<script|<style|<link/i,Dl=/checked\s*(?:[^=]|=\s*.checked.)/i,Rl=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Ns(n,r){return ae(n,"table")&&ae(r.nodeType!==11?r:r.firstChild,"tr")&&c(n).children("tbody")[0]||n}function Ol(n){return n.type=(n.getAttribute("type")!==null)+"/"+n.type,n}function Il(n){return(n.type||"").slice(0,5)==="true/"?n.type=n.type.slice(5):n.removeAttribute("type"),n}function Ms(n,r){var a,u,d,h,p,b,y;if(r.nodeType===1){if(q.hasData(n)&&(h=q.get(n),y=h.events,y)){q.remove(r,"handle events");for(d in y)for(a=0,u=y[d].length;a<u;a++)c.event.add(r,d,y[d][a])}Ce.hasData(n)&&(p=Ce.access(n),b=c.extend({},p),Ce.set(r,b))}}function Nl(n,r){var a=r.nodeName.toLowerCase();a==="input"&&gi.test(n.type)?r.checked=n.checked:(a==="input"||a==="textarea")&&(r.defaultValue=n.defaultValue)}function Qt(n,r,a,u){r=l(r);var d,h,p,b,y,_,T=0,O=n.length,C=O-1,M=r[0],X=j(M);if(X||O>1&&typeof M=="string"&&!P.checkClone&&Dl.test(M))return n.each(function(ne){var Q=n.eq(ne);X&&(r[0]=M.call(this,ne,Q.html())),Qt(Q,r,a,u)});if(O&&(d=Os(r,n[0].ownerDocument,!1,n,u),h=d.firstChild,d.childNodes.length===1&&(d=h),h||u)){for(p=c.map(Ae(d,"script"),Ol),b=p.length;T<O;T++)y=d,T!==C&&(y=c.clone(y,!0,!0),b&&c.merge(p,Ae(y,"script"))),a.call(n[T],y,T);if(b)for(_=p[p.length-1].ownerDocument,c.map(p,Il),T=0;T<b;T++)y=p[T],Rs.test(y.type||"")&&!q.access(y,"globalEval")&&c.contains(_,y)&&(y.src&&(y.type||"").toLowerCase()!=="module"?c._evalUrl&&!y.noModule&&c._evalUrl(y.src,{nonce:y.nonce||y.getAttribute("nonce")},_):U(y.textContent.replace(Rl,""),y,_))}return n}function js(n,r,a){for(var u,d=r?c.filter(r,n):n,h=0;(u=d[h])!=null;h++)!a&&u.nodeType===1&&c.cleanData(Ae(u)),u.parentNode&&(a&&Xt(u)&&zn(Ae(u,"script")),u.parentNode.removeChild(u));return n}c.extend({htmlPrefilter:function(n){return n},clone:function(n,r,a){var u,d,h,p,b=n.cloneNode(!0),y=Xt(n);if(!P.noCloneChecked&&(n.nodeType===1||n.nodeType===11)&&!c.isXMLDoc(n))for(p=Ae(b),h=Ae(n),u=0,d=h.length;u<d;u++)Nl(h[u],p[u]);if(r)if(a)for(h=h||Ae(n),p=p||Ae(b),u=0,d=h.length;u<d;u++)Ms(h[u],p[u]);else Ms(n,b);return p=Ae(b,"script"),p.length>0&&zn(p,!y&&Ae(n,"script")),b},cleanData:function(n){for(var r,a,u,d=c.event.special,h=0;(a=n[h])!==void 0;h++)if(di(a)){if(r=a[q.expando]){if(r.events)for(u in r.events)d[u]?c.event.remove(a,u):c.removeEvent(a,u,r.handle);a[q.expando]=void 0}a[Ce.expando]&&(a[Ce.expando]=void 0)}}}),c.fn.extend({detach:function(n){return js(this,n,!0)},remove:function(n){return js(this,n)},text:function(n){return lt(this,function(r){return r===void 0?c.text(this):this.empty().each(function(){(this.nodeType===1||this.nodeType===11||this.nodeType===9)&&(this.textContent=r)})},null,n,arguments.length)},append:function(){return Qt(this,arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var r=Ns(this,n);r.appendChild(n)}})},prepend:function(){return Qt(this,arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var r=Ns(this,n);r.insertBefore(n,r.firstChild)}})},before:function(){return Qt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this)})},after:function(){return Qt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling)})},empty:function(){for(var n,r=0;(n=this[r])!=null;r++)n.nodeType===1&&(c.cleanData(Ae(n,!1)),n.textContent="");return this},clone:function(n,r){return n=n==null?!1:n,r=r==null?n:r,this.map(function(){return c.clone(this,n,r)})},html:function(n){return lt(this,function(r){var a=this[0]||{},u=0,d=this.length;if(r===void 0&&a.nodeType===1)return a.innerHTML;if(typeof r=="string"&&!Ll.test(r)&&!Ne[(Ds.exec(r)||["",""])[1].toLowerCase()]){r=c.htmlPrefilter(r);try{for(;u<d;u++)a=this[u]||{},a.nodeType===1&&(c.cleanData(Ae(a,!1)),a.innerHTML=r);a=0}catch(h){}}a&&this.empty().append(r)},null,n,arguments.length)},replaceWith:function(){var n=[];return Qt(this,arguments,function(r){var a=this.parentNode;c.inArray(this,n)<0&&(c.cleanData(Ae(this)),a&&a.replaceChild(r,this))},n)}}),c.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(n,r){c.fn[n]=function(a){for(var u,d=[],h=c(a),p=h.length-1,b=0;b<=p;b++)u=b===p?this:this.clone(!0),c(h[b])[r](u),f.apply(d,u.get());return this.pushStack(d)}});var Kn=new RegExp("^("+ks+")(?!px)[a-z%]+$","i"),Gn=/^--/,Gi=function(n){var r=n.ownerDocument.defaultView;return(!r||!r.opener)&&(r=e),r.getComputedStyle(n)},Hs=function(n,r,a){var u,d,h={};for(d in r)h[d]=n.style[d],n.style[d]=r[d];u=a.call(n);for(d in r)n.style[d]=h[d];return u},Ml=new RegExp(ut.join("|"),"i");(function(){function n(){if(!!_){y.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",_.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Lt.appendChild(y).appendChild(_);var T=e.getComputedStyle(_);a=T.top!=="1%",b=r(T.marginLeft)===12,_.style.right="60%",h=r(T.right)===36,u=r(T.width)===36,_.style.position="absolute",d=r(_.offsetWidth/3)===12,Lt.removeChild(y),_=null}}function r(T){return Math.round(parseFloat(T))}var a,u,d,h,p,b,y=I.createElement("div"),_=I.createElement("div");!_.style||(_.style.backgroundClip="content-box",_.cloneNode(!0).style.backgroundClip="",P.clearCloneStyle=_.style.backgroundClip==="content-box",c.extend(P,{boxSizingReliable:function(){return n(),u},pixelBoxStyles:function(){return n(),h},pixelPosition:function(){return n(),a},reliableMarginLeft:function(){return n(),b},scrollboxSize:function(){return n(),d},reliableTrDimensions:function(){var T,O,C,M;return p==null&&(T=I.createElement("table"),O=I.createElement("tr"),C=I.createElement("div"),T.style.cssText="position:absolute;left:-11111px;border-collapse:separate",O.style.cssText="box-sizing:content-box;border:1px solid",O.style.height="1px",C.style.height="9px",C.style.display="block",Lt.appendChild(T).appendChild(O).appendChild(C),M=e.getComputedStyle(O),p=parseInt(M.height,10)+parseInt(M.borderTopWidth,10)+parseInt(M.borderBottomWidth,10)===O.offsetHeight,Lt.removeChild(T)),p}}))})();function vi(n,r,a){var u,d,h,p,b=Gn.test(r),y=n.style;return a=a||Gi(n),a&&(p=a.getPropertyValue(r)||a[r],b&&p&&(p=p.replace(vt,"$1")||void 0),p===""&&!Xt(n)&&(p=c.style(n,r)),!P.pixelBoxStyles()&&Kn.test(p)&&Ml.test(r)&&(u=y.width,d=y.minWidth,h=y.maxWidth,y.minWidth=y.maxWidth=y.width=p,p=a.width,y.width=u,y.minWidth=d,y.maxWidth=h)),p!==void 0?p+"":p}function $s(n,r){return{get:function(){if(n()){delete this.get;return}return(this.get=r).apply(this,arguments)}}}var Fs=["Webkit","Moz","ms"],Us=I.createElement("div").style,Bs={};function jl(n){for(var r=n[0].toUpperCase()+n.slice(1),a=Fs.length;a--;)if(n=Fs[a]+r,n in Us)return n}function Yn(n){var r=c.cssProps[n]||Bs[n];return r||(n in Us?n:Bs[n]=jl(n)||n)}var Hl=/^(none|table(?!-c[ea]).+)/,$l={position:"absolute",visibility:"hidden",display:"block"},qs={letterSpacing:"0",fontWeight:"400"};function Ws(n,r,a){var u=pi.exec(r);return u?Math.max(0,u[2]-(a||0))+(u[3]||"px"):r}function Qn(n,r,a,u,d,h){var p=r==="width"?1:0,b=0,y=0,_=0;if(a===(u?"border":"content"))return 0;for(;p<4;p+=2)a==="margin"&&(_+=c.css(n,a+ut[p],!0,d)),u?(a==="content"&&(y-=c.css(n,"padding"+ut[p],!0,d)),a!=="margin"&&(y-=c.css(n,"border"+ut[p]+"Width",!0,d))):(y+=c.css(n,"padding"+ut[p],!0,d),a!=="padding"?y+=c.css(n,"border"+ut[p]+"Width",!0,d):b+=c.css(n,"border"+ut[p]+"Width",!0,d));return!u&&h>=0&&(y+=Math.max(0,Math.ceil(n["offset"+r[0].toUpperCase()+r.slice(1)]-h-y-b-.5))||0),y+_}function Vs(n,r,a){var u=Gi(n),d=!P.boxSizingReliable()||a,h=d&&c.css(n,"boxSizing",!1,u)==="border-box",p=h,b=vi(n,r,u),y="offset"+r[0].toUpperCase()+r.slice(1);if(Kn.test(b)){if(!a)return b;b="auto"}return(!P.boxSizingReliable()&&h||!P.reliableTrDimensions()&&ae(n,"tr")||b==="auto"||!parseFloat(b)&&c.css(n,"display",!1,u)==="inline")&&n.getClientRects().length&&(h=c.css(n,"boxSizing",!1,u)==="border-box",p=y in n,p&&(b=n[y])),b=parseFloat(b)||0,b+Qn(n,r,a||(h?"border":"content"),p,u,b)+"px"}c.extend({cssHooks:{opacity:{get:function(n,r){if(r){var a=vi(n,"opacity");return a===""?"1":a}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(n,r,a,u){if(!(!n||n.nodeType===3||n.nodeType===8||!n.style)){var d,h,p,b=Ke(r),y=Gn.test(r),_=n.style;if(y||(r=Yn(b)),p=c.cssHooks[r]||c.cssHooks[b],a!==void 0){if(h=typeof a,h==="string"&&(d=pi.exec(a))&&d[1]&&(a=Ps(n,r,d),h="number"),a==null||a!==a)return;h==="number"&&!y&&(a+=d&&d[3]||(c.cssNumber[b]?"":"px")),!P.clearCloneStyle&&a===""&&r.indexOf("background")===0&&(_[r]="inherit"),(!p||!("set"in p)||(a=p.set(n,a,u))!==void 0)&&(y?_.setProperty(r,a):_[r]=a)}else return p&&"get"in p&&(d=p.get(n,!1,u))!==void 0?d:_[r]}},css:function(n,r,a,u){var d,h,p,b=Ke(r),y=Gn.test(r);return y||(r=Yn(b)),p=c.cssHooks[r]||c.cssHooks[b],p&&"get"in p&&(d=p.get(n,!0,a)),d===void 0&&(d=vi(n,r,u)),d==="normal"&&r in qs&&(d=qs[r]),a===""||a?(h=parseFloat(d),a===!0||isFinite(h)?h||0:d):d}}),c.each(["height","width"],function(n,r){c.cssHooks[r]={get:function(a,u,d){if(u)return Hl.test(c.css(a,"display"))&&(!a.getClientRects().length||!a.getBoundingClientRect().width)?Hs(a,$l,function(){return Vs(a,r,d)}):Vs(a,r,d)},set:function(a,u,d){var h,p=Gi(a),b=!P.scrollboxSize()&&p.position==="absolute",y=b||d,_=y&&c.css(a,"boxSizing",!1,p)==="border-box",T=d?Qn(a,r,d,_,p):0;return _&&b&&(T-=Math.ceil(a["offset"+r[0].toUpperCase()+r.slice(1)]-parseFloat(p[r])-Qn(a,r,"border",!1,p)-.5)),T&&(h=pi.exec(u))&&(h[3]||"px")!=="px"&&(a.style[r]=u,u=c.css(a,r)),Ws(a,u,T)}}}),c.cssHooks.marginLeft=$s(P.reliableMarginLeft,function(n,r){if(r)return(parseFloat(vi(n,"marginLeft"))||n.getBoundingClientRect().left-Hs(n,{marginLeft:0},function(){return n.getBoundingClientRect().left}))+"px"}),c.each({margin:"",padding:"",border:"Width"},function(n,r){c.cssHooks[n+r]={expand:function(a){for(var u=0,d={},h=typeof a=="string"?a.split(" "):[a];u<4;u++)d[n+ut[u]+r]=h[u]||h[u-2]||h[0];return d}},n!=="margin"&&(c.cssHooks[n+r].set=Ws)}),c.fn.extend({css:function(n,r){return lt(this,function(a,u,d){var h,p,b={},y=0;if(Array.isArray(u)){for(h=Gi(a),p=u.length;y<p;y++)b[u[y]]=c.css(a,u[y],!1,h);return b}return d!==void 0?c.style(a,u,d):c.css(a,u)},n,r,arguments.length>1)}});function Te(n,r,a,u,d){return new Te.prototype.init(n,r,a,u,d)}c.Tween=Te,Te.prototype={constructor:Te,init:function(n,r,a,u,d,h){this.elem=n,this.prop=a,this.easing=d||c.easing._default,this.options=r,this.start=this.now=this.cur(),this.end=u,this.unit=h||(c.cssNumber[a]?"":"px")},cur:function(){var n=Te.propHooks[this.prop];return n&&n.get?n.get(this):Te.propHooks._default.get(this)},run:function(n){var r,a=Te.propHooks[this.prop];return this.options.duration?this.pos=r=c.easing[this.easing](n,this.options.duration*n,0,1,this.options.duration):this.pos=r=n,this.now=(this.end-this.start)*r+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),a&&a.set?a.set(this):Te.propHooks._default.set(this),this}},Te.prototype.init.prototype=Te.prototype,Te.propHooks={_default:{get:function(n){var r;return n.elem.nodeType!==1||n.elem[n.prop]!=null&&n.elem.style[n.prop]==null?n.elem[n.prop]:(r=c.css(n.elem,n.prop,""),!r||r==="auto"?0:r)},set:function(n){c.fx.step[n.prop]?c.fx.step[n.prop](n):n.elem.nodeType===1&&(c.cssHooks[n.prop]||n.elem.style[Yn(n.prop)]!=null)?c.style(n.elem,n.prop,n.now+n.unit):n.elem[n.prop]=n.now}}},Te.propHooks.scrollTop=Te.propHooks.scrollLeft={set:function(n){n.elem.nodeType&&n.elem.parentNode&&(n.elem[n.prop]=n.now)}},c.easing={linear:function(n){return n},swing:function(n){return .5-Math.cos(n*Math.PI)/2},_default:"swing"},c.fx=Te.prototype.init,c.fx.step={};var Zt,Yi,Fl=/^(?:toggle|show|hide)$/,Ul=/queueHooks$/;function Zn(){Yi&&(I.hidden===!1&&e.requestAnimationFrame?e.requestAnimationFrame(Zn):e.setTimeout(Zn,c.fx.interval),c.fx.tick())}function Js(){return e.setTimeout(function(){Zt=void 0}),Zt=Date.now()}function Qi(n,r){var a,u=0,d={height:n};for(r=r?1:0;u<4;u+=2-r)a=ut[u],d["margin"+a]=d["padding"+a]=n;return r&&(d.opacity=d.width=n),d}function zs(n,r,a){for(var u,d=(Ue.tweeners[r]||[]).concat(Ue.tweeners["*"]),h=0,p=d.length;h<p;h++)if(u=d[h].call(a,r,n))return u}function Bl(n,r,a){var u,d,h,p,b,y,_,T,O="width"in r||"height"in r,C=this,M={},X=n.style,ne=n.nodeType&&Xi(n),Q=q.get(n,"fxshow");a.queue||(p=c._queueHooks(n,"fx"),p.unqueued==null&&(p.unqueued=0,b=p.empty.fire,p.empty.fire=function(){p.unqueued||b()}),p.unqueued++,C.always(function(){C.always(function(){p.unqueued--,c.queue(n,"fx").length||p.empty.fire()})}));for(u in r)if(d=r[u],Fl.test(d)){if(delete r[u],h=h||d==="toggle",d===(ne?"hide":"show"))if(d==="show"&&Q&&Q[u]!==void 0)ne=!0;else continue;M[u]=Q&&Q[u]||c.style(n,u)}if(y=!c.isEmptyObject(r),!(!y&&c.isEmptyObject(M))){O&&n.nodeType===1&&(a.overflow=[X.overflow,X.overflowX,X.overflowY],_=Q&&Q.display,_==null&&(_=q.get(n,"display")),T=c.css(n,"display"),T==="none"&&(_?T=_:(Kt([n],!0),_=n.style.display||_,T=c.css(n,"display"),Kt([n]))),(T==="inline"||T==="inline-block"&&_!=null)&&c.css(n,"float")==="none"&&(y||(C.done(function(){X.display=_}),_==null&&(T=X.display,_=T==="none"?"":T)),X.display="inline-block")),a.overflow&&(X.overflow="hidden",C.always(function(){X.overflow=a.overflow[0],X.overflowX=a.overflow[1],X.overflowY=a.overflow[2]})),y=!1;for(u in M)y||(Q?"hidden"in Q&&(ne=Q.hidden):Q=q.access(n,"fxshow",{display:_}),h&&(Q.hidden=!ne),ne&&Kt([n],!0),C.done(function(){ne||Kt([n]),q.remove(n,"fxshow");for(u in M)c.style(n,u,M[u])})),y=zs(ne?Q[u]:0,u,C),u in Q||(Q[u]=y.start,ne&&(y.end=y.start,y.start=0))}}function ql(n,r){var a,u,d,h,p;for(a in n)if(u=Ke(a),d=r[u],h=n[a],Array.isArray(h)&&(d=h[1],h=n[a]=h[0]),a!==u&&(n[u]=h,delete n[a]),p=c.cssHooks[u],p&&"expand"in p){h=p.expand(h),delete n[u];for(a in h)a in n||(n[a]=h[a],r[a]=d)}else r[u]=d}function Ue(n,r,a){var u,d,h=0,p=Ue.prefilters.length,b=c.Deferred().always(function(){delete y.elem}),y=function(){if(d)return!1;for(var O=Zt||Js(),C=Math.max(0,_.startTime+_.duration-O),M=C/_.duration||0,X=1-M,ne=0,Q=_.tweens.length;ne<Q;ne++)_.tweens[ne].run(X);return b.notifyWith(n,[_,X,C]),X<1&&Q?C:(Q||b.notifyWith(n,[_,1,0]),b.resolveWith(n,[_]),!1)},_=b.promise({elem:n,props:c.extend({},r),opts:c.extend(!0,{specialEasing:{},easing:c.easing._default},a),originalProperties:r,originalOptions:a,startTime:Zt||Js(),duration:a.duration,tweens:[],createTween:function(O,C){var M=c.Tween(n,_.opts,O,C,_.opts.specialEasing[O]||_.opts.easing);return _.tweens.push(M),M},stop:function(O){var C=0,M=O?_.tweens.length:0;if(d)return this;for(d=!0;C<M;C++)_.tweens[C].run(1);return O?(b.notifyWith(n,[_,1,0]),b.resolveWith(n,[_,O])):b.rejectWith(n,[_,O]),this}}),T=_.props;for(ql(T,_.opts.specialEasing);h<p;h++)if(u=Ue.prefilters[h].call(_,n,T,_.opts),u)return j(u.stop)&&(c._queueHooks(_.elem,_.opts.queue).stop=u.stop.bind(u)),u;return c.map(T,zs,_),j(_.opts.start)&&_.opts.start.call(n,_),_.progress(_.opts.progress).done(_.opts.done,_.opts.complete).fail(_.opts.fail).always(_.opts.always),c.fx.timer(c.extend(y,{elem:n,anim:_,queue:_.opts.queue})),_}c.Animation=c.extend(Ue,{tweeners:{"*":[function(n,r){var a=this.createTween(n,r);return Ps(a.elem,n,pi.exec(r),a),a}]},tweener:function(n,r){j(n)?(r=n,n=["*"]):n=n.match(Ee);for(var a,u=0,d=n.length;u<d;u++)a=n[u],Ue.tweeners[a]=Ue.tweeners[a]||[],Ue.tweeners[a].unshift(r)},prefilters:[Bl],prefilter:function(n,r){r?Ue.prefilters.unshift(n):Ue.prefilters.push(n)}}),c.speed=function(n,r,a){var u=n&&typeof n=="object"?c.extend({},n):{complete:a||!a&&r||j(n)&&n,duration:n,easing:a&&r||r&&!j(r)&&r};return c.fx.off?u.duration=0:typeof u.duration!="number"&&(u.duration in c.fx.speeds?u.duration=c.fx.speeds[u.duration]:u.duration=c.fx.speeds._default),(u.queue==null||u.queue===!0)&&(u.queue="fx"),u.old=u.complete,u.complete=function(){j(u.old)&&u.old.call(this),u.queue&&c.dequeue(this,u.queue)},u},c.fn.extend({fadeTo:function(n,r,a,u){return this.filter(Xi).css("opacity",0).show().end().animate({opacity:r},n,a,u)},animate:function(n,r,a,u){var d=c.isEmptyObject(n),h=c.speed(r,a,u),p=function(){var b=Ue(this,c.extend({},n),h);(d||q.get(this,"finish"))&&b.stop(!0)};return p.finish=p,d||h.queue===!1?this.each(p):this.queue(h.queue,p)},stop:function(n,r,a){var u=function(d){var h=d.stop;delete d.stop,h(a)};return typeof n!="string"&&(a=r,r=n,n=void 0),r&&this.queue(n||"fx",[]),this.each(function(){var d=!0,h=n!=null&&n+"queueHooks",p=c.timers,b=q.get(this);if(h)b[h]&&b[h].stop&&u(b[h]);else for(h in b)b[h]&&b[h].stop&&Ul.test(h)&&u(b[h]);for(h=p.length;h--;)p[h].elem===this&&(n==null||p[h].queue===n)&&(p[h].anim.stop(a),d=!1,p.splice(h,1));(d||!a)&&c.dequeue(this,n)})},finish:function(n){return n!==!1&&(n=n||"fx"),this.each(function(){var r,a=q.get(this),u=a[n+"queue"],d=a[n+"queueHooks"],h=c.timers,p=u?u.length:0;for(a.finish=!0,c.queue(this,n,[]),d&&d.stop&&d.stop.call(this,!0),r=h.length;r--;)h[r].elem===this&&h[r].queue===n&&(h[r].anim.stop(!0),h.splice(r,1));for(r=0;r<p;r++)u[r]&&u[r].finish&&u[r].finish.call(this);delete a.finish})}}),c.each(["toggle","show","hide"],function(n,r){var a=c.fn[r];c.fn[r]=function(u,d,h){return u==null||typeof u=="boolean"?a.apply(this,arguments):this.animate(Qi(r,!0),u,d,h)}}),c.each({slideDown:Qi("show"),slideUp:Qi("hide"),slideToggle:Qi("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(n,r){c.fn[n]=function(a,u,d){return this.animate(r,a,u,d)}}),c.timers=[],c.fx.tick=function(){var n,r=0,a=c.timers;for(Zt=Date.now();r<a.length;r++)n=a[r],!n()&&a[r]===n&&a.splice(r--,1);a.length||c.fx.stop(),Zt=void 0},c.fx.timer=function(n){c.timers.push(n),c.fx.start()},c.fx.interval=13,c.fx.start=function(){Yi||(Yi=!0,Zn())},c.fx.stop=function(){Yi=null},c.fx.speeds={slow:600,fast:200,_default:400},c.fn.delay=function(n,r){return n=c.fx&&c.fx.speeds[n]||n,r=r||"fx",this.queue(r,function(a,u){var d=e.setTimeout(a,n);u.stop=function(){e.clearTimeout(d)}})},function(){var n=I.createElement("input"),r=I.createElement("select"),a=r.appendChild(I.createElement("option"));n.type="checkbox",P.checkOn=n.value!=="",P.optSelected=a.selected,n=I.createElement("input"),n.value="t",n.type="radio",P.radioValue=n.value==="t"}();var Xs,mi=c.expr.attrHandle;c.fn.extend({attr:function(n,r){return lt(this,c.attr,n,r,arguments.length>1)},removeAttr:function(n){return this.each(function(){c.removeAttr(this,n)})}}),c.extend({attr:function(n,r,a){var u,d,h=n.nodeType;if(!(h===3||h===8||h===2)){if(typeof n.getAttribute=="undefined")return c.prop(n,r,a);if((h!==1||!c.isXMLDoc(n))&&(d=c.attrHooks[r.toLowerCase()]||(c.expr.match.bool.test(r)?Xs:void 0)),a!==void 0){if(a===null){c.removeAttr(n,r);return}return d&&"set"in d&&(u=d.set(n,a,r))!==void 0?u:(n.setAttribute(r,a+""),a)}return d&&"get"in d&&(u=d.get(n,r))!==null?u:(u=c.find.attr(n,r),u==null?void 0:u)}},attrHooks:{type:{set:function(n,r){if(!P.radioValue&&r==="radio"&&ae(n,"input")){var a=n.value;return n.setAttribute("type",r),a&&(n.value=a),r}}}},removeAttr:function(n,r){var a,u=0,d=r&&r.match(Ee);if(d&&n.nodeType===1)for(;a=d[u++];)n.removeAttribute(a)}}),Xs={set:function(n,r,a){return r===!1?c.removeAttr(n,a):n.setAttribute(a,a),a}},c.each(c.expr.match.bool.source.match(/\w+/g),function(n,r){var a=mi[r]||c.find.attr;mi[r]=function(u,d,h){var p,b,y=d.toLowerCase();return h||(b=mi[y],mi[y]=p,p=a(u,d,h)!=null?y:null,mi[y]=b),p}});var Wl=/^(?:input|select|textarea|button)$/i,Vl=/^(?:a|area)$/i;c.fn.extend({prop:function(n,r){return lt(this,c.prop,n,r,arguments.length>1)},removeProp:function(n){return this.each(function(){delete this[c.propFix[n]||n]})}}),c.extend({prop:function(n,r,a){var u,d,h=n.nodeType;if(!(h===3||h===8||h===2))return(h!==1||!c.isXMLDoc(n))&&(r=c.propFix[r]||r,d=c.propHooks[r]),a!==void 0?d&&"set"in d&&(u=d.set(n,a,r))!==void 0?u:n[r]=a:d&&"get"in d&&(u=d.get(n,r))!==null?u:n[r]},propHooks:{tabIndex:{get:function(n){var r=c.find.attr(n,"tabindex");return r?parseInt(r,10):Wl.test(n.nodeName)||Vl.test(n.nodeName)&&n.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),P.optSelected||(c.propHooks.selected={get:function(n){var r=n.parentNode;return r&&r.parentNode&&r.parentNode.selectedIndex,null},set:function(n){var r=n.parentNode;r&&(r.selectedIndex,r.parentNode&&r.parentNode.selectedIndex)}}),c.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){c.propFix[this.toLowerCase()]=this});function Dt(n){var r=n.match(Ee)||[];return r.join(" ")}function Rt(n){return n.getAttribute&&n.getAttribute("class")||""}function er(n){return Array.isArray(n)?n:typeof n=="string"?n.match(Ee)||[]:[]}c.fn.extend({addClass:function(n){var r,a,u,d,h,p;return j(n)?this.each(function(b){c(this).addClass(n.call(this,b,Rt(this)))}):(r=er(n),r.length?this.each(function(){if(u=Rt(this),a=this.nodeType===1&&" "+Dt(u)+" ",a){for(h=0;h<r.length;h++)d=r[h],a.indexOf(" "+d+" ")<0&&(a+=d+" ");p=Dt(a),u!==p&&this.setAttribute("class",p)}}):this)},removeClass:function(n){var r,a,u,d,h,p;return j(n)?this.each(function(b){c(this).removeClass(n.call(this,b,Rt(this)))}):arguments.length?(r=er(n),r.length?this.each(function(){if(u=Rt(this),a=this.nodeType===1&&" "+Dt(u)+" ",a){for(h=0;h<r.length;h++)for(d=r[h];a.indexOf(" "+d+" ")>-1;)a=a.replace(" "+d+" "," ");p=Dt(a),u!==p&&this.setAttribute("class",p)}}):this):this.attr("class","")},toggleClass:function(n,r){var a,u,d,h,p=typeof n,b=p==="string"||Array.isArray(n);return j(n)?this.each(function(y){c(this).toggleClass(n.call(this,y,Rt(this),r),r)}):typeof r=="boolean"&&b?r?this.addClass(n):this.removeClass(n):(a=er(n),this.each(function(){if(b)for(h=c(this),d=0;d<a.length;d++)u=a[d],h.hasClass(u)?h.removeClass(u):h.addClass(u);else(n===void 0||p==="boolean")&&(u=Rt(this),u&&q.set(this,"__className__",u),this.setAttribute&&this.setAttribute("class",u||n===!1?"":q.get(this,"__className__")||""))}))},hasClass:function(n){var r,a,u=0;for(r=" "+n+" ";a=this[u++];)if(a.nodeType===1&&(" "+Dt(Rt(a))+" ").indexOf(r)>-1)return!0;return!1}});var Jl=/\r/g;c.fn.extend({val:function(n){var r,a,u,d=this[0];return arguments.length?(u=j(n),this.each(function(h){var p;this.nodeType===1&&(u?p=n.call(this,h,c(this).val()):p=n,p==null?p="":typeof p=="number"?p+="":Array.isArray(p)&&(p=c.map(p,function(b){return b==null?"":b+""})),r=c.valHooks[this.type]||c.valHooks[this.nodeName.toLowerCase()],(!r||!("set"in r)||r.set(this,p,"value")===void 0)&&(this.value=p))})):d?(r=c.valHooks[d.type]||c.valHooks[d.nodeName.toLowerCase()],r&&"get"in r&&(a=r.get(d,"value"))!==void 0?a:(a=d.value,typeof a=="string"?a.replace(Jl,""):a==null?"":a)):void 0}}),c.extend({valHooks:{option:{get:function(n){var r=c.find.attr(n,"value");return r!=null?r:Dt(c.text(n))}},select:{get:function(n){var r,a,u,d=n.options,h=n.selectedIndex,p=n.type==="select-one",b=p?null:[],y=p?h+1:d.length;for(h<0?u=y:u=p?h:0;u<y;u++)if(a=d[u],(a.selected||u===h)&&!a.disabled&&(!a.parentNode.disabled||!ae(a.parentNode,"optgroup"))){if(r=c(a).val(),p)return r;b.push(r)}return b},set:function(n,r){for(var a,u,d=n.options,h=c.makeArray(r),p=d.length;p--;)u=d[p],(u.selected=c.inArray(c.valHooks.option.get(u),h)>-1)&&(a=!0);return a||(n.selectedIndex=-1),h}}}}),c.each(["radio","checkbox"],function(){c.valHooks[this]={set:function(n,r){if(Array.isArray(r))return n.checked=c.inArray(c(n).val(),r)>-1}},P.checkOn||(c.valHooks[this].get=function(n){return n.getAttribute("value")===null?"on":n.value})});var yi=e.location,Ks={guid:Date.now()},tr=/\?/;c.parseXML=function(n){var r,a;if(!n||typeof n!="string")return null;try{r=new e.DOMParser().parseFromString(n,"text/xml")}catch(u){}return a=r&&r.getElementsByTagName("parsererror")[0],(!r||a)&&c.error("Invalid XML: "+(a?c.map(a.childNodes,function(u){return u.textContent}).join(`
`):n)),r};var Gs=/^(?:focusinfocus|focusoutblur)$/,Ys=function(n){n.stopPropagation()};c.extend(c.event,{trigger:function(n,r,a,u){var d,h,p,b,y,_,T,O,C=[a||I],M=S.call(n,"type")?n.type:n,X=S.call(n,"namespace")?n.namespace.split("."):[];if(h=O=p=a=a||I,!(a.nodeType===3||a.nodeType===8)&&!Gs.test(M+c.event.triggered)&&(M.indexOf(".")>-1&&(X=M.split("."),M=X.shift(),X.sort()),y=M.indexOf(":")<0&&"on"+M,n=n[c.expando]?n:new c.Event(M,typeof n=="object"&&n),n.isTrigger=u?2:3,n.namespace=X.join("."),n.rnamespace=n.namespace?new RegExp("(^|\\.)"+X.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,n.result=void 0,n.target||(n.target=a),r=r==null?[n]:c.makeArray(r,[n]),T=c.event.special[M]||{},!(!u&&T.trigger&&T.trigger.apply(a,r)===!1))){if(!u&&!T.noBubble&&!z(a)){for(b=T.delegateType||M,Gs.test(b+M)||(h=h.parentNode);h;h=h.parentNode)C.push(h),p=h;p===(a.ownerDocument||I)&&C.push(p.defaultView||p.parentWindow||e)}for(d=0;(h=C[d++])&&!n.isPropagationStopped();)O=h,n.type=d>1?b:T.bindType||M,_=(q.get(h,"events")||Object.create(null))[n.type]&&q.get(h,"handle"),_&&_.apply(h,r),_=y&&h[y],_&&_.apply&&di(h)&&(n.result=_.apply(h,r),n.result===!1&&n.preventDefault());return n.type=M,!u&&!n.isDefaultPrevented()&&(!T._default||T._default.apply(C.pop(),r)===!1)&&di(a)&&y&&j(a[M])&&!z(a)&&(p=a[y],p&&(a[y]=null),c.event.triggered=M,n.isPropagationStopped()&&O.addEventListener(M,Ys),a[M](),n.isPropagationStopped()&&O.removeEventListener(M,Ys),c.event.triggered=void 0,p&&(a[y]=p)),n.result}},simulate:function(n,r,a){var u=c.extend(new c.Event,a,{type:n,isSimulated:!0});c.event.trigger(u,null,r)}}),c.fn.extend({trigger:function(n,r){return this.each(function(){c.event.trigger(n,r,this)})},triggerHandler:function(n,r){var a=this[0];if(a)return c.event.trigger(n,r,a,!0)}});var zl=/\[\]$/,Qs=/\r?\n/g,Xl=/^(?:submit|button|image|reset|file)$/i,Kl=/^(?:input|select|textarea|keygen)/i;function ir(n,r,a,u){var d;if(Array.isArray(r))c.each(r,function(h,p){a||zl.test(n)?u(n,p):ir(n+"["+(typeof p=="object"&&p!=null?h:"")+"]",p,a,u)});else if(!a&&G(r)==="object")for(d in r)ir(n+"["+d+"]",r[d],a,u);else u(n,r)}c.param=function(n,r){var a,u=[],d=function(h,p){var b=j(p)?p():p;u[u.length]=encodeURIComponent(h)+"="+encodeURIComponent(b==null?"":b)};if(n==null)return"";if(Array.isArray(n)||n.jquery&&!c.isPlainObject(n))c.each(n,function(){d(this.name,this.value)});else for(a in n)ir(a,n[a],r,d);return u.join("&")},c.fn.extend({serialize:function(){return c.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var n=c.prop(this,"elements");return n?c.makeArray(n):this}).filter(function(){var n=this.type;return this.name&&!c(this).is(":disabled")&&Kl.test(this.nodeName)&&!Xl.test(n)&&(this.checked||!gi.test(n))}).map(function(n,r){var a=c(this).val();return a==null?null:Array.isArray(a)?c.map(a,function(u){return{name:r.name,value:u.replace(Qs,`\r
`)}}):{name:r.name,value:a.replace(Qs,`\r
`)}}).get()}});var Gl=/%20/g,Yl=/#.*$/,Ql=/([?&])_=[^&]*/,Zl=/^(.*?):[ \t]*([^\r\n]*)$/mg,eu=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,tu=/^(?:GET|HEAD)$/,iu=/^\/\//,Zs={},nr={},eo="*/".concat("*"),rr=I.createElement("a");rr.href=yi.href;function to(n){return function(r,a){typeof r!="string"&&(a=r,r="*");var u,d=0,h=r.toLowerCase().match(Ee)||[];if(j(a))for(;u=h[d++];)u[0]==="+"?(u=u.slice(1)||"*",(n[u]=n[u]||[]).unshift(a)):(n[u]=n[u]||[]).push(a)}}function io(n,r,a,u){var d={},h=n===nr;function p(b){var y;return d[b]=!0,c.each(n[b]||[],function(_,T){var O=T(r,a,u);if(typeof O=="string"&&!h&&!d[O])return r.dataTypes.unshift(O),p(O),!1;if(h)return!(y=O)}),y}return p(r.dataTypes[0])||!d["*"]&&p("*")}function sr(n,r){var a,u,d=c.ajaxSettings.flatOptions||{};for(a in r)r[a]!==void 0&&((d[a]?n:u||(u={}))[a]=r[a]);return u&&c.extend(!0,n,u),n}function nu(n,r,a){for(var u,d,h,p,b=n.contents,y=n.dataTypes;y[0]==="*";)y.shift(),u===void 0&&(u=n.mimeType||r.getResponseHeader("Content-Type"));if(u){for(d in b)if(b[d]&&b[d].test(u)){y.unshift(d);break}}if(y[0]in a)h=y[0];else{for(d in a){if(!y[0]||n.converters[d+" "+y[0]]){h=d;break}p||(p=d)}h=h||p}if(h)return h!==y[0]&&y.unshift(h),a[h]}function ru(n,r,a,u){var d,h,p,b,y,_={},T=n.dataTypes.slice();if(T[1])for(p in n.converters)_[p.toLowerCase()]=n.converters[p];for(h=T.shift();h;)if(n.responseFields[h]&&(a[n.responseFields[h]]=r),!y&&u&&n.dataFilter&&(r=n.dataFilter(r,n.dataType)),y=h,h=T.shift(),h){if(h==="*")h=y;else if(y!=="*"&&y!==h){if(p=_[y+" "+h]||_["* "+h],!p){for(d in _)if(b=d.split(" "),b[1]===h&&(p=_[y+" "+b[0]]||_["* "+b[0]],p)){p===!0?p=_[d]:_[d]!==!0&&(h=b[0],T.unshift(b[1]));break}}if(p!==!0)if(p&&n.throws)r=p(r);else try{r=p(r)}catch(O){return{state:"parsererror",error:p?O:"No conversion from "+y+" to "+h}}}}return{state:"success",data:r}}c.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:yi.href,type:"GET",isLocal:eu.test(yi.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":eo,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":c.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(n,r){return r?sr(sr(n,c.ajaxSettings),r):sr(c.ajaxSettings,n)},ajaxPrefilter:to(Zs),ajaxTransport:to(nr),ajax:function(n,r){typeof n=="object"&&(r=n,n=void 0),r=r||{};var a,u,d,h,p,b,y,_,T,O,C=c.ajaxSetup({},r),M=C.context||C,X=C.context&&(M.nodeType||M.jquery)?c(M):c.event,ne=c.Deferred(),Q=c.Callbacks("once memory"),ye=C.statusCode||{},ge={},Ge={},Ye="canceled",ie={readyState:0,getResponseHeader:function(se){var he;if(y){if(!h)for(h={};he=Zl.exec(d);)h[he[1].toLowerCase()+" "]=(h[he[1].toLowerCase()+" "]||[]).concat(he[2]);he=h[se.toLowerCase()+" "]}return he==null?null:he.join(", ")},getAllResponseHeaders:function(){return y?d:null},setRequestHeader:function(se,he){return y==null&&(se=Ge[se.toLowerCase()]=Ge[se.toLowerCase()]||se,ge[se]=he),this},overrideMimeType:function(se){return y==null&&(C.mimeType=se),this},statusCode:function(se){var he;if(se)if(y)ie.always(se[ie.status]);else for(he in se)ye[he]=[ye[he],se[he]];return this},abort:function(se){var he=se||Ye;return a&&a.abort(he),Ot(0,he),this}};if(ne.promise(ie),C.url=((n||C.url||yi.href)+"").replace(iu,yi.protocol+"//"),C.type=r.method||r.type||C.method||C.type,C.dataTypes=(C.dataType||"*").toLowerCase().match(Ee)||[""],C.crossDomain==null){b=I.createElement("a");try{b.href=C.url,b.href=b.href,C.crossDomain=rr.protocol+"//"+rr.host!=b.protocol+"//"+b.host}catch(se){C.crossDomain=!0}}if(C.data&&C.processData&&typeof C.data!="string"&&(C.data=c.param(C.data,C.traditional)),io(Zs,C,r,ie),y)return ie;_=c.event&&C.global,_&&c.active++===0&&c.event.trigger("ajaxStart"),C.type=C.type.toUpperCase(),C.hasContent=!tu.test(C.type),u=C.url.replace(Yl,""),C.hasContent?C.data&&C.processData&&(C.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(C.data=C.data.replace(Gl,"+")):(O=C.url.slice(u.length),C.data&&(C.processData||typeof C.data=="string")&&(u+=(tr.test(u)?"&":"?")+C.data,delete C.data),C.cache===!1&&(u=u.replace(Ql,"$1"),O=(tr.test(u)?"&":"?")+"_="+Ks.guid+++O),C.url=u+O),C.ifModified&&(c.lastModified[u]&&ie.setRequestHeader("If-Modified-Since",c.lastModified[u]),c.etag[u]&&ie.setRequestHeader("If-None-Match",c.etag[u])),(C.data&&C.hasContent&&C.contentType!==!1||r.contentType)&&ie.setRequestHeader("Content-Type",C.contentType),ie.setRequestHeader("Accept",C.dataTypes[0]&&C.accepts[C.dataTypes[0]]?C.accepts[C.dataTypes[0]]+(C.dataTypes[0]!=="*"?", "+eo+"; q=0.01":""):C.accepts["*"]);for(T in C.headers)ie.setRequestHeader(T,C.headers[T]);if(C.beforeSend&&(C.beforeSend.call(M,ie,C)===!1||y))return ie.abort();if(Ye="abort",Q.add(C.complete),ie.done(C.success),ie.fail(C.error),a=io(nr,C,r,ie),!a)Ot(-1,"No Transport");else{if(ie.readyState=1,_&&X.trigger("ajaxSend",[ie,C]),y)return ie;C.async&&C.timeout>0&&(p=e.setTimeout(function(){ie.abort("timeout")},C.timeout));try{y=!1,a.send(ge,Ot)}catch(se){if(y)throw se;Ot(-1,se)}}function Ot(se,he,xi,ar){var Qe,_i,Ze,yt,bt,Me=he;y||(y=!0,p&&e.clearTimeout(p),a=void 0,d=ar||"",ie.readyState=se>0?4:0,Qe=se>=200&&se<300||se===304,xi&&(yt=nu(C,ie,xi)),!Qe&&c.inArray("script",C.dataTypes)>-1&&c.inArray("json",C.dataTypes)<0&&(C.converters["text script"]=function(){}),yt=ru(C,yt,ie,Qe),Qe?(C.ifModified&&(bt=ie.getResponseHeader("Last-Modified"),bt&&(c.lastModified[u]=bt),bt=ie.getResponseHeader("etag"),bt&&(c.etag[u]=bt)),se===204||C.type==="HEAD"?Me="nocontent":se===304?Me="notmodified":(Me=yt.state,_i=yt.data,Ze=yt.error,Qe=!Ze)):(Ze=Me,(se||!Me)&&(Me="error",se<0&&(se=0))),ie.status=se,ie.statusText=(he||Me)+"",Qe?ne.resolveWith(M,[_i,Me,ie]):ne.rejectWith(M,[ie,Me,Ze]),ie.statusCode(ye),ye=void 0,_&&X.trigger(Qe?"ajaxSuccess":"ajaxError",[ie,C,Qe?_i:Ze]),Q.fireWith(M,[ie,Me]),_&&(X.trigger("ajaxComplete",[ie,C]),--c.active||c.event.trigger("ajaxStop")))}return ie},getJSON:function(n,r,a){return c.get(n,r,a,"json")},getScript:function(n,r){return c.get(n,void 0,r,"script")}}),c.each(["get","post"],function(n,r){c[r]=function(a,u,d,h){return j(u)&&(h=h||d,d=u,u=void 0),c.ajax(c.extend({url:a,type:r,dataType:h,data:u,success:d},c.isPlainObject(a)&&a))}}),c.ajaxPrefilter(function(n){var r;for(r in n.headers)r.toLowerCase()==="content-type"&&(n.contentType=n.headers[r]||"")}),c._evalUrl=function(n,r,a){return c.ajax({url:n,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(u){c.globalEval(u,r,a)}})},c.fn.extend({wrapAll:function(n){var r;return this[0]&&(j(n)&&(n=n.call(this[0])),r=c(n,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&r.insertBefore(this[0]),r.map(function(){for(var a=this;a.firstElementChild;)a=a.firstElementChild;return a}).append(this)),this},wrapInner:function(n){return j(n)?this.each(function(r){c(this).wrapInner(n.call(this,r))}):this.each(function(){var r=c(this),a=r.contents();a.length?a.wrapAll(n):r.append(n)})},wrap:function(n){var r=j(n);return this.each(function(a){c(this).wrapAll(r?n.call(this,a):n)})},unwrap:function(n){return this.parent(n).not("body").each(function(){c(this).replaceWith(this.childNodes)}),this}}),c.expr.pseudos.hidden=function(n){return!c.expr.pseudos.visible(n)},c.expr.pseudos.visible=function(n){return!!(n.offsetWidth||n.offsetHeight||n.getClientRects().length)},c.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(n){}};var su={0:200,1223:204},bi=c.ajaxSettings.xhr();P.cors=!!bi&&"withCredentials"in bi,P.ajax=bi=!!bi,c.ajaxTransport(function(n){var r,a;if(P.cors||bi&&!n.crossDomain)return{send:function(u,d){var h,p=n.xhr();if(p.open(n.type,n.url,n.async,n.username,n.password),n.xhrFields)for(h in n.xhrFields)p[h]=n.xhrFields[h];n.mimeType&&p.overrideMimeType&&p.overrideMimeType(n.mimeType),!n.crossDomain&&!u["X-Requested-With"]&&(u["X-Requested-With"]="XMLHttpRequest");for(h in u)p.setRequestHeader(h,u[h]);r=function(b){return function(){r&&(r=a=p.onload=p.onerror=p.onabort=p.ontimeout=p.onreadystatechange=null,b==="abort"?p.abort():b==="error"?typeof p.status!="number"?d(0,"error"):d(p.status,p.statusText):d(su[p.status]||p.status,p.statusText,(p.responseType||"text")!=="text"||typeof p.responseText!="string"?{binary:p.response}:{text:p.responseText},p.getAllResponseHeaders()))}},p.onload=r(),a=p.onerror=p.ontimeout=r("error"),p.onabort!==void 0?p.onabort=a:p.onreadystatechange=function(){p.readyState===4&&e.setTimeout(function(){r&&a()})},r=r("abort");try{p.send(n.hasContent&&n.data||null)}catch(b){if(r)throw b}},abort:function(){r&&r()}}}),c.ajaxPrefilter(function(n){n.crossDomain&&(n.contents.script=!1)}),c.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(n){return c.globalEval(n),n}}}),c.ajaxPrefilter("script",function(n){n.cache===void 0&&(n.cache=!1),n.crossDomain&&(n.type="GET")}),c.ajaxTransport("script",function(n){if(n.crossDomain||n.scriptAttrs){var r,a;return{send:function(u,d){r=c("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",a=function(h){r.remove(),a=null,h&&d(h.type==="error"?404:200,h.type)}),I.head.appendChild(r[0])},abort:function(){a&&a()}}}});var no=[],or=/(=)\?(?=&|$)|\?\?/;c.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=no.pop()||c.expando+"_"+Ks.guid++;return this[n]=!0,n}}),c.ajaxPrefilter("json jsonp",function(n,r,a){var u,d,h,p=n.jsonp!==!1&&(or.test(n.url)?"url":typeof n.data=="string"&&(n.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&or.test(n.data)&&"data");if(p||n.dataTypes[0]==="jsonp")return u=n.jsonpCallback=j(n.jsonpCallback)?n.jsonpCallback():n.jsonpCallback,p?n[p]=n[p].replace(or,"$1"+u):n.jsonp!==!1&&(n.url+=(tr.test(n.url)?"&":"?")+n.jsonp+"="+u),n.converters["script json"]=function(){return h||c.error(u+" was not called"),h[0]},n.dataTypes[0]="json",d=e[u],e[u]=function(){h=arguments},a.always(function(){d===void 0?c(e).removeProp(u):e[u]=d,n[u]&&(n.jsonpCallback=r.jsonpCallback,no.push(u)),h&&j(d)&&d(h[0]),h=d=void 0}),"script"}),P.createHTMLDocument=function(){var n=I.implementation.createHTMLDocument("").body;return n.innerHTML="<form></form><form></form>",n.childNodes.length===2}(),c.parseHTML=function(n,r,a){if(typeof n!="string")return[];typeof r=="boolean"&&(a=r,r=!1);var u,d,h;return r||(P.createHTMLDocument?(r=I.implementation.createHTMLDocument(""),u=r.createElement("base"),u.href=I.location.href,r.head.appendChild(u)):r=I),d=ue.exec(n),h=!a&&[],d?[r.createElement(d[1])]:(d=Os([n],r,h),h&&h.length&&c(h).remove(),c.merge([],d.childNodes))},c.fn.load=function(n,r,a){var u,d,h,p=this,b=n.indexOf(" ");return b>-1&&(u=Dt(n.slice(b)),n=n.slice(0,b)),j(r)?(a=r,r=void 0):r&&typeof r=="object"&&(d="POST"),p.length>0&&c.ajax({url:n,type:d||"GET",dataType:"html",data:r}).done(function(y){h=arguments,p.html(u?c("<div>").append(c.parseHTML(y)).find(u):y)}).always(a&&function(y,_){p.each(function(){a.apply(this,h||[y.responseText,_,y])})}),this},c.expr.pseudos.animated=function(n){return c.grep(c.timers,function(r){return n===r.elem}).length},c.offset={setOffset:function(n,r,a){var u,d,h,p,b,y,_,T=c.css(n,"position"),O=c(n),C={};T==="static"&&(n.style.position="relative"),b=O.offset(),h=c.css(n,"top"),y=c.css(n,"left"),_=(T==="absolute"||T==="fixed")&&(h+y).indexOf("auto")>-1,_?(u=O.position(),p=u.top,d=u.left):(p=parseFloat(h)||0,d=parseFloat(y)||0),j(r)&&(r=r.call(n,a,c.extend({},b))),r.top!=null&&(C.top=r.top-b.top+p),r.left!=null&&(C.left=r.left-b.left+d),"using"in r?r.using.call(n,C):O.css(C)}},c.fn.extend({offset:function(n){if(arguments.length)return n===void 0?this:this.each(function(d){c.offset.setOffset(this,n,d)});var r,a,u=this[0];if(!!u)return u.getClientRects().length?(r=u.getBoundingClientRect(),a=u.ownerDocument.defaultView,{top:r.top+a.pageYOffset,left:r.left+a.pageXOffset}):{top:0,left:0}},position:function(){if(!!this[0]){var n,r,a,u=this[0],d={top:0,left:0};if(c.css(u,"position")==="fixed")r=u.getBoundingClientRect();else{for(r=this.offset(),a=u.ownerDocument,n=u.offsetParent||a.documentElement;n&&(n===a.body||n===a.documentElement)&&c.css(n,"position")==="static";)n=n.parentNode;n&&n!==u&&n.nodeType===1&&(d=c(n).offset(),d.top+=c.css(n,"borderTopWidth",!0),d.left+=c.css(n,"borderLeftWidth",!0))}return{top:r.top-d.top-c.css(u,"marginTop",!0),left:r.left-d.left-c.css(u,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var n=this.offsetParent;n&&c.css(n,"position")==="static";)n=n.offsetParent;return n||Lt})}}),c.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(n,r){var a=r==="pageYOffset";c.fn[n]=function(u){return lt(this,function(d,h,p){var b;if(z(d)?b=d:d.nodeType===9&&(b=d.defaultView),p===void 0)return b?b[r]:d[h];b?b.scrollTo(a?b.pageXOffset:p,a?p:b.pageYOffset):d[h]=p},n,u,arguments.length)}}),c.each(["top","left"],function(n,r){c.cssHooks[r]=$s(P.pixelPosition,function(a,u){if(u)return u=vi(a,r),Kn.test(u)?c(a).position()[r]+"px":u})}),c.each({Height:"height",Width:"width"},function(n,r){c.each({padding:"inner"+n,content:r,"":"outer"+n},function(a,u){c.fn[u]=function(d,h){var p=arguments.length&&(a||typeof d!="boolean"),b=a||(d===!0||h===!0?"margin":"border");return lt(this,function(y,_,T){var O;return z(y)?u.indexOf("outer")===0?y["inner"+n]:y.document.documentElement["client"+n]:y.nodeType===9?(O=y.documentElement,Math.max(y.body["scroll"+n],O["scroll"+n],y.body["offset"+n],O["offset"+n],O["client"+n])):T===void 0?c.css(y,_,b):c.style(y,_,T,b)},r,p?d:void 0,p)}})}),c.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(n,r){c.fn[r]=function(a){return this.on(r,a)}}),c.fn.extend({bind:function(n,r,a){return this.on(n,null,r,a)},unbind:function(n,r){return this.off(n,null,r)},delegate:function(n,r,a,u){return this.on(r,n,a,u)},undelegate:function(n,r,a){return arguments.length===1?this.off(n,"**"):this.off(r,n||"**",a)},hover:function(n,r){return this.on("mouseenter",n).on("mouseleave",r||n)}}),c.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(n,r){c.fn[r]=function(a,u){return arguments.length>0?this.on(r,null,a,u):this.trigger(r)}});var ou=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;c.proxy=function(n,r){var a,u,d;if(typeof r=="string"&&(a=n[r],r=n,n=a),!!j(n))return u=o.call(arguments,2),d=function(){return n.apply(r||this,u.concat(o.call(arguments)))},d.guid=n.guid=n.guid||c.guid++,d},c.holdReady=function(n){n?c.readyWait++:c.ready(!0)},c.isArray=Array.isArray,c.parseJSON=JSON.parse,c.nodeName=ae,c.isFunction=j,c.isWindow=z,c.camelCase=Ke,c.type=G,c.now=Date.now,c.isNumeric=function(n){var r=c.type(n);return(r==="number"||r==="string")&&!isNaN(n-parseFloat(n))},c.trim=function(n){return n==null?"":(n+"").replace(ou,"$1")},typeof define=="function"&&define.amd&&define("jquery",[],function(){return c});var au=e.jQuery,lu=e.$;return c.noConflict=function(n){return e.$===c&&(e.$=lu),n&&e.jQuery===c&&(e.jQuery=au),c},typeof t=="undefined"&&(e.jQuery=e.$=c),c})});var qo=co((Bo,En)=>{(function(e,t){"use strict";(function(){for(var R=0,P=["ms","moz","webkit","o"],j=0;j<P.length&&!e.requestAnimationFrame;++j)e.requestAnimationFrame=e[P[j]+"RequestAnimationFrame"],e.cancelAnimationFrame=e[P[j]+"CancelAnimationFrame"]||e[P[j]+"CancelRequestAnimationFrame"];e.requestAnimationFrame||(e.requestAnimationFrame=function(z,I){var k=new Date().getTime(),U=Math.max(0,16-(k-R)),G=e.setTimeout(function(){z(k+U)},U);return R=k+U,G}),e.cancelAnimationFrame||(e.cancelAnimationFrame=function(z){clearTimeout(z)})})();var i,s,o,l,f,g=function(R,P,j){R.addEventListener?R.addEventListener(P,j,!1):R.attachEvent?R.attachEvent("on"+P,j):R["on"+P]=j},v={autoRun:!0,barThickness:3,barColors:{0:"rgba(26,  188, 156, .9)",".25":"rgba(52,  152, 219, .9)",".50":"rgba(241, 196, 15,  .9)",".75":"rgba(230, 126, 34,  .9)","1.0":"rgba(211, 84,  0,   .9)"},shadowBlur:10,shadowColor:"rgba(0,   0,   0,   .6)",className:null},w=function(){i.width=e.innerWidth,i.height=v.barThickness*5;var R=i.getContext("2d");R.shadowBlur=v.shadowBlur,R.shadowColor=v.shadowColor;var P=R.createLinearGradient(0,0,i.width,0);for(var j in v.barColors)P.addColorStop(j,v.barColors[j]);R.lineWidth=v.barThickness,R.beginPath(),R.moveTo(0,v.barThickness/2),R.lineTo(Math.ceil(l*i.width),v.barThickness/2),R.strokeStyle=P,R.stroke()},S=function(){i=t.createElement("canvas");var R=i.style;R.position="fixed",R.top=R.left=R.right=R.margin=R.padding=0,R.zIndex=100001,R.display="none",v.className&&i.classList.add(v.className),t.body.appendChild(i),g(e,"resize",w)},N={config:function(R){for(var P in R)v.hasOwnProperty(P)&&(v[P]=R[P])},show:function(){f||(f=!0,o!==null&&e.cancelAnimationFrame(o),i||S(),i.style.opacity=1,i.style.display="block",N.progress(0),v.autoRun&&function R(){s=e.requestAnimationFrame(R),N.progress("+"+.05*Math.pow(1-Math.sqrt(l),2))}())},progress:function(R){return typeof R=="undefined"||(typeof R=="string"&&(R=(R.indexOf("+")>=0||R.indexOf("-")>=0?l:0)+parseFloat(R)),l=R>1?1:R,w()),l},hide:function(){!f||(f=!1,s!=null&&(e.cancelAnimationFrame(s),s=null),function R(){if(N.progress("+.1")>=1&&(i.style.opacity-=.05,i.style.opacity<=.05)){i.style.display="none",o=null;return}o=e.requestAnimationFrame(R)}())}};typeof En=="object"&&typeof En.exports=="object"?En.exports=N:typeof define=="function"&&define.amd?define(function(){return N}):this.topbar=N}).call(Bo,window,document)});(function(){var e=t();function t(){if(typeof window.CustomEvent=="function")return window.CustomEvent;function o(l,f){f=f||{bubbles:!1,cancelable:!1,detail:void 0};var g=document.createEvent("CustomEvent");return g.initCustomEvent(l,f.bubbles,f.cancelable,f.detail),g}return o.prototype=window.Event.prototype,o}function i(o,l){var f=document.createElement("input");return f.type="hidden",f.name=o,f.value=l,f}function s(o,l){var f=o.getAttribute("data-to"),g=i("_method",o.getAttribute("data-method")),v=i("_csrf_token",o.getAttribute("data-csrf")),w=document.createElement("form"),S=document.createElement("input"),N=o.getAttribute("target");w.method=o.getAttribute("data-method")==="get"?"get":"post",w.action=f,w.style.display="none",N?w.target=N:l&&(w.target="_blank"),w.appendChild(v),w.appendChild(g),document.body.appendChild(w),S.type="submit",w.appendChild(S),S.click()}window.addEventListener("click",function(o){var l=o.target;if(!o.defaultPrevented)for(;l&&l.getAttribute;){var f=new e("phoenix.link.click",{bubbles:!0,cancelable:!0});if(!l.dispatchEvent(f))return o.preventDefault(),o.stopImmediatePropagation(),!1;if(l.getAttribute("data-method"))return s(l,o.metaKey||o.shiftKey),o.preventDefault(),!1;l=l.parentNode}},!1),window.addEventListener("phoenix.link.click",function(o){var l=o.target.getAttribute("data-confirm");l&&!window.confirm(l)&&o.preventDefault()},!1)})();var $d=vr(mr()),Ud=vr(mr());var Ei=e=>typeof e=="function"?e:function(){return e},_u=typeof self!="undefined"?self:null,Si=typeof window!="undefined"?window:null,Ci=_u||Si||Ci,wu="2.0.0",tt={connecting:0,open:1,closing:2,closed:3},Su=1e4,Eu=1e3,Re={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},ht={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},br={longpoll:"longpoll",websocket:"websocket"},Cu={complete:4},on=class{constructor(e,t,i,s){this.channel=e,this.event=t,this.payload=i||function(){return{}},this.receivedResp=null,this.timeout=s,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(e){this.timeout=e,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(e,t){return this.hasReceived(e)&&t(this.receivedResp.response),this.recHooks.push({status:e,callback:t}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:e,response:t,_ref:i}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}cancelRefEvent(){!this.refEvent||this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,e=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=e,this.matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}trigger(e,t){this.channel.trigger(this.refEvent,{status:e,response:t})}},ho=class{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},Au=class{constructor(e,t,i){this.state=Re.closed,this.topic=e,this.params=Ei(t||{}),this.socket=i,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new on(this,ht.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new ho(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=Re.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=Re.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=Re.closed,this.socket.remove(this)}),this.onError(s=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,s),this.isJoining()&&this.joinPush.reset(),this.state=Re.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new on(this,ht.leave,Ei({}),this.timeout).send(),this.state=Re.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(ht.reply,(s,o)=>{this.trigger(this.replyEventName(o),s)})}join(e=this.timeout){if(this.joinedOnce)throw new Error("tried to join multiple times. 'join' can only be called a single time per channel instance");return this.timeout=e,this.joinedOnce=!0,this.rejoin(),this.joinPush}onClose(e){this.on(ht.close,e)}onError(e){return this.on(ht.error,t=>e(t))}on(e,t){let i=this.bindingRef++;return this.bindings.push({event:e,ref:i,callback:t}),i}off(e,t){this.bindings=this.bindings.filter(i=>!(i.event===e&&(typeof t=="undefined"||t===i.ref)))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,i=this.timeout){if(t=t||{},!this.joinedOnce)throw new Error(`tried to push '${e}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let s=new on(this,e,function(){return t},i);return this.canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}leave(e=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=Re.leaving;let t=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(ht.close,"leave")},i=new on(this,ht.leave,Ei({}),e);return i.receive("ok",()=>t()).receive("timeout",()=>t()),i.send(),this.canPush()||i.trigger("ok",{}),i}onMessage(e,t,i){return t}isMember(e,t,i,s){return this.topic!==e?!1:s&&s!==this.joinRef()?(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:e,event:t,payload:i,joinRef:s}),!1):!0}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=Re.joining,this.joinPush.resend(e))}trigger(e,t,i,s){let o=this.onMessage(e,t,i,s);if(t&&!o)throw new Error("channel onMessage callbacks must return the payload, modified or unmodified");let l=this.bindings.filter(f=>f.event===e);for(let f=0;f<l.length;f++)l[f].callback(o,i,s||this.joinRef())}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===Re.closed}isErrored(){return this.state===Re.errored}isJoined(){return this.state===Re.joined}isJoining(){return this.state===Re.joining}isLeaving(){return this.state===Re.leaving}},ln=class{static request(e,t,i,s,o,l,f){if(Ci.XDomainRequest){let g=new Ci.XDomainRequest;return this.xdomainRequest(g,e,t,s,o,l,f)}else{let g=new Ci.XMLHttpRequest;return this.xhrRequest(g,e,t,i,s,o,l,f)}}static xdomainRequest(e,t,i,s,o,l,f){return e.timeout=o,e.open(t,i),e.onload=()=>{let g=this.parseJSON(e.responseText);f&&f(g)},l&&(e.ontimeout=l),e.onprogress=()=>{},e.send(s),e}static xhrRequest(e,t,i,s,o,l,f,g){return e.open(t,i,!0),e.timeout=l,e.setRequestHeader("Content-Type",s),e.onerror=()=>g&&g(null),e.onreadystatechange=()=>{if(e.readyState===Cu.complete&&g){let v=this.parseJSON(e.responseText);g(v)}},f&&(e.ontimeout=f),e.send(o),e}static parseJSON(e){if(!e||e==="")return null;try{return JSON.parse(e)}catch(t){return console&&console.log("failed to parse JSON response",e),null}}static serialize(e,t){let i=[];for(var s in e){if(!Object.prototype.hasOwnProperty.call(e,s))continue;let o=t?`${t}[${s}]`:s,l=e[s];typeof l=="object"?i.push(this.serialize(l,o)):i.push(encodeURIComponent(o)+"="+encodeURIComponent(l))}return i.join("&")}static appendParams(e,t){if(Object.keys(t).length===0)return e;let i=e.match(/\?/)?"&":"?";return`${e}${i}${this.serialize(t)}`}},yr=class{constructor(e){this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(e),this.readyState=tt.connecting,this.poll()}normalizeEndpoint(e){return e.replace("ws://","http://").replace("wss://","https://").replace(new RegExp("(.*)/"+br.websocket),"$1/"+br.longpoll)}endpointURL(){return ln.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(e,t,i){this.close(e,t,i),this.readyState=tt.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===tt.open||this.readyState===tt.connecting}poll(){this.ajax("GET",null,()=>this.ontimeout(),e=>{if(e){var{status:t,token:i,messages:s}=e;this.token=i}else t=0;switch(t){case 200:s.forEach(o=>{setTimeout(()=>this.onmessage({data:o}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=tt.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw new Error(`unhandled poll status ${t}`)}})}send(e){this.ajax("POST",e,()=>this.onerror("timeout"),t=>{(!t||t.status!==200)&&(this.onerror(t&&t.status),this.closeAndRetry(1011,"internal server error",!1))})}close(e,t,i){for(let o of this.reqs)o.abort();this.readyState=tt.closed;let s=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:e,reason:t,wasClean:i});typeof CloseEvent!="undefined"?this.onclose(new CloseEvent("close",s)):this.onclose(s)}ajax(e,t,i,s){let o,l=()=>{this.reqs.delete(o),i()};o=ln.request(e,this.endpointURL(),"application/json",t,this.timeout,l,f=>{this.reqs.delete(o),this.isActive()&&s(f)}),this.reqs.add(o)}};var an={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(e,t){if(e.payload.constructor===ArrayBuffer)return t(this.binaryEncode(e));{let i=[e.join_ref,e.ref,e.topic,e.event,e.payload];return t(JSON.stringify(i))}},decode(e,t){if(e.constructor===ArrayBuffer)return t(this.binaryDecode(e));{let[i,s,o,l,f]=JSON.parse(e);return t({join_ref:i,ref:s,topic:o,event:l,payload:f})}},binaryEncode(e){let{join_ref:t,ref:i,event:s,topic:o,payload:l}=e,f=this.META_LENGTH+t.length+i.length+o.length+s.length,g=new ArrayBuffer(this.HEADER_LENGTH+f),v=new DataView(g),w=0;v.setUint8(w++,this.KINDS.push),v.setUint8(w++,t.length),v.setUint8(w++,i.length),v.setUint8(w++,o.length),v.setUint8(w++,s.length),Array.from(t,N=>v.setUint8(w++,N.charCodeAt(0))),Array.from(i,N=>v.setUint8(w++,N.charCodeAt(0))),Array.from(o,N=>v.setUint8(w++,N.charCodeAt(0))),Array.from(s,N=>v.setUint8(w++,N.charCodeAt(0)));var S=new Uint8Array(g.byteLength+l.byteLength);return S.set(new Uint8Array(g),0),S.set(new Uint8Array(l),g.byteLength),S.buffer},binaryDecode(e){let t=new DataView(e),i=t.getUint8(0),s=new TextDecoder;switch(i){case this.KINDS.push:return this.decodePush(e,t,s);case this.KINDS.reply:return this.decodeReply(e,t,s);case this.KINDS.broadcast:return this.decodeBroadcast(e,t,s)}},decodePush(e,t,i){let s=t.getUint8(1),o=t.getUint8(2),l=t.getUint8(3),f=this.HEADER_LENGTH+this.META_LENGTH-1,g=i.decode(e.slice(f,f+s));f=f+s;let v=i.decode(e.slice(f,f+o));f=f+o;let w=i.decode(e.slice(f,f+l));f=f+l;let S=e.slice(f,e.byteLength);return{join_ref:g,ref:null,topic:v,event:w,payload:S}},decodeReply(e,t,i){let s=t.getUint8(1),o=t.getUint8(2),l=t.getUint8(3),f=t.getUint8(4),g=this.HEADER_LENGTH+this.META_LENGTH,v=i.decode(e.slice(g,g+s));g=g+s;let w=i.decode(e.slice(g,g+o));g=g+o;let S=i.decode(e.slice(g,g+l));g=g+l;let N=i.decode(e.slice(g,g+f));g=g+f;let R=e.slice(g,e.byteLength),P={status:N,response:R};return{join_ref:v,ref:w,topic:S,event:ht.reply,payload:P}},decodeBroadcast(e,t,i){let s=t.getUint8(1),o=t.getUint8(2),l=this.HEADER_LENGTH+2,f=i.decode(e.slice(l,l+s));l=l+s;let g=i.decode(e.slice(l,l+o));l=l+o;let v=e.slice(l,e.byteLength);return{join_ref:null,ref:null,topic:f,event:g,payload:v}}},po=class{constructor(e,t={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=t.timeout||Su,this.transport=t.transport||Ci.WebSocket||yr,this.establishedConnections=0,this.defaultEncoder=an.encode.bind(an),this.defaultDecoder=an.decode.bind(an),this.closeWasClean=!1,this.binaryType=t.binaryType||"arraybuffer",this.connectClock=1,this.transport!==yr?(this.encode=t.encode||this.defaultEncoder,this.decode=t.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let i=null;Si&&Si.addEventListener&&(Si.addEventListener("pagehide",s=>{this.conn&&(this.disconnect(),i=this.connectClock)}),Si.addEventListener("pageshow",s=>{i===this.connectClock&&(i=null,this.connect())})),this.heartbeatIntervalMs=t.heartbeatIntervalMs||3e4,this.rejoinAfterMs=s=>t.rejoinAfterMs?t.rejoinAfterMs(s):[1e3,2e3,5e3][s-1]||1e4,this.reconnectAfterMs=s=>t.reconnectAfterMs?t.reconnectAfterMs(s):[10,50,100,150,200,250,500,1e3,2e3][s-1]||5e3,this.logger=t.logger||null,this.longpollerTimeout=t.longpollerTimeout||2e4,this.params=Ei(t.params||{}),this.endPoint=`${e}/${br.websocket}`,this.vsn=t.vsn||wu,this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new ho(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs)}getLongPollTransport(){return yr}replaceTransport(e){this.connectClock++,this.closeWasClean=!0,this.reconnectTimer.reset(),this.sendBuffer=[],this.conn&&(this.conn.close(),this.conn=null),this.transport=e}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let e=ln.appendParams(ln.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return e.charAt(0)!=="/"?e:e.charAt(1)==="/"?`${this.protocol()}:${e}`:`${this.protocol()}://${location.host}${e}`}disconnect(e,t,i){this.connectClock++,this.closeWasClean=!0,this.reconnectTimer.reset(),this.teardown(e,t,i)}connect(e){e&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=Ei(e)),!this.conn&&(this.connectClock++,this.closeWasClean=!1,this.conn=new this.transport(this.endPointURL()),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=t=>this.onConnError(t),this.conn.onmessage=t=>this.onConnMessage(t),this.conn.onclose=t=>this.onConnClose(t))}log(e,t,i){this.logger(e,t,i)}hasLogger(){return this.logger!==null}onOpen(e){let t=this.makeRef();return this.stateChangeCallbacks.open.push([t,e]),t}onClose(e){let t=this.makeRef();return this.stateChangeCallbacks.close.push([t,e]),t}onError(e){let t=this.makeRef();return this.stateChangeCallbacks.error.push([t,e]),t}onMessage(e){let t=this.makeRef();return this.stateChangeCallbacks.message.push([t,e]),t}ping(e){if(!this.isConnected())return!1;let t=this.makeRef(),i=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:t});let s=this.onMessage(o=>{o.ref===t&&(this.off([s]),e(Date.now()-i))});return!0}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,e])=>e())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),Eu,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(e,t,i){if(!this.conn)return e&&e();this.waitForBufferDone(()=>{this.conn&&(t?this.conn.close(t,i||""):this.conn.close()),this.waitForSocketClosed(()=>{this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),e&&e()})})}waitForBufferDone(e,t=1){if(t===5||!this.conn||!this.conn.bufferedAmount){e();return}setTimeout(()=>{this.waitForBufferDone(e,t+1)},150*t)}waitForSocketClosed(e,t=1){if(t===5||!this.conn||this.conn.readyState===tt.closed){e();return}setTimeout(()=>{this.waitForSocketClosed(e,t+1)},150*t)}onConnClose(e){let t=e&&e.code;this.hasLogger()&&this.log("transport","close",e),this.triggerChanError(),this.clearHeartbeats(),!this.closeWasClean&&t!==1e3&&this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,i])=>i(e))}onConnError(e){this.hasLogger()&&this.log("transport",e);let t=this.transport,i=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,s])=>{s(e,t,i)}),(t===this.transport||i>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(e=>{e.isErrored()||e.isLeaving()||e.isClosed()||e.trigger(ht.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case tt.connecting:return"connecting";case tt.open:return"open";case tt.closing:return"closing";default:return"closed"}}isConnected(){return this.connectionState()==="open"}remove(e){this.off(e.stateChangeRefs),this.channels=this.channels.filter(t=>t.joinRef()!==e.joinRef())}off(e){for(let t in this.stateChangeCallbacks)this.stateChangeCallbacks[t]=this.stateChangeCallbacks[t].filter(([i])=>e.indexOf(i)===-1)}channel(e,t={}){let i=new Au(e,t,this);return this.channels.push(i),i}push(e){if(this.hasLogger()){let{topic:t,event:i,payload:s,ref:o,join_ref:l}=e;this.log("push",`${t} ${i} (${l}, ${o})`,s)}this.isConnected()?this.encode(e,t=>this.conn.send(t)):this.sendBuffer.push(()=>this.encode(e,t=>this.conn.send(t)))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}sendHeartbeat(){this.pendingHeartbeatRef&&!this.isConnected()||(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}onConnMessage(e){this.decode(e.data,t=>{let{topic:i,event:s,payload:o,ref:l,join_ref:f}=t;l&&l===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${o.status||""} ${i} ${s} ${l&&"("+l+")"||""}`,o);for(let g=0;g<this.channels.length;g++){let v=this.channels[g];!v.isMember(i,s,o,f)||v.trigger(s,o,l,f)}for(let g=0;g<this.stateChangeCallbacks.message.length;g++){let[,v]=this.stateChangeCallbacks.message[g];v(t)}})}leaveOpenTopic(e){let t=this.channels.find(i=>i.topic===e&&(i.isJoined()||i.isJoining()));t&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${e}"`),t.leave())}};var No="consecutive-reloads",Tu=10,ku=5e3,Pu=1e4,Lu=3e4,Mo=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading"],Ie="data-phx-component",xr="data-phx-link",Du="track-static",Ru="data-phx-link-state",ot="data-phx-ref",Nt="data-phx-ref-src",jo="track-uploads",Mt="data-phx-upload-ref",Ir="data-phx-preflighted-refs",Ou="data-phx-done-refs",go="drop-target",Lr="data-phx-active-refs",bn="phx:live-file:updated",Dr="data-phx-skip",vo="data-phx-prune",mo="page-loading",yo="phx-connected",_r="phx-loading",wr="phx-no-feedback",bo="phx-error",ii="data-phx-parent-id",Nr="data-phx-main",Oi="data-phx-root-id",Iu="trigger-action",_n="feedback-for",Rr="phx-has-focused",Nu=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],Ho=["checkbox","radio"],wn="phx-has-submitted",Et="data-phx-session",ri=`[${Et}]`,xo="data-phx-sticky",Di="data-phx-static",Sr="data-phx-readonly",un="data-phx-disabled",Or="disable-with",cn="data-phx-disable-with-restore",Ai="hook",Mu="debounce",ju="throttle",Sn="update",Er="stream",Hu="key",it="phxPrivate",_o="auto-recover",fn="phx:live-socket:debug",Cr="phx:live-socket:profiling",Ar="phx:live-socket:latency-sim",$u="progress",wo="mounted",Fu=1,Uu=200,Bu="phx-",qu=3e4,Ti="debounce-trigger",dn="throttled",So="debounce-prev-key",Wu={debounce:300,throttle:300},hn="d",nt="s",Oe="c",Eo="e",Co="r",Ao="t",Vu="p",Ju="stream",zu=class{constructor(e,t,i){this.liveSocket=i,this.entry=e,this.offset=0,this.chunkSize=t,this.chunkTimer=null,this.uploadChannel=i.channel(`lvu:${e.ref}`,{token:e.metadata()})}error(e){clearTimeout(this.chunkTimer),this.uploadChannel.leave(),this.entry.error(e)}upload(){this.uploadChannel.onError(e=>this.error(e)),this.uploadChannel.join().receive("ok",e=>this.readNextChunk()).receive("error",e=>this.error(e))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let e=new window.FileReader,t=this.entry.file.slice(this.offset,this.chunkSize+this.offset);e.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return _e("Read error: "+i.target.error)},e.readAsArrayBuffer(t)}pushChunk(e){!this.uploadChannel.isJoined()||this.uploadChannel.push("chunk",e).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))})}},_e=(e,t)=>console.error&&console.error(e,t),wt=e=>{let t=typeof e;return t==="number"||t==="string"&&/^(0|[1-9]\d*)$/.test(e)};function Xu(){let e=new Set,t=document.querySelectorAll("*[id]");for(let i=0,s=t.length;i<s;i++)e.has(t[i].id)?console.error(`Multiple IDs detected: ${t[i].id}. Ensure unique element ids.`):e.add(t[i].id)}var Ku=(e,t,i,s)=>{e.liveSocket.isDebugEnabled()&&console.log(`${e.id} ${t}: ${i} - `,s)},Tr=e=>typeof e=="function"?e:function(){return e},xn=e=>JSON.parse(JSON.stringify(e)),Ri=(e,t,i)=>{do{if(e.matches(`[${t}]`)&&!e.disabled)return e;e=e.parentElement||e.parentNode}while(e!==null&&e.nodeType===1&&!(i&&i.isSameNode(e)||e.matches(ri)));return null},ki=e=>e!==null&&typeof e=="object"&&!(e instanceof Array),Gu=(e,t)=>JSON.stringify(e)===JSON.stringify(t),To=e=>{for(let t in e)return!1;return!0},St=(e,t)=>e&&t(e),Yu=function(e,t,i,s){e.forEach(o=>{new zu(o,i.config.chunk_size,s).upload()})},$o={canPushState(){return typeof history.pushState!="undefined"},dropLocal(e,t,i){return e.removeItem(this.localKey(t,i))},updateLocal(e,t,i,s,o){let l=this.getLocal(e,t,i),f=this.localKey(t,i),g=l===null?s:o(l);return e.setItem(f,JSON.stringify(g)),g},getLocal(e,t,i){return JSON.parse(e.getItem(this.localKey(t,i)))},updateCurrentState(e){!this.canPushState()||history.replaceState(e(history.state||{}),"",window.location.href)},pushState(e,t,i){if(this.canPushState()){if(i!==window.location.href){if(t.type=="redirect"&&t.scroll){let o=history.state||{};o.scroll=t.scroll,history.replaceState(o,"",window.location.href)}delete t.scroll,history[e+"State"](t,"",i||null);let s=this.getHashTargetEl(window.location.hash);s?s.scrollIntoView():t.type==="redirect"&&window.scroll(0,0)}}else this.redirect(i)},setCookie(e,t){document.cookie=`${e}=${t}`},getCookie(e){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${e}s*=s*([^;]*).*$)|^.*$`),"$1")},redirect(e,t){t&&$o.setCookie("__phoenix_flash__",t+"; max-age=60000; path=/"),window.location=e},localKey(e,t){return`${e}-${t}`},getHashTargetEl(e){let t=e.toString().substring(1);if(t!=="")return document.getElementById(t)||document.querySelector(`a[name="${t}"]`)}},rt=$o,qe={byId(e){return document.getElementById(e)||_e(`no id found for ${e}`)},removeClass(e,t){e.classList.remove(t),e.classList.length===0&&e.removeAttribute("class")},all(e,t,i){if(!e)return[];let s=Array.from(e.querySelectorAll(t));return i?s.forEach(i):s},childNodeLength(e){let t=document.createElement("template");return t.innerHTML=e,t.content.childElementCount},isUploadInput(e){return e.type==="file"&&e.getAttribute(Mt)!==null},findUploadInputs(e){return this.all(e,`input[type="file"][${Mt}]`)},findComponentNodeList(e,t){return this.filterWithinSameLiveView(this.all(e,`[${Ie}="${t}"]`),e)},isPhxDestroyed(e){return!!(e.id&&qe.private(e,"destroyed"))},wantsNewTab(e){return e.ctrlKey||e.shiftKey||e.metaKey||e.button&&e.button===1||e.target.getAttribute("target")==="_blank"},isUnloadableFormSubmit(e){return!e.defaultPrevented&&!this.wantsNewTab(e)},isNewPageHref(e,t){let i;try{i=new URL(e)}catch(s){try{i=new URL(e,t)}catch(o){return!0}}return i.host===t.host&&i.protocol===t.protocol&&i.pathname===t.pathname&&i.search===t.search?i.hash===""&&!i.href.endsWith("#"):!0},markPhxChildDestroyed(e){this.isPhxChild(e)&&e.setAttribute(Et,""),this.putPrivate(e,"destroyed",!0)},findPhxChildrenInFragment(e,t){let i=document.createElement("template");return i.innerHTML=e,this.findPhxChildren(i.content,t)},isIgnored(e,t){return(e.getAttribute(t)||e.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(e,t,i){return e.getAttribute&&i.indexOf(e.getAttribute(t))>=0},findPhxSticky(e){return this.all(e,`[${xo}]`)},findPhxChildren(e,t){return this.all(e,`${ri}[${ii}="${t}"]`)},findParentCIDs(e,t){let i=new Set(t),s=t.reduce((o,l)=>{let f=`[${Ie}="${l}"] [${Ie}]`;return this.filterWithinSameLiveView(this.all(e,f),e).map(g=>parseInt(g.getAttribute(Ie))).forEach(g=>o.delete(g)),o},i);return s.size===0?new Set(t):s},filterWithinSameLiveView(e,t){return t.querySelector(ri)?e.filter(i=>this.withinSameLiveView(i,t)):e},withinSameLiveView(e,t){for(;e=e.parentNode;){if(e.isSameNode(t))return!0;if(e.getAttribute(Et)!==null)return!1}},private(e,t){return e[it]&&e[it][t]},deletePrivate(e,t){e[it]&&delete e[it][t]},putPrivate(e,t,i){e[it]||(e[it]={}),e[it][t]=i},updatePrivate(e,t,i,s){let o=this.private(e,t);o===void 0?this.putPrivate(e,t,s(i)):this.putPrivate(e,t,s(o))},copyPrivates(e,t){t[it]&&(e[it]=t[it])},putTitle(e){let t=document.querySelector("title");if(t){let{prefix:i,suffix:s}=t.dataset;document.title=`${i||""}${e}${s||""}`}else document.title=e},debounce(e,t,i,s,o,l,f,g){let v=e.getAttribute(i),w=e.getAttribute(o);v===""&&(v=s),w===""&&(w=l);let S=v||w;switch(S){case null:return g();case"blur":this.once(e,"debounce-blur")&&e.addEventListener("blur",()=>g());return;default:let N=parseInt(S),R=()=>w?this.deletePrivate(e,dn):g(),P=this.incCycle(e,Ti,R);if(isNaN(N))return _e(`invalid throttle/debounce value: ${S}`);if(w){let z=!1;if(t.type==="keydown"){let I=this.private(e,So);this.putPrivate(e,So,t.key),z=I!==t.key}if(!z&&this.private(e,dn))return!1;g(),this.putPrivate(e,dn,!0),setTimeout(()=>{f()&&this.triggerCycle(e,Ti)},N)}else setTimeout(()=>{f()&&this.triggerCycle(e,Ti,P)},N);let j=e.form;j&&this.once(j,"bind-debounce")&&j.addEventListener("submit",()=>{Array.from(new FormData(j).entries(),([z])=>{let I=j.querySelector(`[name="${z}"]`);this.incCycle(I,Ti),this.deletePrivate(I,dn)})}),this.once(e,"bind-debounce")&&e.addEventListener("blur",()=>this.triggerCycle(e,Ti))}},triggerCycle(e,t,i){let[s,o]=this.private(e,t);i||(i=s),i===s&&(this.incCycle(e,t),o())},once(e,t){return this.private(e,t)===!0?!1:(this.putPrivate(e,t,!0),!0)},incCycle(e,t,i=function(){}){let[s]=this.private(e,t)||[0,i];return s++,this.putPrivate(e,t,[s,i]),s},discardError(e,t,i){let s=t.getAttribute&&t.getAttribute(i),o=s&&e.querySelector(`[id="${s}"], [name="${s}"], [name="${s}[]"]`);!o||this.private(o,Rr)||this.private(o,wn)||t.classList.add(wr)},resetForm(e,t){Array.from(e.elements).forEach(i=>{let s=`[${t}="${i.id}"],
                   [${t}="${i.name}"],
                   [${t}="${i.name.replace(/\[\]$/,"")}"]`;this.deletePrivate(i,Rr),this.deletePrivate(i,wn),this.all(document,s,o=>{o.classList.add(wr)})})},showError(e,t){(e.id||e.name)&&this.all(e.form,`[${t}="${e.id}"], [${t}="${e.name}"]`,i=>{this.removeClass(i,wr)})},isPhxChild(e){return e.getAttribute&&e.getAttribute(ii)},isPhxSticky(e){return e.getAttribute&&e.getAttribute(xo)!==null},firstPhxChild(e){return this.isPhxChild(e)?e:this.all(e,`[${ii}]`)[0]},dispatchEvent(e,t,i={}){let o={bubbles:i.bubbles===void 0?!0:!!i.bubbles,cancelable:!0,detail:i.detail||{}},l=t==="click"?new MouseEvent("click",o):new CustomEvent(t,o);e.dispatchEvent(l)},cloneNode(e,t){if(typeof t=="undefined")return e.cloneNode(!0);{let i=e.cloneNode(!1);return i.innerHTML=t,i}},mergeAttrs(e,t,i={}){let s=i.exclude||[],o=i.isIgnored,l=t.attributes;for(let g=l.length-1;g>=0;g--){let v=l[g].name;s.indexOf(v)<0&&e.setAttribute(v,t.getAttribute(v))}let f=e.attributes;for(let g=f.length-1;g>=0;g--){let v=f[g].name;o?v.startsWith("data-")&&!t.hasAttribute(v)&&e.removeAttribute(v):t.hasAttribute(v)||e.removeAttribute(v)}},mergeFocusedInput(e,t){e instanceof HTMLSelectElement||qe.mergeAttrs(e,t,{exclude:["value"]}),t.readOnly?e.setAttribute("readonly",!0):e.removeAttribute("readonly")},hasSelectionRange(e){return e.setSelectionRange&&(e.type==="text"||e.type==="textarea")},restoreFocus(e,t,i){if(!qe.isTextualInput(e))return;let s=e.matches(":focus");e.readOnly&&e.blur(),s||e.focus(),this.hasSelectionRange(e)&&e.setSelectionRange(t,i)},isFormInput(e){return/^(?:input|select|textarea)$/i.test(e.tagName)&&e.type!=="button"},syncAttrsToProps(e){e instanceof HTMLInputElement&&Ho.indexOf(e.type.toLocaleLowerCase())>=0&&(e.checked=e.getAttribute("checked")!==null)},isTextualInput(e){return Nu.indexOf(e.type)>=0},isNowTriggerFormExternal(e,t){return e.getAttribute&&e.getAttribute(t)!==null},syncPendingRef(e,t,i){let s=e.getAttribute(ot);if(s===null)return!0;let o=e.getAttribute(Nt);return qe.isFormInput(e)||e.getAttribute(i)!==null?(qe.isUploadInput(e)&&qe.mergeAttrs(e,t,{isIgnored:!0}),qe.putPrivate(e,ot,t),!1):(Mo.forEach(l=>{e.classList.contains(l)&&t.classList.add(l)}),t.setAttribute(ot,s),t.setAttribute(Nt,o),!0)},cleanChildNodes(e,t){if(qe.isPhxUpdate(e,t,["append","prepend"])){let i=[];e.childNodes.forEach(s=>{s.id||(s.nodeType===Node.TEXT_NODE&&s.nodeValue.trim()===""||_e(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(s.outerHTML||s.nodeValue).trim()}"

`),i.push(s))}),i.forEach(s=>s.remove())}},replaceRootContainer(e,t,i){let s=new Set(["id",Et,Di,Nr,Oi]);if(e.tagName.toLowerCase()===t.toLowerCase())return Array.from(e.attributes).filter(o=>!s.has(o.name.toLowerCase())).forEach(o=>e.removeAttribute(o.name)),Object.keys(i).filter(o=>!s.has(o.toLowerCase())).forEach(o=>e.setAttribute(o,i[o])),e;{let o=document.createElement(t);return Object.keys(i).forEach(l=>o.setAttribute(l,i[l])),s.forEach(l=>o.setAttribute(l,e.getAttribute(l))),o.innerHTML=e.innerHTML,e.replaceWith(o),o}},getSticky(e,t,i){let s=(qe.private(e,"sticky")||[]).find(([o])=>t===o);if(s){let[o,l,f]=s;return f}else return typeof i=="function"?i():i},deleteSticky(e,t){this.updatePrivate(e,"sticky",[],i=>i.filter(([s,o])=>s!==t))},putSticky(e,t,i){let s=i(e);this.updatePrivate(e,"sticky",[],o=>{let l=o.findIndex(([f])=>t===f);return l>=0?o[l]=[t,i,s]:o.push([t,i,s]),o})},applyStickyOperations(e){let t=qe.private(e,"sticky");!t||t.forEach(([i,s,o])=>this.putSticky(e,i,s))}},D=qe,kr=class{static isActive(e,t){let i=t._phxRef===void 0,o=e.getAttribute(Lr).split(",").indexOf(xe.genFileRef(t))>=0;return t.size>0&&(i||o)}static isPreflighted(e,t){return e.getAttribute(Ir).split(",").indexOf(xe.genFileRef(t))>=0&&this.isActive(e,t)}constructor(e,t,i){this.ref=xe.genFileRef(t),this.fileEl=e,this.file=t,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(bn,this._onElUpdated)}metadata(){return this.meta}progress(e){this._progress=Math.floor(e),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{xe.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}cancel(){this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(e="failed"){this.fileEl.removeEventListener(bn,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:e}),xe.clearFiles(this.fileEl)}onDone(e){this._onDone=()=>{this.fileEl.removeEventListener(bn,this._onElUpdated),e()}}onElUpdated(){this.fileEl.getAttribute(Lr).split(",").indexOf(this.ref)===-1&&this.cancel()}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref}}uploader(e){if(this.meta.uploader){let t=e[this.meta.uploader]||_e(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:t}}else return{name:"channel",callback:Yu}}zipPostFlight(e){this.meta=e.entries[this.ref],this.meta||_e(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:e})}},Qu=0,xe=class{static genFileRef(e){let t=e._phxRef;return t!==void 0?t:(e._phxRef=(Qu++).toString(),e._phxRef)}static getEntryDataURL(e,t,i){let s=this.activeFiles(e).find(o=>this.genFileRef(o)===t);i(URL.createObjectURL(s))}static hasUploadsInProgress(e){let t=0;return D.findUploadInputs(e).forEach(i=>{i.getAttribute(Ir)!==i.getAttribute(Ou)&&t++}),t>0}static serializeUploads(e){let t=this.activeFiles(e),i={};return t.forEach(s=>{let o={path:e.name},l=e.getAttribute(Mt);i[l]=i[l]||[],o.ref=this.genFileRef(s),o.last_modified=s.lastModified,o.name=s.name||o.ref,o.relative_path=s.webkitRelativePath,o.type=s.type,o.size=s.size,i[l].push(o)}),i}static clearFiles(e){e.value=null,e.removeAttribute(Mt),D.putPrivate(e,"files",[])}static untrackFile(e,t){D.putPrivate(e,"files",D.private(e,"files").filter(i=>!Object.is(i,t)))}static trackFiles(e,t,i){if(e.getAttribute("multiple")!==null){let s=t.filter(o=>!this.activeFiles(e).find(l=>Object.is(l,o)));D.putPrivate(e,"files",this.activeFiles(e).concat(s)),e.value=null}else i&&i.files.length>0&&(e.files=i.files),D.putPrivate(e,"files",t)}static activeFileInputs(e){let t=D.findUploadInputs(e);return Array.from(t).filter(i=>i.files&&this.activeFiles(i).length>0)}static activeFiles(e){return(D.private(e,"files")||[]).filter(t=>kr.isActive(e,t))}static inputsAwaitingPreflight(e){let t=D.findUploadInputs(e);return Array.from(t).filter(i=>this.filesAwaitingPreflight(i).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(t=>!kr.isPreflighted(e,t))}constructor(e,t,i){this.view=t,this.onComplete=i,this._entries=Array.from(xe.filesAwaitingPreflight(e)||[]).map(s=>new kr(e,s,t)),this.numEntriesInProgress=this._entries.length}entries(){return this._entries}initAdapterUpload(e,t,i){this._entries=this._entries.map(o=>(o.zipPostFlight(e),o.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()}),o));let s=this._entries.reduce((o,l)=>{let{name:f,callback:g}=l.uploader(i.uploaders);return o[f]=o[f]||{callback:g,entries:[]},o[f].entries.push(l),o},{});for(let o in s){let{callback:l,entries:f}=s[o];l(f,t,e,i)}}},Zu={focusMain(){let e=document.querySelector("main h1, main, h1");if(e){let t=e.tabIndex;e.tabIndex=-1,e.focus(),e.tabIndex=t}},anyOf(e,t){return t.find(i=>e instanceof i)},isFocusable(e,t){return e instanceof HTMLAnchorElement&&e.rel!=="ignore"||e instanceof HTMLAreaElement&&e.href!==void 0||!e.disabled&&this.anyOf(e,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||e instanceof HTMLIFrameElement||e.tabIndex>0||!t&&e.tabIndex===0&&e.getAttribute("tabindex")!==null&&e.getAttribute("aria-hidden")!=="true"},attemptFocus(e,t){if(this.isFocusable(e,t))try{e.focus()}catch(i){}return!!document.activeElement&&document.activeElement.isSameNode(e)},focusFirstInteractive(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t,!0)||this.focusFirstInteractive(t,!0))return!0;t=t.nextElementSibling}},focusFirst(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t)||this.focusFirst(t))return!0;t=t.nextElementSibling}},focusLast(e){let t=e.lastElementChild;for(;t;){if(this.attemptFocus(t)||this.focusLast(t))return!0;t=t.previousElementSibling}}},ni=Zu,ec={LiveFileUpload:{activeRefs(){return this.el.getAttribute(Lr)},preflightedRefs(){return this.el.getAttribute(Ir)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let e=this.preflightedRefs();this.preflightedWas!==e&&(this.preflightedWas=e,e===""&&this.__view.cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(bn))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(Mt)),xe.getEntryDataURL(this.inputEl,this.ref,e=>{this.url=e,this.el.src=e})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",()=>ni.focusLast(this.el)),this.focusEnd.addEventListener("focus",()=>ni.focusFirst(this.el)),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&ni.focusFirst(this.el)}}},tc=ec,ic=class{constructor(e,t,i){let s=new Set,o=new Set([...t.children].map(f=>f.id)),l=[];Array.from(e.children).forEach(f=>{if(f.id&&(s.add(f.id),o.has(f.id))){let g=f.previousElementSibling&&f.previousElementSibling.id;l.push({elementId:f.id,previousElementId:g})}}),this.containerId=t.id,this.updateType=i,this.elementsToModify=l,this.elementIdsToAdd=[...o].filter(f=>!s.has(f))}perform(){let e=D.byId(this.containerId);this.elementsToModify.forEach(t=>{t.previousElementId?St(document.getElementById(t.previousElementId),i=>{St(document.getElementById(t.elementId),s=>{s.previousElementSibling&&s.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",s)})}):St(document.getElementById(t.elementId),i=>{i.previousElementSibling==null||e.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(t=>{St(document.getElementById(t),i=>e.insertAdjacentElement("afterbegin",i))})}},ko=11;function nc(e,t){var i=t.attributes,s,o,l,f,g;if(!(t.nodeType===ko||e.nodeType===ko)){for(var v=i.length-1;v>=0;v--)s=i[v],o=s.name,l=s.namespaceURI,f=s.value,l?(o=s.localName||o,g=e.getAttributeNS(l,o),g!==f&&(s.prefix==="xmlns"&&(o=s.name),e.setAttributeNS(l,o,f))):(g=e.getAttribute(o),g!==f&&e.setAttribute(o,f));for(var w=e.attributes,S=w.length-1;S>=0;S--)s=w[S],o=s.name,l=s.namespaceURI,l?(o=s.localName||o,t.hasAttributeNS(l,o)||e.removeAttributeNS(l,o)):t.hasAttribute(o)||e.removeAttribute(o)}}var pn,rc="http://www.w3.org/1999/xhtml",Pe=typeof document=="undefined"?void 0:document,sc=!!Pe&&"content"in Pe.createElement("template"),oc=!!Pe&&Pe.createRange&&"createContextualFragment"in Pe.createRange();function ac(e){var t=Pe.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function lc(e){pn||(pn=Pe.createRange(),pn.selectNode(Pe.body));var t=pn.createContextualFragment(e);return t.childNodes[0]}function uc(e){var t=Pe.createElement("body");return t.innerHTML=e,t.childNodes[0]}function cc(e){return e=e.trim(),sc?ac(e):oc?lc(e):uc(e)}function gn(e,t){var i=e.nodeName,s=t.nodeName,o,l;return i===s?!0:(o=i.charCodeAt(0),l=s.charCodeAt(0),o<=90&&l>=97?i===s.toUpperCase():l<=90&&o>=97?s===i.toUpperCase():!1)}function fc(e,t){return!t||t===rc?Pe.createElement(e):Pe.createElementNS(t,e)}function dc(e,t){for(var i=e.firstChild;i;){var s=i.nextSibling;t.appendChild(i),i=s}return t}function Pr(e,t,i){e[i]!==t[i]&&(e[i]=t[i],e[i]?e.setAttribute(i,""):e.removeAttribute(i))}var Po={OPTION:function(e,t){var i=e.parentNode;if(i){var s=i.nodeName.toUpperCase();s==="OPTGROUP"&&(i=i.parentNode,s=i&&i.nodeName.toUpperCase()),s==="SELECT"&&!i.hasAttribute("multiple")&&(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),i.selectedIndex=-1)}Pr(e,t,"selected")},INPUT:function(e,t){Pr(e,t,"checked"),Pr(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var i=t.value;e.value!==i&&(e.value=i);var s=e.firstChild;if(s){var o=s.nodeValue;if(o==i||!i&&o==e.placeholder)return;s.nodeValue=i}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var i=-1,s=0,o=e.firstChild,l,f;o;)if(f=o.nodeName&&o.nodeName.toUpperCase(),f==="OPTGROUP")l=o,o=l.firstChild;else{if(f==="OPTION"){if(o.hasAttribute("selected")){i=s;break}s++}o=o.nextSibling,!o&&l&&(o=l.nextSibling,l=null)}e.selectedIndex=i}}},Pi=1,Lo=11,Do=3,Ro=8;function _t(){}function hc(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}function pc(e){return function(i,s,o){if(o||(o={}),typeof s=="string")if(i.nodeName==="#document"||i.nodeName==="HTML"||i.nodeName==="BODY"){var l=s;s=Pe.createElement("html"),s.innerHTML=l}else s=cc(s);else s.nodeType===Lo&&(s=s.firstElementChild);var f=o.getNodeKey||hc,g=o.onBeforeNodeAdded||_t,v=o.onNodeAdded||_t,w=o.onBeforeElUpdated||_t,S=o.onElUpdated||_t,N=o.onBeforeNodeDiscarded||_t,R=o.onNodeDiscarded||_t,P=o.onBeforeElChildrenUpdated||_t,j=o.skipFromChildren||_t,z=o.addChild||function(Y,V){return Y.appendChild(V)},I=o.childrenOnly===!0,k=Object.create(null),U=[];function G(Y){U.push(Y)}function oe(Y,V){if(Y.nodeType===Pi)for(var fe=Y.firstChild;fe;){var te=void 0;V&&(te=f(fe))?G(te):(R(fe),fe.firstChild&&oe(fe,V)),fe=fe.nextSibling}}function Le(Y,V,fe){N(Y)!==!1&&(V&&V.removeChild(Y),R(Y),oe(Y,fe))}function c(Y){if(Y.nodeType===Pi||Y.nodeType===Lo)for(var V=Y.firstChild;V;){var fe=f(V);fe&&(k[fe]=V),c(V),V=V.nextSibling}}c(i);function Je(Y){v(Y);for(var V=Y.firstChild;V;){var fe=V.nextSibling,te=f(V);if(te){var ue=k[te];ue&&gn(V,ue)?(V.parentNode.replaceChild(ue,V),gt(ue,V)):Je(V)}else Je(V);V=fe}}function ae(Y,V,fe){for(;V;){var te=V.nextSibling;(fe=f(V))?G(fe):Le(V,Y,!0),V=te}}function gt(Y,V,fe){var te=f(V);te&&delete k[te],!(!fe&&(w(Y,V)===!1||(e(Y,V),S(Y),P(Y,V)===!1)))&&(Y.nodeName!=="TEXTAREA"?ci(Y,V):Po.TEXTAREA(Y,V))}function ci(Y,V){var fe=j(Y),te=V.firstChild,ue=Y.firstChild,ze,$e,mt,Vt,Xe;e:for(;te;){for(Vt=te.nextSibling,ze=f(te);!fe&&ue;){if(mt=ue.nextSibling,te.isSameNode&&te.isSameNode(ue)){te=Vt,ue=mt;continue e}$e=f(ue);var Jt=ue.nodeType,Fe=void 0;if(Jt===te.nodeType&&(Jt===Pi?(ze?ze!==$e&&((Xe=k[ze])?mt===Xe?Fe=!1:(Y.insertBefore(Xe,ue),$e?G($e):Le(ue,Y,!0),ue=Xe):Fe=!1):$e&&(Fe=!1),Fe=Fe!==!1&&gn(ue,te),Fe&&gt(ue,te)):(Jt===Do||Jt==Ro)&&(Fe=!0,ue.nodeValue!==te.nodeValue&&(ue.nodeValue=te.nodeValue))),Fe){te=Vt,ue=mt;continue e}$e?G($e):Le(ue,Y,!0),ue=mt}if(ze&&(Xe=k[ze])&&gn(Xe,te))fe||z(Y,Xe),gt(Xe,te);else{var Ee=g(te);Ee!==!1&&(Ee&&(te=Ee),te.actualize&&(te=te.actualize(Y.ownerDocument||Pe)),z(Y,te),Je(te))}te=Vt,ue=mt}ae(Y,ue,$e);var Vi=Po[Y.nodeName];Vi&&Vi(Y,V)}var me=i,re=me.nodeType,vt=s.nodeType;if(!I){if(re===Pi)vt===Pi?gn(i,s)||(R(i),me=dc(i,fc(s.nodeName,s.namespaceURI))):me=s;else if(re===Do||re===Ro){if(vt===re)return me.nodeValue!==s.nodeValue&&(me.nodeValue=s.nodeValue),me;me=s}}if(me===s)R(i);else{if(s.isSameNode&&s.isSameNode(me))return;if(gt(me,s,I),U)for(var fi=0,Vn=U.length;fi<Vn;fi++){var De=k[U[fi]];De&&Le(De,De.parentNode,!1)}}return!I&&me!==i&&i.parentNode&&(me.actualize&&(me=me.actualize(i.ownerDocument||Pe)),i.parentNode.replaceChild(me,i)),me}}var gc=pc(nc),Oo=gc,vn=class{static patchEl(e,t,i){Oo(e,t,{childrenOnly:!1,onBeforeElUpdated:(s,o)=>{if(i&&i.isSameNode(s)&&D.isFormInput(s))return D.mergeFocusedInput(s,o),!1}})}constructor(e,t,i,s,o,l){this.view=e,this.liveSocket=e.liveSocket,this.container=t,this.id=i,this.rootID=e.root.id,this.html=s,this.streams=o,this.streamInserts={},this.targetCID=l,this.cidPatch=wt(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]}}before(e,t){this.callbacks[`before${e}`].push(t)}after(e,t){this.callbacks[`after${e}`].push(t)}trackBefore(e,...t){this.callbacks[`before${e}`].forEach(i=>i(...t))}trackAfter(e,...t){this.callbacks[`after${e}`].forEach(i=>i(...t))}markPrunableContentForRemoval(){let e=this.liveSocket.binding(Sn);D.all(this.container,`[${e}=${Er}]`,t=>t.innerHTML=""),D.all(this.container,`[${e}=append] > *, [${e}=prepend] > *`,t=>{t.setAttribute(vo,"")})}perform(){let{view:e,liveSocket:t,container:i,html:s}=this,o=this.isCIDPatch()?this.targetCIDContainer(s):i;if(this.isCIDPatch()&&!o)return;let l=t.getActiveElement(),{selectionStart:f,selectionEnd:g}=l&&D.hasSelectionRange(l)?l:{},v=t.binding(Sn),w=t.binding(_n),S=t.binding(Or),N=t.binding(Iu),R=[],P=[],j=[],z=null,I=t.time("premorph container prep",()=>this.buildDiffHTML(i,s,v,o));return this.trackBefore("added",i),this.trackBefore("updated",i,i),t.time("morphdom",()=>{this.streams.forEach(([k,U])=>{this.streamInserts=Object.assign(this.streamInserts,k),U.forEach(G=>{let oe=i.querySelector(`[id="${G}"]`);oe&&(this.maybePendingRemove(oe)||(oe.remove(),this.onNodeDiscarded(oe)))})}),Oo(o,I,{childrenOnly:o.getAttribute(Ie)===null,getNodeKey:k=>D.isPhxDestroyed(k)?null:k.id,skipFromChildren:k=>k.getAttribute(v)===Er,addChild:(k,U)=>{let G=U.id?this.streamInserts[U.id]:void 0;if(G===void 0)return k.appendChild(U);if(G===0)k.insertAdjacentElement("afterbegin",U);else if(G===-1)k.appendChild(U);else if(G>0){let oe=Array.from(k.children)[G];k.insertBefore(U,oe)}},onBeforeNodeAdded:k=>(this.trackBefore("added",k),k),onNodeAdded:k=>{k instanceof HTMLImageElement&&k.srcset?k.srcset=k.srcset:k instanceof HTMLVideoElement&&k.autoplay&&k.play(),D.isNowTriggerFormExternal(k,N)&&(z=k),D.discardError(o,k,w),(D.isPhxChild(k)&&e.ownsElement(k)||D.isPhxSticky(k)&&e.ownsElement(k.parentNode))&&this.trackAfter("phxChildAdded",k),R.push(k)},onNodeDiscarded:k=>this.onNodeDiscarded(k),onBeforeNodeDiscarded:k=>k.getAttribute&&k.getAttribute(vo)!==null?!0:!(k.parentElement!==null&&k.id&&D.isPhxUpdate(k.parentElement,v,[Er,"append","prepend"])||this.maybePendingRemove(k)||this.skipCIDSibling(k)),onElUpdated:k=>{D.isNowTriggerFormExternal(k,N)&&(z=k),P.push(k),this.maybeReOrderStream(k)},onBeforeElUpdated:(k,U)=>{if(D.cleanChildNodes(U,v),this.skipCIDSibling(U)||D.isPhxSticky(k))return!1;if(D.isIgnored(k,v)||k.form&&k.form.isSameNode(z))return this.trackBefore("updated",k,U),D.mergeAttrs(k,U,{isIgnored:!0}),P.push(k),D.applyStickyOperations(k),!1;if(k.type==="number"&&k.validity&&k.validity.badInput)return!1;if(!D.syncPendingRef(k,U,S))return D.isUploadInput(k)&&(this.trackBefore("updated",k,U),P.push(k)),D.applyStickyOperations(k),!1;if(D.isPhxChild(U)){let oe=k.getAttribute(Et);return D.mergeAttrs(k,U,{exclude:[Di]}),oe!==""&&k.setAttribute(Et,oe),k.setAttribute(Oi,this.rootID),D.applyStickyOperations(k),!1}return D.copyPrivates(U,k),D.discardError(o,U,w),l&&k.isSameNode(l)&&D.isFormInput(k)&&k.type!=="hidden"?(this.trackBefore("updated",k,U),D.mergeFocusedInput(k,U),D.syncAttrsToProps(k),P.push(k),D.applyStickyOperations(k),!1):(D.isPhxUpdate(U,v,["append","prepend"])&&j.push(new ic(k,U,U.getAttribute(v))),D.syncAttrsToProps(U),D.applyStickyOperations(U),this.trackBefore("updated",k,U),!0)}})}),t.isDebugEnabled()&&Xu(),j.length>0&&t.time("post-morph append/prepend restoration",()=>{j.forEach(k=>k.perform())}),t.silenceEvents(()=>D.restoreFocus(l,f,g)),D.dispatchEvent(document,"phx:update"),R.forEach(k=>this.trackAfter("added",k)),P.forEach(k=>this.trackAfter("updated",k)),this.transitionPendingRemoves(),z&&(t.unload(),z.submit()),!0}onNodeDiscarded(e){(D.isPhxChild(e)||D.isPhxSticky(e))&&this.liveSocket.destroyViewByEl(e),this.trackAfter("discarded",e)}maybePendingRemove(e){return e.getAttribute&&e.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(e),!0):!1}maybeReOrderStream(e){let t=e.id?this.streamInserts[e.id]:void 0;if(t!==void 0){if(t===0)e.parentElement.insertBefore(e,e.parentElement.firstElementChild);else if(t>0){let i=Array.from(e.parentElement.children),s=i.indexOf(e);if(t>=i.length-1)e.parentElement.appendChild(e);else{let o=i[t];s>t?e.parentElement.insertBefore(e,o):e.parentElement.insertBefore(e,o.nextElementSibling)}}}}transitionPendingRemoves(){let{pendingRemoves:e,liveSocket:t}=this;e.length>0&&(t.transitionRemoves(e),t.requestDOMUpdate(()=>{e.forEach(i=>{let s=D.firstPhxChild(i);s&&t.destroyViewByEl(s),i.remove()}),this.trackAfter("transitionsDiscarded",e)}))}isCIDPatch(){return this.cidPatch}skipCIDSibling(e){return e.nodeType===Node.ELEMENT_NODE&&e.getAttribute(Dr)!==null}targetCIDContainer(e){if(!this.isCIDPatch())return;let[t,...i]=D.findComponentNodeList(this.container,this.targetCID);return i.length===0&&D.childNodeLength(e)===1?t:t&&t.parentNode}buildDiffHTML(e,t,i,s){let o=this.isCIDPatch(),l=o&&s.getAttribute(Ie)===this.targetCID.toString();if(!o||l)return t;{let f=null,g=document.createElement("template");f=D.cloneNode(s);let[v,...w]=D.findComponentNodeList(f,this.targetCID);return g.innerHTML=t,w.forEach(S=>S.remove()),Array.from(f.childNodes).forEach(S=>{S.id&&S.nodeType===Node.ELEMENT_NODE&&S.getAttribute(Ie)!==this.targetCID.toString()&&(S.setAttribute(Dr,""),S.innerHTML="")}),Array.from(g.content.childNodes).forEach(S=>f.insertBefore(S,v)),v.remove(),f.outerHTML}}indexOf(e,t){return Array.from(e.children).indexOf(t)}},Io=class{static extract(e){let{[Co]:t,[Eo]:i,[Ao]:s}=e;return delete e[Co],delete e[Eo],delete e[Ao],{diff:e,title:s,reply:t||null,events:i||[]}}constructor(e,t){this.viewId=e,this.rendered={},this.mergeDiff(t)}parentViewId(){return this.viewId}toString(e){let[t,i]=this.recursiveToString(this.rendered,this.rendered[Oe],e);return[t,i]}recursiveToString(e,t=e[Oe],i){i=i?new Set(i):null;let s={buffer:"",components:t,onlyCids:i,streams:new Set};return this.toOutputBuffer(e,null,s),[s.buffer,s.streams]}componentCIDs(e){return Object.keys(e[Oe]||{}).map(t=>parseInt(t))}isComponentOnlyDiff(e){return e[Oe]?Object.keys(e).length===1:!1}getComponent(e,t){return e[Oe][t]}mergeDiff(e){let t=e[Oe],i={};if(delete e[Oe],this.rendered=this.mutableMerge(this.rendered,e),this.rendered[Oe]=this.rendered[Oe]||{},t){let s=this.rendered[Oe];for(let o in t)t[o]=this.cachedFindComponent(o,t[o],s,t,i);for(let o in t)s[o]=t[o];e[Oe]=t}}cachedFindComponent(e,t,i,s,o){if(o[e])return o[e];{let l,f,g=t[nt];if(wt(g)){let v;g>0?v=this.cachedFindComponent(g,s[g],i,s,o):v=i[-g],f=v[nt],l=this.cloneMerge(v,t),l[nt]=f}else l=t[nt]!==void 0?t:this.cloneMerge(i[e]||{},t);return o[e]=l,l}}mutableMerge(e,t){return t[nt]!==void 0?t:(this.doMutableMerge(e,t),e)}doMutableMerge(e,t){for(let i in t){let s=t[i],o=e[i];ki(s)&&s[nt]===void 0&&ki(o)?this.doMutableMerge(o,s):e[i]=s}}cloneMerge(e,t){let i=dt(dt({},e),t);for(let s in i){let o=t[s],l=e[s];ki(o)&&o[nt]===void 0&&ki(l)&&(i[s]=this.cloneMerge(l,o))}return i}componentToString(e){let[t,i]=this.recursiveCIDToString(this.rendered[Oe],e);return[t,i]}pruneCIDs(e){e.forEach(t=>delete this.rendered[Oe][t])}get(){return this.rendered}isNewFingerprint(e={}){return!!e[nt]}templateStatic(e,t){return typeof e=="number"?t[e]:e}toOutputBuffer(e,t,i){if(e[hn])return this.comprehensionToBuffer(e,t,i);let{[nt]:s}=e;s=this.templateStatic(s,t),i.buffer+=s[0];for(let o=1;o<s.length;o++)this.dynamicToBuffer(e[o-1],t,i),i.buffer+=s[o]}comprehensionToBuffer(e,t,i){let{[hn]:s,[nt]:o,[Ju]:l}=e,[f,g]=l||[{},[]];o=this.templateStatic(o,t);let v=t||e[Vu];for(let w=0;w<s.length;w++){let S=s[w];i.buffer+=o[0];for(let N=1;N<o.length;N++)this.dynamicToBuffer(S[N-1],v,i),i.buffer+=o[N]}l!==void 0&&(e[hn].length>0||g.length>0)&&(e[hn]=[],i.streams.add(l))}dynamicToBuffer(e,t,i){if(typeof e=="number"){let[s,o]=this.recursiveCIDToString(i.components,e,i.onlyCids);i.buffer+=s,i.streams=new Set([...i.streams,...o])}else ki(e)?this.toOutputBuffer(e,t,i):i.buffer+=e}recursiveCIDToString(e,t,i){let s=e[t]||_e(`no component for CID ${t}`,e),o=document.createElement("template"),[l,f]=this.recursiveToString(s,e,i);o.innerHTML=l;let g=o.content,v=i&&!i.has(t),[w,S]=Array.from(g.childNodes).reduce(([N,R],P,j)=>P.nodeType===Node.ELEMENT_NODE?P.getAttribute(Ie)?[N,!0]:(P.setAttribute(Ie,t),P.id||(P.id=`${this.parentViewId()}-${t}-${j}`),v&&(P.setAttribute(Dr,""),P.innerHTML=""),[!0,R]):P.nodeValue.trim()!==""?(_e(`only HTML element tags are allowed at the root of components.

got: "${P.nodeValue.trim()}"

within:
`,o.innerHTML.trim()),P.replaceWith(this.createSpan(P.nodeValue,t)),[!0,R]):(P.remove(),[N,R]),[!1,!1]);return!w&&!S?(_e(`expected at least one HTML element tag inside a component, but the component is empty:
`,o.innerHTML.trim()),[this.createSpan("",t).outerHTML,f]):!w&&S?(_e("expected at least one HTML element tag directly inside a component, but only subcomponents were found. A component must render at least one HTML tag directly inside itself.",o.innerHTML.trim()),[o.innerHTML,f]):[o.innerHTML,f]}createSpan(e,t){let i=document.createElement("span");return i.innerText=e,i.setAttribute(Ie,t),i}},vc=1,Li=class{static makeID(){return vc++}static elementID(e){return e.phxHookId}constructor(e,t,i){this.__view=e,this.liveSocket=e.liveSocket,this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,this.el=t,this.el.phxHookId=this.constructor.makeID();for(let s in this.__callbacks)this[s]=this.__callbacks[s]}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed()}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}pushEvent(e,t={},i=function(){}){return this.__view.pushHookEvent(null,e,t,i)}pushEventTo(e,t,i={},s=function(){}){return this.__view.withinTargets(e,(o,l)=>o.pushHookEvent(l,t,i,s))}handleEvent(e,t){let i=(s,o)=>o?e:t(s.detail);return window.addEventListener(`phx:${e}`,i),this.__listeners.add(i),i}removeHandleEvent(e){let t=e(null,!0);window.removeEventListener(`phx:${t}`,e),this.__listeners.delete(e)}upload(e,t){return this.__view.dispatchUploads(e,t)}uploadTo(e,t,i){return this.__view.withinTargets(e,s=>s.dispatchUploads(t,i))}__cleanup__(){this.__listeners.forEach(e=>this.removeHandleEvent(e))}},mn=null,mc={exec(e,t,i,s,o){let[l,f]=o||[null,{}];(t.charAt(0)==="["?JSON.parse(t):[[l,f]]).forEach(([v,w])=>{v===l&&f.data&&(w.data=Object.assign(w.data||{},f.data)),this.filterToEls(s,w).forEach(S=>{this[`exec_${v}`](e,t,i,s,S,w)})})},isVisible(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length>0)},exec_exec(e,t,i,s,o,[l,f]){(f?D.all(document,f):[s]).forEach(v=>{let w=v.getAttribute(l);if(!w)throw new Error(`expected ${l} to contain JS command on "${f}"`);i.liveSocket.execJS(v,w,e)})},exec_dispatch(e,t,i,s,o,{to:l,event:f,detail:g,bubbles:v}){g=g||{},g.dispatcher=s,D.dispatchEvent(o,f,{detail:g,bubbles:v})},exec_push(e,t,i,s,o,l){if(!i.isConnected())return;let{event:f,data:g,target:v,page_loading:w,loading:S,value:N,dispatcher:R}=l,P={loading:S,value:N,target:v,page_loading:!!w},j=e==="change"&&R?R:s,z=v||j.getAttribute(i.binding("target"))||j;i.withinTargets(z,(I,k)=>{if(e==="change"){let{newCid:U,_target:G,callback:oe}=l;G=G||(D.isFormInput(s)?s.name:void 0),G&&(P._target=G),I.pushInput(s,k,U,f||t,P,oe)}else if(e==="submit"){let{submitter:U}=l;I.submitForm(s,k,f||t,U,P)}else I.pushEvent(e,s,k,f||t,g,P)})},exec_navigate(e,t,i,s,o,{href:l,replace:f}){i.liveSocket.historyRedirect(l,f?"replace":"push")},exec_patch(e,t,i,s,o,{href:l,replace:f}){i.liveSocket.pushHistoryPatch(l,f?"replace":"push",s)},exec_focus(e,t,i,s,o){window.requestAnimationFrame(()=>ni.attemptFocus(o))},exec_focus_first(e,t,i,s,o){window.requestAnimationFrame(()=>ni.focusFirstInteractive(o)||ni.focusFirst(o))},exec_push_focus(e,t,i,s,o){window.requestAnimationFrame(()=>mn=o||s)},exec_pop_focus(e,t,i,s,o){window.requestAnimationFrame(()=>{mn&&mn.focus(),mn=null})},exec_add_class(e,t,i,s,o,{names:l,transition:f,time:g}){this.addOrRemoveClasses(o,l,[],f,g,i)},exec_remove_class(e,t,i,s,o,{names:l,transition:f,time:g}){this.addOrRemoveClasses(o,[],l,f,g,i)},exec_transition(e,t,i,s,o,{time:l,transition:f}){this.addOrRemoveClasses(o,[],[],f,l,i)},exec_toggle(e,t,i,s,o,{display:l,ins:f,outs:g,time:v}){this.toggle(e,i,o,l,f,g,v)},exec_show(e,t,i,s,o,{display:l,transition:f,time:g}){this.show(e,i,o,l,f,g)},exec_hide(e,t,i,s,o,{display:l,transition:f,time:g}){this.hide(e,i,o,l,f,g)},exec_set_attr(e,t,i,s,o,{attr:[l,f]}){this.setOrRemoveAttrs(o,[[l,f]],[])},exec_remove_attr(e,t,i,s,o,{attr:l}){this.setOrRemoveAttrs(o,[],[l])},show(e,t,i,s,o,l){this.isVisible(i)||this.toggle(e,t,i,s,o,null,l)},hide(e,t,i,s,o,l){this.isVisible(i)&&this.toggle(e,t,i,s,null,o,l)},toggle(e,t,i,s,o,l,f){let[g,v,w]=o||[[],[],[]],[S,N,R]=l||[[],[],[]];if(g.length>0||S.length>0)if(this.isVisible(i)){let P=()=>{this.addOrRemoveClasses(i,N,g.concat(v).concat(w)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,S,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,R,N))})};i.dispatchEvent(new Event("phx:hide-start")),t.transition(f,P,()=>{this.addOrRemoveClasses(i,[],S.concat(R)),D.putSticky(i,"toggle",j=>j.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))})}else{if(e==="remove")return;let P=()=>{this.addOrRemoveClasses(i,v,S.concat(N).concat(R));let j=s||this.defaultDisplay(i);D.putSticky(i,"toggle",z=>z.style.display=j),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,g,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,w,v))})};i.dispatchEvent(new Event("phx:show-start")),t.transition(f,P,()=>{this.addOrRemoveClasses(i,[],g.concat(w)),i.dispatchEvent(new Event("phx:show-end"))})}else this.isVisible(i)?window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:hide-start")),D.putSticky(i,"toggle",P=>P.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:show-start"));let P=s||this.defaultDisplay(i);D.putSticky(i,"toggle",j=>j.style.display=P),i.dispatchEvent(new Event("phx:show-end"))})},addOrRemoveClasses(e,t,i,s,o,l){let[f,g,v]=s||[[],[],[]];if(f.length>0){let w=()=>this.addOrRemoveClasses(e,g.concat(f),[]),S=()=>this.addOrRemoveClasses(e,t.concat(v),i.concat(f).concat(g));return l.transition(o,w,S)}window.requestAnimationFrame(()=>{let[w,S]=D.getSticky(e,"classes",[[],[]]),N=t.filter(z=>w.indexOf(z)<0&&!e.classList.contains(z)),R=i.filter(z=>S.indexOf(z)<0&&e.classList.contains(z)),P=w.filter(z=>i.indexOf(z)<0).concat(N),j=S.filter(z=>t.indexOf(z)<0).concat(R);D.putSticky(e,"classes",z=>(z.classList.remove(...j),z.classList.add(...P),[P,j]))})},setOrRemoveAttrs(e,t,i){let[s,o]=D.getSticky(e,"attrs",[[],[]]),l=t.map(([v,w])=>v).concat(i),f=s.filter(([v,w])=>!l.includes(v)).concat(t),g=o.filter(v=>!l.includes(v)).concat(i);D.putSticky(e,"attrs",v=>(g.forEach(w=>v.removeAttribute(w)),f.forEach(([w,S])=>v.setAttribute(w,S)),[f,g]))},hasAllClasses(e,t){return t.every(i=>e.classList.contains(i))},isToggledOut(e,t){return!this.isVisible(e)||this.hasAllClasses(e,t)},filterToEls(e,{to:t}){return t?D.all(document,t):[e]},defaultDisplay(e){return{tr:"table-row",td:"table-cell"}[e.tagName.toLowerCase()]||"block"}},st=mc,yn=(e,t,i=[])=>{let v=t,{submitter:s}=v,o=uo(v,["submitter"]),l=new FormData(e);s&&s.hasAttribute("name")&&s.form&&s.form===e&&l.append(s.name,s.value);let f=[];l.forEach((w,S,N)=>{w instanceof File&&f.push(S)}),f.forEach(w=>l.delete(w));let g=new URLSearchParams;for(let[w,S]of l.entries())(i.length===0||i.indexOf(w)>=0)&&g.append(w,S);for(let w in o)g.append(w,o[w]);return g.toString()},Fo=class{constructor(e,t,i,s,o){this.isDead=!1,this.liveSocket=t,this.flash=s,this.parent=i,this.root=i?i.root:this,this.el=e,this.id=this.el.id,this.ref=0,this.childJoins=0,this.loaderTimer=null,this.pendingDiffs=[],this.pruningCIDs=[],this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(l){l&&l()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.uploaders={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>({redirect:this.redirect?this.href:void 0,url:this.redirect?void 0:this.href||void 0,params:this.connectParams(o),session:this.getSession(),static:this.getStatic(),flash:this.flash}))}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(Nr)}connectParams(e){let t=this.liveSocket.params(this.el),i=D.all(document,`[${this.binding(Du)}]`).map(s=>s.src||s.href).filter(s=>typeof s=="string");return i.length>0&&(t._track_static=i),t._mounts=this.joinCount,t._live_referer=e,t}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(Et)}getStatic(){let e=this.el.getAttribute(Di);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let t=()=>{e();for(let i in this.viewHooks)this.destroyHook(this.viewHooks[i])};D.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",t).receive("error",t).receive("timeout",t)}setContainerClasses(...e){this.el.classList.remove(yo,_r,bo),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let t in this.viewHooks)this.viewHooks[t].__disconnected();this.setContainerClasses(_r)}}execAll(e){D.all(this.el,`[${e}]`,t=>this.liveSocket.execJS(t,t.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),this.setContainerClasses(yo),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,t){this.liveSocket.log(this,e,t)}transition(e,t,i=function(){}){this.liveSocket.transition(e,t,i)}withinTargets(e,t){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,i=>t(i,e));if(wt(e))D.findComponentNodeList(this.el,e).length===0?_e(`no component found matching phx-target of ${e}`):t(this,parseInt(e));else{let i=Array.from(document.querySelectorAll(e));i.length===0&&_e(`nothing found matching the phx-target selector "${e}"`),i.forEach(s=>this.liveSocket.owner(s,o=>t(o,s)))}}applyDiff(e,t,i){this.log(e,()=>["",xn(t)]);let{diff:s,reply:o,events:l,title:f}=Io.extract(t);i({diff:s,reply:o,events:l}),f&&window.requestAnimationFrame(()=>D.putTitle(f))}onJoin(e){let{rendered:t,container:i}=e;if(i){let[s,o]=i;this.el=D.replaceRootContainer(this.el,s,o)}this.childJoins=0,this.joinPending=!0,this.flash=null,rt.dropLocal(this.liveSocket.localStorage,window.location.pathname,No),this.applyDiff("mount",t,({diff:s,events:o})=>{this.rendered=new Io(this.id,s);let[l,f]=this.renderContainer(null,"join");this.dropPendingRefs();let g=this.formsForRecovery(l);this.joinCount++,g.length>0?g.forEach(([v,w,S],N)=>{this.pushFormRecovery(v,S,R=>{N===g.length-1&&this.onJoinComplete(R,l,f,o)})}):this.onJoinComplete(e,l,f,o)})}dropPendingRefs(){D.all(document,`[${Nt}="${this.id}"][${ot}]`,e=>{e.removeAttribute(ot),e.removeAttribute(Nt)})}onJoinComplete({live_patch:e},t,i,s){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,t,i,s);D.findPhxChildrenInFragment(t,this.id).filter(l=>{let f=l.id&&this.el.querySelector(`[id="${l.id}"]`),g=f&&f.getAttribute(Di);return g&&l.setAttribute(Di,g),this.joinChild(l)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,s)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,t,i,s)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,s)])}attachTrueDocEl(){this.el=D.byId(this.id),this.el.setAttribute(Oi,this.root.id)}execNewMounted(){D.all(this.el,`[${this.binding(Ai)}], [data-phx-${Ai}]`,e=>{this.maybeAddNewHook(e)}),D.all(this.el,`[${this.binding(wo)}]`,e=>this.maybeMounted(e))}applyJoinPatch(e,t,i,s){this.attachTrueDocEl();let o=new vn(this,this.el,this.id,t,i,null);if(o.markPrunableContentForRemoval(),this.performPatch(o,!1),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(s),this.applyPendingUpdates(),e){let{kind:l,to:f}=e;this.liveSocket.historyPatch(f,l)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,t){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,t]);let i=this.getHook(e),s=i&&D.isIgnored(e,this.binding(Sn));if(i&&!e.isEqualNode(t)&&!(s&&Gu(e.dataset,t.dataset)))return i.__beforeUpdate(),i}maybeMounted(e){let t=e.getAttribute(this.binding(wo)),i=t&&D.private(e,"mounted");t&&!i&&(this.liveSocket.execJS(e,t),D.putPrivate(e,"mounted",!0))}maybeAddNewHook(e,t){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,t){let i=[],s=!1,o=new Set;return e.after("added",l=>{this.liveSocket.triggerDOM("onNodeAdded",[l]),this.maybeAddNewHook(l),l.getAttribute&&this.maybeMounted(l)}),e.after("phxChildAdded",l=>{D.isPhxSticky(l)?this.liveSocket.joinRootViews():s=!0}),e.before("updated",(l,f)=>{this.triggerBeforeUpdateHook(l,f)&&o.add(l.id)}),e.after("updated",l=>{o.has(l.id)&&this.getHook(l).__updated()}),e.after("discarded",l=>{l.nodeType===Node.ELEMENT_NODE&&i.push(l)}),e.after("transitionsDiscarded",l=>this.afterElementsRemoved(l,t)),e.perform(),this.afterElementsRemoved(i,t),s}afterElementsRemoved(e,t){let i=[];e.forEach(s=>{let o=D.all(s,`[${Ie}]`),l=D.all(s,`[${this.binding(Ai)}]`);o.concat(s).forEach(f=>{let g=this.componentID(f);wt(g)&&i.indexOf(g)===-1&&i.push(g)}),l.concat(s).forEach(f=>{let g=this.getHook(f);g&&this.destroyHook(g)})}),t&&this.maybePushComponentsDestroyed(i)}joinNewChildren(){D.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(ii)][e.id]}destroyDescendent(e){for(let t in this.root.children)for(let i in this.root.children[t])if(i===e)return this.root.children[t][i].destroy()}joinChild(e){if(!this.getChildById(e.id)){let i=new Fo(e,this.liveSocket,this);return this.root.children[this.id][i.id]=i,i.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,t])=>{e.isDestroyed()||t()}),this.pendingJoinOps=[]})}update(e,t){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:t});this.rendered.mergeDiff(e);let i=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{D.findParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(o=>{this.componentPatch(this.rendered.getComponent(e,o),o)&&(i=!0)})}):To(e)||this.liveSocket.time("full patch complete",()=>{let[s,o]=this.renderContainer(e,"update"),l=new vn(this,this.el,this.id,s,o,null);i=this.performPatch(l,!0)}),this.liveSocket.dispatchEvents(t),i&&this.joinNewChildren()}renderContainer(e,t){return this.liveSocket.time(`toString diff (${t})`,()=>{let i=this.el.tagName,s=e?this.rendered.componentCIDs(e).concat(this.pruningCIDs):null,[o,l]=this.rendered.toString(s);return[`<${i}>${o}</${i}>`,l]})}componentPatch(e,t){if(To(e))return!1;let[i,s]=this.rendered.componentToString(t),o=new vn(this,this.el,this.id,i,s,t);return this.performPatch(o,!0)}getHook(e){return this.viewHooks[Li.elementID(e)]}addHook(e){if(Li.elementID(e)||!e.getAttribute)return;let t=e.getAttribute(`data-phx-${Ai}`)||e.getAttribute(this.binding(Ai));if(t&&!this.ownsElement(e))return;let i=this.liveSocket.getHookCallbacks(t);if(i){e.id||_e(`no DOM ID for hook "${t}". Hooks require a unique ID on each element.`,e);let s=new Li(this,e,i);return this.viewHooks[Li.elementID(s.el)]=s,s}else t!==null&&_e(`unknown hook found for "${t}"`,e)}destroyHook(e){e.__destroyed(),e.__cleanup__(),delete this.viewHooks[Li.elementID(e.el)]}applyPendingUpdates(){this.pendingDiffs.forEach(({diff:e,events:t})=>this.update(e,t)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates())}eachChild(e){let t=this.root.children[this.id]||{};for(let i in t)e(this.getChildById(i))}onChannel(e,t){this.liveSocket.onChannel(this.channel,e,i=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>t(i)]):this.liveSocket.requestDOMUpdate(()=>t(i))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:t,events:i})=>this.update(t,i))})}),this.onChannel("redirect",({to:e,flash:t})=>this.onRedirect({to:e,flash:t})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:t,kind:i,flash:s}=e,o=this.expandURL(t);this.liveSocket.historyRedirect(o,i,s)}onLivePatch(e){let{to:t,kind:i}=e;this.href=this.expandURL(t),this.liveSocket.historyPatch(t,i)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:t}){this.liveSocket.redirect(e,t)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=t=>{t=t||function(){},e?e(this.joinCount,t):t()},this.liveSocket.wrapPush(this,{timeout:!1},()=>this.channel.join().receive("ok",t=>{this.isDestroyed()||this.liveSocket.requestDOMUpdate(()=>this.onJoin(t))}).receive("error",t=>!this.isDestroyed()&&this.onJoinError(t)).receive("timeout",()=>!this.isDestroyed()&&this.onJoinError({reason:"timeout"})))}onJoinError(e){if(e.reason==="reload")return this.log("error",()=>[`failed mount with ${e.status}. Falling back to page request`,e]),this.onRedirect({to:this.href});if(e.reason==="unauthorized"||e.reason==="stale")return this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.onRedirect({to:this.href});if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);this.log("error",()=>["unable to join",e]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this)}onClose(e){if(!this.isDestroyed()){if(this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(Uu)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||this.displayError()}displayError(){this.isMain()&&D.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(_r,bo),this.execAll(this.binding("disconnected"))}pushWithReply(e,t,i,s=function(){}){if(!this.isConnected())return;let[o,[l],f]=e?e():[null,[],{}],g=function(){};return(f.page_loading||l&&l.getAttribute(this.binding(mo))!==null)&&(g=this.liveSocket.withPageLoading({kind:"element",target:l})),typeof i.cid!="number"&&delete i.cid,this.liveSocket.wrapPush(this,{timeout:!0},()=>this.channel.push(t,i,qu).receive("ok",v=>{let w=S=>{v.redirect&&this.onRedirect(v.redirect),v.live_patch&&this.onLivePatch(v.live_patch),v.live_redirect&&this.onLiveRedirect(v.live_redirect),o!==null&&this.undoRefs(o),g(),s(v,S)};v.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",v.diff,({diff:S,reply:N,events:R})=>{this.update(S,R),w(N)})}):w(null)}))}undoRefs(e){!this.isConnected()||D.all(document,`[${Nt}="${this.id}"][${ot}="${e}"]`,t=>{let i=t.getAttribute(un);t.removeAttribute(ot),t.removeAttribute(Nt),t.getAttribute(Sr)!==null&&(t.readOnly=!1,t.removeAttribute(Sr)),i!==null&&(t.disabled=i==="true",t.removeAttribute(un)),Mo.forEach(l=>D.removeClass(t,l));let s=t.getAttribute(cn);s!==null&&(t.innerText=s,t.removeAttribute(cn));let o=D.private(t,ot);if(o){let l=this.triggerBeforeUpdateHook(t,o);vn.patchEl(t,o,this.liveSocket.getActiveElement()),l&&l.__updated(),D.deletePrivate(t,ot)}})}putRef(e,t,i={}){let s=this.ref++,o=this.binding(Or);return i.loading&&(e=e.concat(D.all(document,i.loading))),e.forEach(l=>{l.classList.add(`phx-${t}-loading`),l.setAttribute(ot,s),l.setAttribute(Nt,this.el.id);let f=l.getAttribute(o);f!==null&&(l.getAttribute(cn)||l.setAttribute(cn,l.innerText),f!==""&&(l.innerText=f),l.setAttribute("disabled",""))}),[s,e,i]}componentID(e){let t=e.getAttribute&&e.getAttribute(Ie);return t?parseInt(t):null}targetComponentID(e,t,i={}){if(wt(t))return t;let s=e.getAttribute(this.binding("target"));return wt(s)?parseInt(s):t&&(s!==null||i.target)?this.closestComponentID(t):null}closestComponentID(e){return wt(e)?e:e?St(e.closest(`[${Ie}]`),t=>this.ownsElement(t)&&this.componentID(t)):null}pushHookEvent(e,t,i,s){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",t,i]),!1;let[o,l,f]=this.putRef([],"hook");return this.pushWithReply(()=>[o,l,f],"event",{type:"hook",event:t,value:i,cid:this.closestComponentID(e)},(g,v)=>s(v,o)),o}extractMeta(e,t,i){let s=this.binding("value-");for(let o=0;o<e.attributes.length;o++){t||(t={});let l=e.attributes[o].name;l.startsWith(s)&&(t[l.replace(s,"")]=e.getAttribute(l))}if(e.value!==void 0&&(t||(t={}),t.value=e.value,e.tagName==="INPUT"&&Ho.indexOf(e.type)>=0&&!e.checked&&delete t.value),i){t||(t={});for(let o in i)t[o]=i[o]}return t}pushEvent(e,t,i,s,o,l={}){this.pushWithReply(()=>this.putRef([t],e,l),"event",{type:e,event:s,value:this.extractMeta(t,o,l.value),cid:this.targetComponentID(t,i,l)})}pushFileProgress(e,t,i,s=function(){}){this.liveSocket.withinOwners(e.form,(o,l)=>{o.pushWithReply(null,"progress",{event:e.getAttribute(o.binding($u)),ref:e.getAttribute(Mt),entry_ref:t,progress:i,cid:o.targetComponentID(e.form,l)},s)})}pushInput(e,t,i,s,o,l){let f,g=wt(i)?i:this.targetComponentID(e.form,t),v=()=>this.putRef([e,e.form],"change",o),w;e.getAttribute(this.binding("change"))?w=yn(e.form,{_target:o._target},[e.name]):w=yn(e.form,{_target:o._target}),D.isUploadInput(e)&&e.files&&e.files.length>0&&xe.trackFiles(e,Array.from(e.files)),f=xe.serializeUploads(e);let S={type:"form",event:s,value:w,uploads:f,cid:g};this.pushWithReply(v,"event",S,N=>{if(D.showError(e,this.liveSocket.binding(_n)),D.isUploadInput(e)&&e.getAttribute("data-phx-auto-upload")!==null){if(xe.filesAwaitingPreflight(e).length>0){let[R,P]=v();this.uploadFiles(e.form,t,R,g,j=>{l&&l(N),this.triggerAwaitingSubmit(e.form)})}}else l&&l(N)})}triggerAwaitingSubmit(e){let t=this.getScheduledSubmit(e);if(t){let[i,s,o,l]=t;this.cancelSubmit(e),l()}}getScheduledSubmit(e){return this.formSubmits.find(([t,i,s,o])=>t.isSameNode(e))}scheduleSubmit(e,t,i,s){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,t,i,s])}cancelSubmit(e){this.formSubmits=this.formSubmits.filter(([t,i,s])=>t.isSameNode(e)?(this.undoRefs(i),!1):!0)}disableForm(e,t={}){let i=S=>!(Ri(S,`${this.binding(Sn)}=ignore`,S.form)||Ri(S,"data-phx-update=ignore",S.form)),s=S=>S.hasAttribute(this.binding(Or)),o=S=>S.tagName=="BUTTON",l=S=>["INPUT","TEXTAREA","SELECT"].includes(S.tagName),f=Array.from(e.elements),g=f.filter(s),v=f.filter(o).filter(i),w=f.filter(l).filter(i);return v.forEach(S=>{S.setAttribute(un,S.disabled),S.disabled=!0}),w.forEach(S=>{S.setAttribute(Sr,S.readOnly),S.readOnly=!0,S.files&&(S.setAttribute(un,S.disabled),S.disabled=!0)}),e.setAttribute(this.binding(mo),""),this.putRef([e].concat(g).concat(v).concat(w),"submit",t)}pushFormSubmit(e,t,i,s,o,l){let f=()=>this.disableForm(e,o),g=this.targetComponentID(e,t);if(xe.hasUploadsInProgress(e)){let[v,w]=f(),S=()=>this.pushFormSubmit(e,s,t,i,o,l);return this.scheduleSubmit(e,v,o,S)}else if(xe.inputsAwaitingPreflight(e).length>0){let[v,w]=f(),S=()=>[v,w,o];this.uploadFiles(e,t,v,g,N=>{let R=yn(e,{submitter:s});this.pushWithReply(S,"event",{type:"form",event:i,value:R,cid:g},l)})}else{let v=yn(e,{submitter:s});this.pushWithReply(f,"event",{type:"form",event:i,value:v,cid:g},l)}}uploadFiles(e,t,i,s,o){let l=this.joinCount,f=xe.activeFileInputs(e),g=f.length;f.forEach(v=>{let w=new xe(v,this,()=>{g--,g===0&&o()});this.uploaders[v]=w;let S=w.entries().map(R=>R.toPreflightPayload()),N={ref:v.getAttribute(Mt),entries:S,cid:this.targetComponentID(v.form,t)};this.log("upload",()=>["sending preflight request",N]),this.pushWithReply(null,"allow_upload",N,R=>{if(this.log("upload",()=>["got preflight response",R]),R.error){this.undoRefs(i);let[P,j]=R.error;this.log("upload",()=>[`error for entry ${P}`,j])}else{let P=j=>{this.channel.onError(()=>{this.joinCount===l&&j()})};w.initAdapterUpload(R,P,this.liveSocket)}})})}dispatchUploads(e,t){let i=D.findUploadInputs(this.el).filter(s=>s.name===e);i.length===0?_e(`no live file inputs found matching the name "${e}"`):i.length>1?_e(`duplicate live file inputs found matching the name "${e}"`):D.dispatchEvent(i[0],jo,{detail:{files:t}})}pushFormRecovery(e,t,i){this.liveSocket.withinOwners(e,(s,o)=>{let l=Array.from(e.elements).find(g=>D.isFormInput(g)&&g.type!=="hidden"&&!g.hasAttribute(this.binding("change"))),f=e.getAttribute(this.binding(_o))||e.getAttribute(this.binding("change"));st.exec("change",f,s,l,["push",{_target:l.name,newCid:t,callback:i}])})}pushLinkPatch(e,t,i){let s=this.liveSocket.setPendingLink(e),o=t?()=>this.putRef([t],"click"):null,l=()=>this.liveSocket.redirect(window.location.href),f=this.pushWithReply(o,"live_patch",{url:e},g=>{this.liveSocket.requestDOMUpdate(()=>{g.link_redirect?this.liveSocket.replaceMain(e,null,i,s):(this.liveSocket.commitPendingLink(s)&&(this.href=e),this.applyPendingUpdates(),i&&i(s))})});f?f.receive("timeout",l):l()}formsForRecovery(e){if(this.joinCount===0)return[];let t=this.binding("change"),i=document.createElement("template");return i.innerHTML=e,D.all(this.el,`form[${t}]`).filter(s=>s.id&&this.ownsElement(s)).filter(s=>s.elements.length>0).filter(s=>s.getAttribute(this.binding(_o))!=="ignore").map(s=>{let o=i.content.querySelector(`form[id="${s.id}"][${t}="${s.getAttribute(t)}"]`);return o?[s,o,this.targetComponentID(o)]:[s,null,null]}).filter(([s,o,l])=>o)}maybePushComponentsDestroyed(e){let t=e.filter(i=>D.findComponentNodeList(this.el,i).length===0);t.length>0&&(this.pruningCIDs.push(...t),this.pushWithReply(null,"cids_will_destroy",{cids:t},()=>{this.pruningCIDs=this.pruningCIDs.filter(s=>t.indexOf(s)!==-1);let i=t.filter(s=>D.findComponentNodeList(this.el,s).length===0);i.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:i},s=>{this.rendered.pruneCIDs(s.cids)})}))}ownsElement(e){let t=e.closest(ri);return e.getAttribute(ii)===this.id||t&&t.id===this.id||!t&&this.isDead}submitForm(e,t,i,s,o={}){D.putPrivate(e,wn,!0);let l=this.liveSocket.binding(_n),f=Array.from(e.elements);f.forEach(g=>D.putPrivate(g,wn,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,t,i,s,o,()=>{f.forEach(g=>D.showError(g,l)),this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}},Uo=class{constructor(e,t,i={}){if(this.unloaded=!1,!t||t.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new t(e,i),this.bindingPrefix=i.bindingPrefix||Bu,this.opts=i,this.params=Tr(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(xn(Wu),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=xn(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||Fu,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||Tu,this.reloadJitterMin=i.reloadJitterMin||ku,this.reloadJitterMax=i.reloadJitterMax||Pu,this.failsafeJitter=i.failsafeJitter||Lu,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.domCallbacks=Object.assign({onNodeAdded:Tr(),onBeforeElUpdated:Tr()},i.dom||{}),this.transitions=new yc,window.addEventListener("pagehide",s=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}isProfileEnabled(){return this.sessionStorage.getItem(Cr)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(fn)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(fn)==="false"}enableDebug(){this.sessionStorage.setItem(fn,"true")}enableProfiling(){this.sessionStorage.setItem(Cr,"true")}disableDebug(){this.sessionStorage.setItem(fn,"false")}disableProfiling(){this.sessionStorage.removeItem(Cr)}enableLatencySim(e){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(Ar,e)}disableLatencySim(){this.sessionStorage.removeItem(Ar)}getLatencySim(){let e=this.sessionStorage.getItem(Ar);return e?parseInt(e):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let e=()=>{this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?e():document.addEventListener("DOMContentLoaded",()=>e())}disconnect(e){clearTimeout(this.reloadWithJitterTimer),this.socket.disconnect(e)}replaceTransport(e){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(e),this.connect()}execJS(e,t,i=null){this.owner(e,s=>st.exec(i,t,s,e))}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(e,t){this.domCallbacks[e](...t)}time(e,t){if(!this.isProfileEnabled()||!console.time)return t();console.time(e);let i=t();return console.timeEnd(e),i}log(e,t,i){if(this.viewLogger){let[s,o]=i();this.viewLogger(e,t,s,o)}else if(this.isDebugEnabled()){let[s,o]=i();Ku(e,t,s,o)}}requestDOMUpdate(e){this.transitions.after(e)}transition(e,t,i=function(){}){this.transitions.addTransition(e,t,i)}onChannel(e,t,i){e.on(t,s=>{let o=this.getLatencySim();o?setTimeout(()=>i(s),o):i(s)})}wrapPush(e,t,i){let s=this.getLatencySim(),o=e.joinCount;if(!s)return this.isConnected()&&t.timeout?i().receive("timeout",()=>{e.joinCount===o&&!e.isDestroyed()&&this.reloadWithJitter(e,()=>{this.log(e,"timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}):i();let l={receives:[],receive(f,g){this.receives.push([f,g])}};return setTimeout(()=>{e.isDestroyed()||l.receives.reduce((f,[g,v])=>f.receive(g,v),i())},s),l}reloadWithJitter(e,t){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,s=this.reloadJitterMax,o=Math.floor(Math.random()*(s-i+1))+i,l=rt.updateLocal(this.localStorage,window.location.pathname,No,0,f=>f+1);l>this.maxReloads&&(o=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{e.isDestroyed()||e.isConnected()||(e.destroy(),t?t():this.log(e,"join",()=>[`encountered ${l} consecutive reloads`]),l>this.maxReloads&&this.log(e,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},o)}getHookCallbacks(e){return e&&e.startsWith("Phoenix.")?tc[e.split(".")[1]]:this.hooks[e]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(e){return`${this.getBindingPrefix()}${e}`}channel(e,t){return this.socket.channel(e,t)}joinDeadView(){let e=document.body;if(e&&!this.isPhxView(e)&&!this.isPhxView(document.firstElementChild)){let t=this.newRootView(e);t.setHref(this.getHref()),t.joinDead(),this.main||(this.main=t),window.requestAnimationFrame(()=>t.execNewMounted())}}joinRootViews(){let e=!1;return D.all(document,`${ri}:not([${ii}])`,t=>{if(!this.getRootById(t.id)){let i=this.newRootView(t);i.setHref(this.getHref()),i.join(),t.hasAttribute(Nr)&&(this.main=i)}e=!0}),e}redirect(e,t){this.unload(),rt.redirect(e,t)}replaceMain(e,t,i=null,s=this.setPendingLink(e)){let o=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let l=D.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(l,t,o),this.main.setRedirect(e),this.transitionRemoves(),this.main.join((f,g)=>{f===1&&this.commitPendingLink(s)&&this.requestDOMUpdate(()=>{D.findPhxSticky(document).forEach(v=>l.appendChild(v)),this.outgoingMainEl.replaceWith(l),this.outgoingMainEl=null,i&&requestAnimationFrame(i),g()})})}transitionRemoves(e){let t=this.binding("remove");e=e||D.all(document,`[${t}]`),e.forEach(i=>{document.body.contains(i)&&this.execJS(i,i.getAttribute(t),"remove")})}isPhxView(e){return e.getAttribute&&e.getAttribute(Et)!==null}newRootView(e,t,i){let s=new Fo(e,this,null,t,i);return this.roots[s.id]=s,s}owner(e,t){let i=St(e.closest(ri),s=>this.getViewByEl(s))||this.main;i&&t(i)}withinOwners(e,t){this.owner(e,i=>t(i,e))}getViewByEl(e){let t=e.getAttribute(Oi);return St(this.getRootById(t),i=>i.getDescendentByEl(e))}getRootById(e){return this.roots[e]}destroyAllViews(){for(let e in this.roots)this.roots[e].destroy(),delete this.roots[e];this.main=null}destroyViewByEl(e){let t=this.getRootById(e.getAttribute(Oi));t&&t.id===e.id?(t.destroy(),delete this.roots[t.id]):t&&t.destroyDescendent(e.id)}setActiveElement(e){if(this.activeElement===e)return;this.activeElement=e;let t=()=>{e===this.activeElement&&(this.activeElement=null),e.removeEventListener("mouseup",this),e.removeEventListener("touchend",this)};e.addEventListener("mouseup",t),e.addEventListener("touchend",t)}getActiveElement(){return document.activeElement===document.body?this.activeElement||document.activeElement:document.activeElement||document.body}dropActiveElement(e){this.prevActive&&e.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:e}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.socket.onClose(t=>{if(t&&t.code===1001)return this.unload();if(t&&t.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",t=>{t.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),e||this.bindNav(),this.bindClicks(),e||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(t,i,s,o,l,f)=>{let g=o.getAttribute(this.binding(Hu)),v=t.key&&t.key.toLowerCase();if(g&&g.toLowerCase()!==v)return;let w=dt({key:t.key},this.eventMeta(i,t,o));st.exec(i,l,s,o,["push",{data:w}])}),this.bind({blur:"focusout",focus:"focusin"},(t,i,s,o,l,f)=>{if(!f){let g=dt({key:t.key},this.eventMeta(i,t,o));st.exec(i,l,s,o,["push",{data:g}])}}),this.bind({blur:"blur",focus:"focus"},(t,i,s,o,l,f,g)=>{if(g==="window"){let v=this.eventMeta(i,t,o);st.exec(i,f,s,o,["push",{data:v}])}}),window.addEventListener("dragover",t=>t.preventDefault()),window.addEventListener("drop",t=>{t.preventDefault();let i=St(Ri(t.target,this.binding(go)),l=>l.getAttribute(this.binding(go))),s=i&&document.getElementById(i),o=Array.from(t.dataTransfer.files||[]);!s||s.disabled||o.length===0||!(s.files instanceof FileList)||(xe.trackFiles(s,o,t.dataTransfer),s.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(jo,t=>{let i=t.target;if(!D.isUploadInput(i))return;let s=Array.from(t.detail.files||[]).filter(o=>o instanceof File||o instanceof Blob);xe.trackFiles(i,s),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(e,t,i){let s=this.metadataCallbacks[e];return s?s(t,i):{}}setPendingLink(e){return this.linkRef++,this.pendingLink=e,this.linkRef}commitPendingLink(e){return this.linkRef!==e?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(e,t){for(let i in e){let s=e[i];this.on(s,o=>{let l=this.binding(i),f=this.binding(`window-${i}`),g=o.target.getAttribute&&o.target.getAttribute(l);g?this.debounce(o.target,o,s,()=>{this.withinOwners(o.target,v=>{t(o,i,v,o.target,g,null)})}):D.all(document,`[${f}]`,v=>{let w=v.getAttribute(f);this.debounce(v,o,s,()=>{this.withinOwners(v,S=>{t(o,i,S,v,w,"window")})})})})}}bindClicks(){window.addEventListener("click",e=>this.clickStartedAtTarget=e.target),this.bindClick("click","click",!1),this.bindClick("mousedown","capture-click",!0)}bindClick(e,t,i){let s=this.binding(t);window.addEventListener(e,o=>{let l=null;if(i)l=o.target.matches(`[${s}]`)?o.target:o.target.querySelector(`[${s}]`);else{let g=this.clickStartedAtTarget||o.target;l=Ri(g,s),this.dispatchClickAway(o,g),this.clickStartedAtTarget=null}let f=l&&l.getAttribute(s);if(!f){let g=o.target instanceof HTMLAnchorElement?o.target.getAttribute("href"):null;!i&&g!==null&&!D.wantsNewTab(o)&&D.isNewPageHref(g,window.location)&&this.unload();return}l.getAttribute("href")==="#"&&o.preventDefault(),this.debounce(l,o,"click",()=>{this.withinOwners(l,g=>{st.exec("click",f,g,l,["push",{data:this.eventMeta("click",o,l)}])})})},i)}dispatchClickAway(e,t){let i=this.binding("click-away");D.all(document,`[${i}]`,s=>{s.isSameNode(t)||s.contains(t)||this.withinOwners(e.target,o=>{let l=s.getAttribute(i);st.isVisible(s)&&st.exec("click",l,o,s,["push",{data:this.eventMeta("click",e,e.target)}])})})}bindNav(){if(!rt.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let e=null;window.addEventListener("scroll",t=>{clearTimeout(e),e=setTimeout(()=>{rt.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",t=>{if(!this.registerNewLocation(window.location))return;let{type:i,id:s,root:o,scroll:l}=t.state||{},f=window.location.href;this.requestDOMUpdate(()=>{this.main.isConnected()&&i==="patch"&&s===this.main.id?this.main.pushLinkPatch(f,null,()=>{this.maybeScroll(l)}):this.replaceMain(f,null,()=>{o&&this.replaceRootHistory(),this.maybeScroll(l)})})},!1),window.addEventListener("click",t=>{let i=Ri(t.target,xr),s=i&&i.getAttribute(xr);if(!s||!this.isConnected()||!this.main||D.wantsNewTab(t))return;let o=i.href,l=i.getAttribute(Ru);t.preventDefault(),t.stopImmediatePropagation(),this.pendingLink!==o&&this.requestDOMUpdate(()=>{if(s==="patch")this.pushHistoryPatch(o,l,i);else if(s==="redirect")this.historyRedirect(o,l);else throw new Error(`expected ${xr} to be "patch" or "redirect", got: ${s}`);let f=i.getAttribute(this.binding("click"));f&&this.requestDOMUpdate(()=>this.execJS(i,f,"click"))})},!1)}maybeScroll(e){typeof e=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,e)})}dispatchEvent(e,t={}){D.dispatchEvent(window,`phx:${e}`,{detail:t})}dispatchEvents(e){e.forEach(([t,i])=>this.dispatchEvent(t,i))}withPageLoading(e,t){D.dispatchEvent(window,"phx:page-loading-start",{detail:e});let i=()=>D.dispatchEvent(window,"phx:page-loading-stop",{detail:e});return t?t(i):i}pushHistoryPatch(e,t,i){if(!this.isConnected())return rt.redirect(e);this.withPageLoading({to:e,kind:"patch"},s=>{this.main.pushLinkPatch(e,i,o=>{this.historyPatch(e,t,o),s()})})}historyPatch(e,t,i=this.setPendingLink(e)){!this.commitPendingLink(i)||(rt.pushState(t,{type:"patch",id:this.main.id},e),this.registerNewLocation(window.location))}historyRedirect(e,t,i){if(!this.isConnected())return rt.redirect(e,i);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:o,host:l}=window.location;e=`${o}//${l}${e}`}let s=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},o=>{this.replaceMain(e,i,()=>{rt.pushState(t,{type:"redirect",id:this.main.id,scroll:s},e),this.registerNewLocation(window.location),o()})})}replaceRootHistory(){rt.pushState("replace",{root:!0,type:"patch",id:this.main.id})}registerNewLocation(e){let{pathname:t,search:i}=this.currentLocation;return t+i===e.pathname+e.search?!1:(this.currentLocation=xn(e),!0)}bindForms(){let e=0,t=!1;this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit")),o=i.target.getAttribute(this.binding("change"));!t&&o&&!s&&(t=!0,i.preventDefault(),this.withinOwners(i.target,l=>{l.disableForm(i.target),window.requestAnimationFrame(()=>{D.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))},!0),this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit"));if(!s){D.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,o=>{st.exec("submit",s,o,i.target,["push",{submitter:i.submitter}])})},!1);for(let i of["change","input"])this.on(i,s=>{let o=this.binding("change"),l=s.target,f=l.getAttribute(o),g=l.form&&l.form.getAttribute(o),v=f||g;if(!v||l.type==="number"&&l.validity&&l.validity.badInput)return;let w=f?l:l.form,S=e;e++;let{at:N,type:R}=D.private(l,"prev-iteration")||{};N===S-1&&i!==R||(D.putPrivate(l,"prev-iteration",{at:S,type:i}),this.debounce(l,s,i,()=>{this.withinOwners(w,P=>{D.putPrivate(l,Rr,!0),D.isTextualInput(l)||this.setActiveElement(l),st.exec("change",v,P,l,["push",{_target:s.target.name,dispatcher:w}])})}))},!1);this.on("reset",i=>{let s=i.target;D.resetForm(s,this.binding(_n));let o=Array.from(s.elements).find(l=>l.type==="reset");window.requestAnimationFrame(()=>{o.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(e,t,i,s){if(i==="blur"||i==="focusout")return s();let o=this.binding(Mu),l=this.binding(ju),f=this.defaults.debounce.toString(),g=this.defaults.throttle.toString();this.withinOwners(e,v=>{let w=()=>!v.isDestroyed()&&document.body.contains(e);D.debounce(e,t,o,f,l,g,w,()=>{s()})})}silenceEvents(e){this.silenced=!0,e(),this.silenced=!1}on(e,t){window.addEventListener(e,i=>{this.silenced||t(i)})}},yc=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(e=>{clearTimeout(e),this.transitions.delete(e)}),this.flushPendingOps()}after(e){this.size()===0?e():this.pushPendingOp(e)}addTransition(e,t,i){t();let s=setTimeout(()=>{this.transitions.delete(s),i(),this.flushPendingOps()},e);this.transitions.add(s)}pushPendingOp(e){this.pendingOps.push(e)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let e=this.pendingOps.shift();e&&(e(),this.flushPendingOps())}};var Wn=vr(qo());var jt={},Mr=null;jt.Notification={alert(e,t){var i=document.getElementById(e+"-notification"),s=document.getElementById(e+"-notification-panel"),o=document.getElementById(e+"-notification-msg");s.classList.remove("translate-y-2","opacity-0","sm:translate-y-0","sm:translate-x-2"),s.classList.add("translate-y-0","opacity-100","sm:translate-x-0"),i.classList.remove("hidden"),o.textContent=t},mounted(){this.handleEvent("notification",({message:e})=>{var t=document.getElementById("modal-close");t&&t.click(),e.info?(this.alert("info",e.info),e.push_to&&this.pushEvent(e.push_to,e.params)):this.alert("error",e.error)})}};jt.HandleConfirmation={params(){return this.el.dataset},mounted(){this.el.addEventListener("click",e=>{this.params().isconfirm=="YES"&&(this.el.disabled=!0,document.getElementById("modal-confirm-msg").classList.add("hidden"),document.getElementById("modal-processing-msg").classList.remove("hidden"),this.pushEvent(Mr.event,Mr))})}};jt.Confirm={params(){return this.el.dataset},mounted(){this.el.addEventListener("click",e=>{Mr=this.params()})}};jt.PrintPage={mounted(){var e=window.open("","PRINT","height=400,width=600");e.document.write("<html><head><title>"+document.title+"</title>"),e.document.write("</head><body >"),e.document.write("<h1>"+document.title+"</h1>"),e.document.write(document.getElementById(elem).innerHTML),e.document.write("</body></html>"),e.document.close(),e.focus(),e.print(),e.close()}};jt.UserRoleCheckboxes={mounted(){this.updateCheckboxes()},updated(){this.updateCheckboxes()},updateCheckboxes(){this.el.querySelectorAll("[data-select-val]").forEach(t=>{let i=t.getAttribute("data-select-val");t.checked=i==="Y"})}};jt.live_select;var Wo=jt;var Br=!1,qr=!1,Ft=[],Wr=-1;function bc(e){xc(e)}function xc(e){Ft.includes(e)||Ft.push(e),_c()}function sa(e){let t=Ft.indexOf(e);t!==-1&&t>Wr&&Ft.splice(t,1)}function _c(){!qr&&!Br&&(Br=!0,queueMicrotask(wc))}function wc(){Br=!1,qr=!0;for(let e=0;e<Ft.length;e++)Ft[e](),Wr=e;Ft.length=0,Wr=-1,qr=!1}var ai,Wt,li,oa,Vr=!0;function Sc(e){Vr=!1,e(),Vr=!0}function Ec(e){ai=e.reactive,li=e.release,Wt=t=>e.effect(t,{scheduler:i=>{Vr?bc(i):i()}}),oa=e.raw}function Vo(e){Wt=e}function Cc(e){let t=()=>{};return[s=>{let o=Wt(s);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(l=>l())}),e._x_effects.add(o),t=()=>{o!==void 0&&(e._x_effects.delete(o),li(o))},o},()=>{t()}]}function aa(e,t){let i=!0,s,o=Wt(()=>{let l=e();JSON.stringify(l),i?s=l:queueMicrotask(()=>{t(l,s),s=l}),i=!1});return()=>li(o)}var la=[],ua=[],ca=[];function Ac(e){ca.push(e)}function ss(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,ua.push(t))}function fa(e){la.push(e)}function da(e,t,i){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(i)}function ha(e,t){!e._x_attributeCleanups||Object.entries(e._x_attributeCleanups).forEach(([i,s])=>{(t===void 0||t.includes(i))&&(s.forEach(o=>o()),delete e._x_attributeCleanups[i])})}function Tc(e){if(e._x_cleanups)for(;e._x_cleanups.length;)e._x_cleanups.pop()()}var os=new MutationObserver(cs),as=!1;function ls(){os.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),as=!0}function pa(){kc(),os.disconnect(),as=!1}var Ii=[];function kc(){let e=os.takeRecords();Ii.push(()=>e.length>0&&cs(e));let t=Ii.length;queueMicrotask(()=>{if(Ii.length===t)for(;Ii.length>0;)Ii.shift()()})}function ve(e){if(!as)return e();pa();let t=e();return ls(),t}var us=!1,Rn=[];function Pc(){us=!0}function Lc(){us=!1,cs(Rn),Rn=[]}function cs(e){if(us){Rn=Rn.concat(e);return}let t=new Set,i=new Set,s=new Map,o=new Map;for(let l=0;l<e.length;l++)if(!e[l].target._x_ignoreMutationObserver&&(e[l].type==="childList"&&(e[l].addedNodes.forEach(f=>f.nodeType===1&&t.add(f)),e[l].removedNodes.forEach(f=>f.nodeType===1&&i.add(f))),e[l].type==="attributes")){let f=e[l].target,g=e[l].attributeName,v=e[l].oldValue,w=()=>{s.has(f)||s.set(f,[]),s.get(f).push({name:g,value:f.getAttribute(g)})},S=()=>{o.has(f)||o.set(f,[]),o.get(f).push(g)};f.hasAttribute(g)&&v===null?w():f.hasAttribute(g)?(S(),w()):S()}o.forEach((l,f)=>{ha(f,l)}),s.forEach((l,f)=>{la.forEach(g=>g(f,l))});for(let l of i)t.has(l)||ua.forEach(f=>f(l));t.forEach(l=>{l._x_ignoreSelf=!0,l._x_ignore=!0});for(let l of t)i.has(l)||!l.isConnected||(delete l._x_ignoreSelf,delete l._x_ignore,ca.forEach(f=>f(l)),l._x_ignore=!0,l._x_ignoreSelf=!0);t.forEach(l=>{delete l._x_ignoreSelf,delete l._x_ignore}),t=null,i=null,s=null,o=null}function ga(e){return Bi(si(e))}function Ui(e,t,i){return e._x_dataStack=[t,...si(i||e)],()=>{e._x_dataStack=e._x_dataStack.filter(s=>s!==t)}}function si(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?si(e.host):e.parentNode?si(e.parentNode):[]}function Bi(e){return new Proxy({objects:e},Dc)}var Dc={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(i=>Object.prototype.hasOwnProperty.call(i,t)||Reflect.has(i,t))},get({objects:e},t,i){return t=="toJSON"?Rc:Reflect.get(e.find(s=>Reflect.has(s,t))||{},t,i)},set({objects:e},t,i,s){let o=e.find(f=>Object.prototype.hasOwnProperty.call(f,t))||e[e.length-1],l=Object.getOwnPropertyDescriptor(o,t);return(l==null?void 0:l.set)&&(l==null?void 0:l.get)?l.set.call(s,i)||!0:Reflect.set(o,t,i)}};function Rc(){return Reflect.ownKeys(this).reduce((t,i)=>(t[i]=Reflect.get(this,i),t),{})}function va(e){let t=s=>typeof s=="object"&&!Array.isArray(s)&&s!==null,i=(s,o="")=>{Object.entries(Object.getOwnPropertyDescriptors(s)).forEach(([l,{value:f,enumerable:g}])=>{if(g===!1||f===void 0||typeof f=="object"&&f!==null&&f.__v_skip)return;let v=o===""?l:`${o}.${l}`;typeof f=="object"&&f!==null&&f._x_interceptor?s[l]=f.initialize(e,v,l):t(f)&&f!==s&&!(f instanceof Element)&&i(f,v)})};return i(e)}function ma(e,t=()=>{}){let i={initialValue:void 0,_x_interceptor:!0,initialize(s,o,l){return e(this.initialValue,()=>Oc(s,o),f=>Jr(s,o,f),o,l)}};return t(i),s=>{if(typeof s=="object"&&s!==null&&s._x_interceptor){let o=i.initialize.bind(i);i.initialize=(l,f,g)=>{let v=s.initialize(l,f,g);return i.initialValue=v,o(l,f,g)}}else i.initialValue=s;return i}}function Oc(e,t){return t.split(".").reduce((i,s)=>i[s],e)}function Jr(e,t,i){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=i;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Jr(e[t[0]],t.slice(1),i)}}var ya={};function Ve(e,t){ya[e]=t}function zr(e,t){return Object.entries(ya).forEach(([i,s])=>{let o=null;function l(){if(o)return o;{let[f,g]=Ea(t);return o=dt({interceptor:ma},f),ss(t,g),o}}Object.defineProperty(e,`$${i}`,{get(){return s(t,l())},enumerable:!1})}),e}function Ic(e,t,i,...s){try{return i(...s)}catch(o){Fi(o,e,t)}}function Fi(e,t,i=void 0){e=Object.assign(e!=null?e:{message:"No error message given."},{el:t,expression:i}),console.warn(`Alpine Expression Error: ${e.message}

${i?'Expression: "'+i+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var Ln=!0;function ba(e){let t=Ln;Ln=!1;let i=e();return Ln=t,i}function Ut(e,t,i={}){let s;return Se(e,t)(o=>s=o,i),s}function Se(...e){return xa(...e)}var xa=_a;function Nc(e){xa=e}function _a(e,t){let i={};zr(i,e);let s=[i,...si(e)],o=typeof t=="function"?Mc(s,t):Hc(s,t,e);return Ic.bind(null,e,t,o)}function Mc(e,t){return(i=()=>{},{scope:s={},params:o=[]}={})=>{let l=t.apply(Bi([s,...e]),o);On(i,l)}}var jr={};function jc(e,t){if(jr[e])return jr[e];let i=Object.getPrototypeOf(async function(){}).constructor,s=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,l=(()=>{try{let f=new i(["__self","scope"],`with (scope) { __self.result = ${s} }; __self.finished = true; return __self.result;`);return Object.defineProperty(f,"name",{value:`[Alpine] ${e}`}),f}catch(f){return Fi(f,t,e),Promise.resolve()}})();return jr[e]=l,l}function Hc(e,t,i){let s=jc(t,i);return(o=()=>{},{scope:l={},params:f=[]}={})=>{s.result=void 0,s.finished=!1;let g=Bi([l,...e]);if(typeof s=="function"){let v=s(s,g).catch(w=>Fi(w,i,t));s.finished?(On(o,s.result,g,f,i),s.result=void 0):v.then(w=>{On(o,w,g,f,i)}).catch(w=>Fi(w,i,t)).finally(()=>s.result=void 0)}}}function On(e,t,i,s,o){if(Ln&&typeof t=="function"){let l=t.apply(i,s);l instanceof Promise?l.then(f=>On(e,f,i,s)).catch(f=>Fi(f,o,t)):e(l)}else typeof t=="object"&&t instanceof Promise?t.then(l=>e(l)):e(t)}var fs="x-";function ui(e=""){return fs+e}function $c(e){fs=e}var In={};function pe(e,t){return In[e]=t,{before(i){if(!In[i]){console.warn(String.raw`Cannot find directive \`${i}\`. \`${e}\` will use the default order of execution`);return}let s=$t.indexOf(i);$t.splice(s>=0?s:$t.indexOf("DEFAULT"),0,e)}}}function Fc(e){return Object.keys(In).includes(e)}function ds(e,t,i){if(t=Array.from(t),e._x_virtualDirectives){let l=Object.entries(e._x_virtualDirectives).map(([g,v])=>({name:g,value:v})),f=wa(l);l=l.map(g=>f.find(v=>v.name===g.name)?{name:`x-bind:${g.name}`,value:`"${g.value}"`}:g),t=t.concat(l)}let s={};return t.map(Ta((l,f)=>s[l]=f)).filter(Pa).map(qc(s,i)).sort(Wc).map(l=>Bc(e,l))}function wa(e){return Array.from(e).map(Ta()).filter(t=>!Pa(t))}var Xr=!1,ji=new Map,Sa=Symbol();function Uc(e){Xr=!0;let t=Symbol();Sa=t,ji.set(t,[]);let i=()=>{for(;ji.get(t).length;)ji.get(t).shift()();ji.delete(t)},s=()=>{Xr=!1,i()};e(i),s()}function Ea(e){let t=[],i=g=>t.push(g),[s,o]=Cc(e);return t.push(o),[{Alpine:Wi,effect:s,cleanup:i,evaluateLater:Se.bind(Se,e),evaluate:Ut.bind(Ut,e)},()=>t.forEach(g=>g())]}function Bc(e,t){let i=()=>{},s=In[t.type]||i,[o,l]=Ea(e);da(e,t.original,l);let f=()=>{e._x_ignore||e._x_ignoreSelf||(s.inline&&s.inline(e,t,o),s=s.bind(s,e,t,o),Xr?ji.get(Sa).push(s):s())};return f.runCleanups=l,f}var Ca=(e,t)=>({name:i,value:s})=>(i.startsWith(e)&&(i=i.replace(e,t)),{name:i,value:s}),Aa=e=>e;function Ta(e=()=>{}){return({name:t,value:i})=>{let{name:s,value:o}=ka.reduce((l,f)=>f(l),{name:t,value:i});return s!==t&&e(s,t),{name:s,value:o}}}var ka=[];function hs(e){ka.push(e)}function Pa({name:e}){return La().test(e)}var La=()=>new RegExp(`^${fs}([^:^.]+)\\b`);function qc(e,t){return({name:i,value:s})=>{let o=i.match(La()),l=i.match(/:([a-zA-Z0-9\-_:]+)/),f=i.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],g=t||e[i]||i;return{type:o?o[1]:null,value:l?l[1]:null,modifiers:f.map(v=>v.replace(".","")),expression:s,original:g}}}var Kr="DEFAULT",$t=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Kr,"teleport"];function Wc(e,t){let i=$t.indexOf(e.type)===-1?Kr:e.type,s=$t.indexOf(t.type)===-1?Kr:t.type;return $t.indexOf(i)-$t.indexOf(s)}function Hi(e,t,i={}){e.dispatchEvent(new CustomEvent(t,{detail:i,bubbles:!0,composed:!0,cancelable:!0}))}function At(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(o=>At(o,t));return}let i=!1;if(t(e,()=>i=!0),i)return;let s=e.firstElementChild;for(;s;)At(s,t,!1),s=s.nextElementSibling}function He(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var Jo=!1;function Vc(){Jo&&He("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Jo=!0,document.body||He("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Hi(document,"alpine:init"),Hi(document,"alpine:initializing"),ls(),Ac(t=>pt(t,At)),ss(t=>ja(t)),fa((t,i)=>{ds(t,i).forEach(s=>s())});let e=t=>!Mn(t.parentElement,!0);Array.from(document.querySelectorAll(Oa().join(","))).filter(e).forEach(t=>{pt(t)}),Hi(document,"alpine:initialized"),setTimeout(()=>{Xc()})}var ps=[],Da=[];function Ra(){return ps.map(e=>e())}function Oa(){return ps.concat(Da).map(e=>e())}function Ia(e){ps.push(e)}function Na(e){Da.push(e)}function Mn(e,t=!1){return qi(e,i=>{if((t?Oa():Ra()).some(o=>i.matches(o)))return!0})}function qi(e,t){if(!!e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return qi(e.parentElement,t)}}function Jc(e){return Ra().some(t=>e.matches(t))}var Ma=[];function zc(e){Ma.push(e)}function pt(e,t=At,i=()=>{}){Uc(()=>{t(e,(s,o)=>{i(s,o),Ma.forEach(l=>l(s,o)),ds(s,s.attributes).forEach(l=>l()),s._x_ignore&&o()})})}function ja(e,t=At){t(e,i=>{ha(i),Tc(i)})}function Xc(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,i,s])=>{Fc(i)||s.some(o=>{if(document.querySelector(o))return He(`found "${o}", but missing ${t} plugin`),!0})})}var Gr=[],gs=!1;function vs(e=()=>{}){return queueMicrotask(()=>{gs||setTimeout(()=>{Yr()})}),new Promise(t=>{Gr.push(()=>{e(),t()})})}function Yr(){for(gs=!1;Gr.length;)Gr.shift()()}function Kc(){gs=!0}function ms(e,t){return Array.isArray(t)?zo(e,t.join(" ")):typeof t=="object"&&t!==null?Gc(e,t):typeof t=="function"?ms(e,t()):zo(e,t)}function zo(e,t){let i=l=>l.split(" ").filter(Boolean),s=l=>l.split(" ").filter(f=>!e.classList.contains(f)).filter(Boolean),o=l=>(e.classList.add(...l),()=>{e.classList.remove(...l)});return t=t===!0?t="":t||"",o(s(t))}function Gc(e,t){let i=g=>g.split(" ").filter(Boolean),s=Object.entries(t).flatMap(([g,v])=>v?i(g):!1).filter(Boolean),o=Object.entries(t).flatMap(([g,v])=>v?!1:i(g)).filter(Boolean),l=[],f=[];return o.forEach(g=>{e.classList.contains(g)&&(e.classList.remove(g),f.push(g))}),s.forEach(g=>{e.classList.contains(g)||(e.classList.add(g),l.push(g))}),()=>{f.forEach(g=>e.classList.add(g)),l.forEach(g=>e.classList.remove(g))}}function jn(e,t){return typeof t=="object"&&t!==null?Yc(e,t):Qc(e,t)}function Yc(e,t){let i={};return Object.entries(t).forEach(([s,o])=>{i[s]=e.style[s],s.startsWith("--")||(s=Zc(s)),e.style.setProperty(s,o)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{jn(e,i)}}function Qc(e,t){let i=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",i||"")}}function Zc(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Qr(e,t=()=>{}){let i=!1;return function(){i?t.apply(this,arguments):(i=!0,e.apply(this,arguments))}}pe("transition",(e,{value:t,modifiers:i,expression:s},{evaluate:o})=>{typeof s=="function"&&(s=o(s)),s!==!1&&(!s||typeof s=="boolean"?tf(e,i,t):ef(e,s,t))});function ef(e,t,i){Ha(e,ms,""),{enter:o=>{e._x_transition.enter.during=o},"enter-start":o=>{e._x_transition.enter.start=o},"enter-end":o=>{e._x_transition.enter.end=o},leave:o=>{e._x_transition.leave.during=o},"leave-start":o=>{e._x_transition.leave.start=o},"leave-end":o=>{e._x_transition.leave.end=o}}[i](t)}function tf(e,t,i){Ha(e,jn);let s=!t.includes("in")&&!t.includes("out")&&!i,o=s||t.includes("in")||["enter"].includes(i),l=s||t.includes("out")||["leave"].includes(i);t.includes("in")&&!s&&(t=t.filter((k,U)=>U<t.indexOf("out"))),t.includes("out")&&!s&&(t=t.filter((k,U)=>U>t.indexOf("out")));let f=!t.includes("opacity")&&!t.includes("scale"),g=f||t.includes("opacity"),v=f||t.includes("scale"),w=g?0:1,S=v?Ni(t,"scale",95)/100:1,N=Ni(t,"delay",0)/1e3,R=Ni(t,"origin","center"),P="opacity, transform",j=Ni(t,"duration",150)/1e3,z=Ni(t,"duration",75)/1e3,I="cubic-bezier(0.4, 0.0, 0.2, 1)";o&&(e._x_transition.enter.during={transformOrigin:R,transitionDelay:`${N}s`,transitionProperty:P,transitionDuration:`${j}s`,transitionTimingFunction:I},e._x_transition.enter.start={opacity:w,transform:`scale(${S})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),l&&(e._x_transition.leave.during={transformOrigin:R,transitionDelay:`${N}s`,transitionProperty:P,transitionDuration:`${z}s`,transitionTimingFunction:I},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:w,transform:`scale(${S})`})}function Ha(e,t,i={}){e._x_transition||(e._x_transition={enter:{during:i,start:i,end:i},leave:{during:i,start:i,end:i},in(s=()=>{},o=()=>{}){Zr(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},s,o)},out(s=()=>{},o=()=>{}){Zr(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},s,o)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,i,s){let o=document.visibilityState==="visible"?requestAnimationFrame:setTimeout,l=()=>o(i);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(i):l():e._x_transition?e._x_transition.in(i):l();return}e._x_hidePromise=e._x_transition?new Promise((f,g)=>{e._x_transition.out(()=>{},()=>f(s)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>g({isFromCancelledTransition:!0}))}):Promise.resolve(s),queueMicrotask(()=>{let f=$a(e);f?(f._x_hideChildren||(f._x_hideChildren=[]),f._x_hideChildren.push(e)):o(()=>{let g=v=>{let w=Promise.all([v._x_hidePromise,...(v._x_hideChildren||[]).map(g)]).then(([S])=>S==null?void 0:S());return delete v._x_hidePromise,delete v._x_hideChildren,w};g(e).catch(v=>{if(!v.isFromCancelledTransition)throw v})})})};function $a(e){let t=e.parentNode;if(!!t)return t._x_hidePromise?t:$a(t)}function Zr(e,t,{during:i,start:s,end:o}={},l=()=>{},f=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(i).length===0&&Object.keys(s).length===0&&Object.keys(o).length===0){l(),f();return}let g,v,w;nf(e,{start(){g=t(e,s)},during(){v=t(e,i)},before:l,end(){g(),w=t(e,o)},after:f,cleanup(){v(),w()}})}function nf(e,t){let i,s,o,l=Qr(()=>{ve(()=>{i=!0,s||t.before(),o||(t.end(),Yr()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(f){this.beforeCancels.push(f)},cancel:Qr(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();l()}),finish:l},ve(()=>{t.start(),t.during()}),Kc(),requestAnimationFrame(()=>{if(i)return;let f=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,g=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;f===0&&(f=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),ve(()=>{t.before()}),s=!0,requestAnimationFrame(()=>{i||(ve(()=>{t.end()}),Yr(),setTimeout(e._x_transitioning.finish,f+g),o=!0)})})}function Ni(e,t,i){if(e.indexOf(t)===-1)return i;let s=e[e.indexOf(t)+1];if(!s||t==="scale"&&isNaN(s))return i;if(t==="duration"||t==="delay"){let o=s.match(/([0-9]+)ms/);if(o)return o[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[s,e[e.indexOf(t)+2]].join(" "):s}var Tt=!1;function Pt(e,t=()=>{}){return(...i)=>Tt?t(...i):e(...i)}function rf(e){return(...t)=>Tt&&e(...t)}var Fa=[];function Hn(e){Fa.push(e)}function sf(e,t){Fa.forEach(i=>i(e,t)),Tt=!0,Ua(()=>{pt(t,(i,s)=>{s(i,()=>{})})}),Tt=!1}var es=!1;function of(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),Tt=!0,es=!0,Ua(()=>{af(t)}),Tt=!1,es=!1}function af(e){let t=!1;pt(e,(s,o)=>{At(s,(l,f)=>{if(t&&Jc(l))return f();t=!0,o(l,f)})})}function Ua(e){let t=Wt;Vo((i,s)=>{let o=t(i);return li(o),()=>{}}),e(),Vo(t)}function Ba(e,t,i,s=[]){switch(e._x_bindings||(e._x_bindings=ai({})),e._x_bindings[t]=i,t=s.includes("camel")?gf(t):t,t){case"value":lf(e,i);break;case"style":cf(e,i);break;case"class":uf(e,i);break;case"selected":case"checked":ff(e,t,i);break;default:qa(e,t,i);break}}function lf(e,t){if(e.type==="radio")e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=Dn(e.value)===t:e.checked=Xo(e.value,t));else if(e.type==="checkbox")Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(i=>Xo(i,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")pf(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function uf(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=ms(e,t)}function cf(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=jn(e,t)}function ff(e,t,i){qa(e,t,i),hf(e,t,i)}function qa(e,t,i){[null,void 0,!1].includes(i)&&vf(t)?e.removeAttribute(t):(Wa(t)&&(i=t),df(e,t,i))}function df(e,t,i){e.getAttribute(t)!=i&&e.setAttribute(t,i)}function hf(e,t,i){e[t]!==i&&(e[t]=i)}function pf(e,t){let i=[].concat(t).map(s=>s+"");Array.from(e.options).forEach(s=>{s.selected=i.includes(s.value)})}function gf(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function Xo(e,t){return e==t}function Dn(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?Boolean(e):null}function Wa(e){return["disabled","checked","required","readonly","open","selected","autofocus","itemscope","multiple","novalidate","allowfullscreen","allowpaymentrequest","formnovalidate","autoplay","controls","loop","muted","playsinline","default","ismap","reversed","async","defer","nomodule"].includes(e)}function vf(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function mf(e,t,i){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:Va(e,t,i)}function yf(e,t,i,s=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let o=e._x_inlineBindings[t];return o.extract=s,ba(()=>Ut(e,o.expression))}return Va(e,t,i)}function Va(e,t,i){let s=e.getAttribute(t);return s===null?typeof i=="function"?i():i:s===""?!0:Wa(t)?!![t,"true"].includes(s):s}function Ja(e,t){var i;return function(){var s=this,o=arguments,l=function(){i=null,e.apply(s,o)};clearTimeout(i),i=setTimeout(l,t)}}function za(e,t){let i;return function(){let s=this,o=arguments;i||(e.apply(s,o),i=!0,setTimeout(()=>i=!1,t))}}function Xa({get:e,set:t},{get:i,set:s}){let o=!0,l,f,g=Wt(()=>{let v=e(),w=i();if(o)s(Hr(v)),o=!1;else{let S=JSON.stringify(v),N=JSON.stringify(w);S!==l?s(Hr(v)):S!==N&&t(Hr(w))}l=JSON.stringify(e()),f=JSON.stringify(i())});return()=>{li(g)}}function Hr(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function bf(e){(Array.isArray(e)?e:[e]).forEach(i=>i(Wi))}var Ht={},Ko=!1;function xf(e,t){if(Ko||(Ht=ai(Ht),Ko=!0),t===void 0)return Ht[e];Ht[e]=t,typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&Ht[e].init(),va(Ht[e])}function _f(){return Ht}var Ka={};function wf(e,t){let i=typeof t!="function"?()=>t:t;return e instanceof Element?Ga(e,i()):(Ka[e]=i,()=>{})}function Sf(e){return Object.entries(Ka).forEach(([t,i])=>{Object.defineProperty(e,t,{get(){return(...s)=>i(...s)}})}),e}function Ga(e,t,i){let s=[];for(;s.length;)s.pop()();let o=Object.entries(t).map(([f,g])=>({name:f,value:g})),l=wa(o);return o=o.map(f=>l.find(g=>g.name===f.name)?{name:`x-bind:${f.name}`,value:`"${f.value}"`}:f),ds(e,o,i).map(f=>{s.push(f.runCleanups),f()}),()=>{for(;s.length;)s.pop()()}}var Ya={};function Ef(e,t){Ya[e]=t}function Cf(e,t){return Object.entries(Ya).forEach(([i,s])=>{Object.defineProperty(e,i,{get(){return(...o)=>s.bind(t)(...o)},enumerable:!1})}),e}var Af={get reactive(){return ai},get release(){return li},get effect(){return Wt},get raw(){return oa},version:"3.14.1",flushAndStopDeferringMutations:Lc,dontAutoEvaluateFunctions:ba,disableEffectScheduling:Sc,startObservingMutations:ls,stopObservingMutations:pa,setReactivityEngine:Ec,onAttributeRemoved:da,onAttributesAdded:fa,closestDataStack:si,skipDuringClone:Pt,onlyDuringClone:rf,addRootSelector:Ia,addInitSelector:Na,interceptClone:Hn,addScopeToNode:Ui,deferMutations:Pc,mapAttributes:hs,evaluateLater:Se,interceptInit:zc,setEvaluator:Nc,mergeProxies:Bi,extractProp:yf,findClosest:qi,onElRemoved:ss,closestRoot:Mn,destroyTree:ja,interceptor:ma,transition:Zr,setStyles:jn,mutateDom:ve,directive:pe,entangle:Xa,throttle:za,debounce:Ja,evaluate:Ut,initTree:pt,nextTick:vs,prefixed:ui,prefix:$c,plugin:bf,magic:Ve,store:xf,start:Vc,clone:of,cloneNode:sf,bound:mf,$data:ga,watch:aa,walk:At,data:Ef,bind:wf},Wi=Af;function Qa(e,t){let i=Object.create(null),s=e.split(",");for(let o=0;o<s.length;o++)i[s[o]]=!0;return t?o=>!!i[o.toLowerCase()]:o=>!!i[o]}var Tf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Rd=Qa(Tf+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected"),kf=Object.freeze({}),Od=Object.freeze([]),Pf=Object.prototype.hasOwnProperty,$n=(e,t)=>Pf.call(e,t),Bt=Array.isArray,$i=e=>Za(e)==="[object Map]",Lf=e=>typeof e=="string",ys=e=>typeof e=="symbol",Fn=e=>e!==null&&typeof e=="object",Df=Object.prototype.toString,Za=e=>Df.call(e),el=e=>Za(e).slice(8,-1),bs=e=>Lf(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Un=e=>{let t=Object.create(null);return i=>t[i]||(t[i]=e(i))},Rf=/-(\w)/g,Id=Un(e=>e.replace(Rf,(t,i)=>i?i.toUpperCase():"")),Of=/\B([A-Z])/g,Nd=Un(e=>e.replace(Of,"-$1").toLowerCase()),tl=Un(e=>e.charAt(0).toUpperCase()+e.slice(1)),Md=Un(e=>e?`on${tl(e)}`:""),il=(e,t)=>e!==t&&(e===e||t===t),ts=new WeakMap,Mi=[],at,qt=Symbol("iterate"),is=Symbol("Map key iterate");function If(e){return e&&e._isEffect===!0}function Nf(e,t=kf){If(e)&&(e=e.raw);let i=Hf(e,t);return t.lazy||i(),i}function Mf(e){e.active&&(nl(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var jf=0;function Hf(e,t){let i=function(){if(!i.active)return e();if(!Mi.includes(i)){nl(i);try{return Ff(),Mi.push(i),at=i,e()}finally{Mi.pop(),rl(),at=Mi[Mi.length-1]}}};return i.id=jf++,i.allowRecurse=!!t.allowRecurse,i._isEffect=!0,i.active=!0,i.raw=e,i.deps=[],i.options=t,i}function nl(e){let{deps:t}=e;if(t.length){for(let i=0;i<t.length;i++)t[i].delete(e);t.length=0}}var oi=!0,xs=[];function $f(){xs.push(oi),oi=!1}function Ff(){xs.push(oi),oi=!0}function rl(){let e=xs.pop();oi=e===void 0?!0:e}function We(e,t,i){if(!oi||at===void 0)return;let s=ts.get(e);s||ts.set(e,s=new Map);let o=s.get(i);o||s.set(i,o=new Set),o.has(at)||(o.add(at),at.deps.push(o),at.options.onTrack&&at.options.onTrack({effect:at,target:e,type:t,key:i}))}function kt(e,t,i,s,o,l){let f=ts.get(e);if(!f)return;let g=new Set,v=S=>{S&&S.forEach(N=>{(N!==at||N.allowRecurse)&&g.add(N)})};if(t==="clear")f.forEach(v);else if(i==="length"&&Bt(e))f.forEach((S,N)=>{(N==="length"||N>=s)&&v(S)});else switch(i!==void 0&&v(f.get(i)),t){case"add":Bt(e)?bs(i)&&v(f.get("length")):(v(f.get(qt)),$i(e)&&v(f.get(is)));break;case"delete":Bt(e)||(v(f.get(qt)),$i(e)&&v(f.get(is)));break;case"set":$i(e)&&v(f.get(qt));break}let w=S=>{S.options.onTrigger&&S.options.onTrigger({effect:S,target:e,key:i,type:t,newValue:s,oldValue:o,oldTarget:l}),S.options.scheduler?S.options.scheduler(S):S()};g.forEach(w)}var Uf=Qa("__proto__,__v_isRef,__isVue"),sl=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(ys)),Bf=ol(),qf=ol(!0),Go=Wf();function Wf(){let e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...i){let s=de(this);for(let l=0,f=this.length;l<f;l++)We(s,"get",l+"");let o=s[t](...i);return o===-1||o===!1?s[t](...i.map(de)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...i){$f();let s=de(this)[t].apply(this,i);return rl(),s}}),e}function ol(e=!1,t=!1){return function(s,o,l){if(o==="__v_isReactive")return!e;if(o==="__v_isReadonly")return e;if(o==="__v_raw"&&l===(e?t?od:cl:t?sd:ul).get(s))return s;let f=Bt(s);if(!e&&f&&$n(Go,o))return Reflect.get(Go,o,l);let g=Reflect.get(s,o,l);return(ys(o)?sl.has(o):Uf(o))||(e||We(s,"get",o),t)?g:ns(g)?!f||!bs(o)?g.value:g:Fn(g)?e?fl(g):Es(g):g}}var Vf=Jf();function Jf(e=!1){return function(i,s,o,l){let f=i[s];if(!e&&(o=de(o),f=de(f),!Bt(i)&&ns(f)&&!ns(o)))return f.value=o,!0;let g=Bt(i)&&bs(s)?Number(s)<i.length:$n(i,s),v=Reflect.set(i,s,o,l);return i===de(l)&&(g?il(o,f)&&kt(i,"set",s,o,f):kt(i,"add",s,o)),v}}function zf(e,t){let i=$n(e,t),s=e[t],o=Reflect.deleteProperty(e,t);return o&&i&&kt(e,"delete",t,void 0,s),o}function Xf(e,t){let i=Reflect.has(e,t);return(!ys(t)||!sl.has(t))&&We(e,"has",t),i}function Kf(e){return We(e,"iterate",Bt(e)?"length":qt),Reflect.ownKeys(e)}var Gf={get:Bf,set:Vf,deleteProperty:zf,has:Xf,ownKeys:Kf},Yf={get:qf,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},_s=e=>Fn(e)?Es(e):e,ws=e=>Fn(e)?fl(e):e,Ss=e=>e,Bn=e=>Reflect.getPrototypeOf(e);function Cn(e,t,i=!1,s=!1){e=e.__v_raw;let o=de(e),l=de(t);t!==l&&!i&&We(o,"get",t),!i&&We(o,"get",l);let{has:f}=Bn(o),g=s?Ss:i?ws:_s;if(f.call(o,t))return g(e.get(t));if(f.call(o,l))return g(e.get(l));e!==o&&e.get(t)}function An(e,t=!1){let i=this.__v_raw,s=de(i),o=de(e);return e!==o&&!t&&We(s,"has",e),!t&&We(s,"has",o),e===o?i.has(e):i.has(e)||i.has(o)}function Tn(e,t=!1){return e=e.__v_raw,!t&&We(de(e),"iterate",qt),Reflect.get(e,"size",e)}function Yo(e){e=de(e);let t=de(this);return Bn(t).has.call(t,e)||(t.add(e),kt(t,"add",e,e)),this}function Qo(e,t){t=de(t);let i=de(this),{has:s,get:o}=Bn(i),l=s.call(i,e);l?ll(i,s,e):(e=de(e),l=s.call(i,e));let f=o.call(i,e);return i.set(e,t),l?il(t,f)&&kt(i,"set",e,t,f):kt(i,"add",e,t),this}function Zo(e){let t=de(this),{has:i,get:s}=Bn(t),o=i.call(t,e);o?ll(t,i,e):(e=de(e),o=i.call(t,e));let l=s?s.call(t,e):void 0,f=t.delete(e);return o&&kt(t,"delete",e,void 0,l),f}function ea(){let e=de(this),t=e.size!==0,i=$i(e)?new Map(e):new Set(e),s=e.clear();return t&&kt(e,"clear",void 0,void 0,i),s}function kn(e,t){return function(s,o){let l=this,f=l.__v_raw,g=de(f),v=t?Ss:e?ws:_s;return!e&&We(g,"iterate",qt),f.forEach((w,S)=>s.call(o,v(w),v(S),l))}}function Pn(e,t,i){return function(...s){let o=this.__v_raw,l=de(o),f=$i(l),g=e==="entries"||e===Symbol.iterator&&f,v=e==="keys"&&f,w=o[e](...s),S=i?Ss:t?ws:_s;return!t&&We(l,"iterate",v?is:qt),{next(){let{value:N,done:R}=w.next();return R?{value:N,done:R}:{value:g?[S(N[0]),S(N[1])]:S(N),done:R}},[Symbol.iterator](){return this}}}}function Ct(e){return function(...t){{let i=t[0]?`on key "${t[0]}" `:"";console.warn(`${tl(e)} operation ${i}failed: target is readonly.`,de(this))}return e==="delete"?!1:this}}function Qf(){let e={get(l){return Cn(this,l)},get size(){return Tn(this)},has:An,add:Yo,set:Qo,delete:Zo,clear:ea,forEach:kn(!1,!1)},t={get(l){return Cn(this,l,!1,!0)},get size(){return Tn(this)},has:An,add:Yo,set:Qo,delete:Zo,clear:ea,forEach:kn(!1,!0)},i={get(l){return Cn(this,l,!0)},get size(){return Tn(this,!0)},has(l){return An.call(this,l,!0)},add:Ct("add"),set:Ct("set"),delete:Ct("delete"),clear:Ct("clear"),forEach:kn(!0,!1)},s={get(l){return Cn(this,l,!0,!0)},get size(){return Tn(this,!0)},has(l){return An.call(this,l,!0)},add:Ct("add"),set:Ct("set"),delete:Ct("delete"),clear:Ct("clear"),forEach:kn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(l=>{e[l]=Pn(l,!1,!1),i[l]=Pn(l,!0,!1),t[l]=Pn(l,!1,!0),s[l]=Pn(l,!0,!0)}),[e,i,t,s]}var[Zf,ed,td,id]=Qf();function al(e,t){let i=t?e?id:td:e?ed:Zf;return(s,o,l)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get($n(i,o)&&o in s?i:s,o,l)}var nd={get:al(!1,!1)},rd={get:al(!0,!1)};function ll(e,t,i){let s=de(i);if(s!==i&&t.call(e,s)){let o=el(e);console.warn(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var ul=new WeakMap,sd=new WeakMap,cl=new WeakMap,od=new WeakMap;function ad(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ld(e){return e.__v_skip||!Object.isExtensible(e)?0:ad(el(e))}function Es(e){return e&&e.__v_isReadonly?e:dl(e,!1,Gf,nd,ul)}function fl(e){return dl(e,!0,Yf,rd,cl)}function dl(e,t,i,s,o){if(!Fn(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;let l=o.get(e);if(l)return l;let f=ld(e);if(f===0)return e;let g=new Proxy(e,f===2?s:i);return o.set(e,g),g}function de(e){return e&&de(e.__v_raw)||e}function ns(e){return Boolean(e&&e.__v_isRef===!0)}Ve("nextTick",()=>vs);Ve("dispatch",e=>Hi.bind(Hi,e));Ve("watch",(e,{evaluateLater:t,cleanup:i})=>(s,o)=>{let l=t(s),g=aa(()=>{let v;return l(w=>v=w),v},o);i(g)});Ve("store",_f);Ve("data",e=>ga(e));Ve("root",e=>Mn(e));Ve("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=Bi(ud(e))),e._x_refs_proxy));function ud(e){let t=[];return qi(e,i=>{i._x_refs&&t.push(i._x_refs)}),t}var $r={};function hl(e){return $r[e]||($r[e]=0),++$r[e]}function cd(e,t){return qi(e,i=>{if(i._x_ids&&i._x_ids[t])return!0})}function fd(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=hl(t))}Ve("id",(e,{cleanup:t})=>(i,s=null)=>{let o=`${i}${s?`-${s}`:""}`;return dd(e,o,t,()=>{let l=cd(e,i),f=l?l._x_ids[i]:hl(i);return s?`${i}-${f}-${s}`:`${i}-${f}`})});Hn((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function dd(e,t,i,s){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let o=s();return e._x_id[t]=o,i(()=>{delete e._x_id[t]}),o}Ve("el",e=>e);pl("Focus","focus","focus");pl("Persist","persist","persist");function pl(e,t,i){Ve(t,s=>He(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,s))}pe("modelable",(e,{expression:t},{effect:i,evaluateLater:s,cleanup:o})=>{let l=s(t),f=()=>{let S;return l(N=>S=N),S},g=s(`${t} = __placeholder`),v=S=>g(()=>{},{scope:{__placeholder:S}}),w=f();v(w),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let S=e._x_model.get,N=e._x_model.set,R=Xa({get(){return S()},set(P){N(P)}},{get(){return f()},set(P){v(P)}});o(R)})});pe("teleport",(e,{modifiers:t,expression:i},{cleanup:s})=>{e.tagName.toLowerCase()!=="template"&&He("x-teleport can only be used on a <template> tag",e);let o=ta(i),l=e.content.cloneNode(!0).firstElementChild;e._x_teleport=l,l._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),l.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(g=>{l.addEventListener(g,v=>{v.stopPropagation(),e.dispatchEvent(new v.constructor(v.type,v))})}),Ui(l,{},e);let f=(g,v,w)=>{w.includes("prepend")?v.parentNode.insertBefore(g,v):w.includes("append")?v.parentNode.insertBefore(g,v.nextSibling):v.appendChild(g)};ve(()=>{f(l,o,t),Pt(()=>{pt(l),l._x_ignore=!0})()}),e._x_teleportPutBack=()=>{let g=ta(i);ve(()=>{f(e._x_teleport,g,t)})},s(()=>l.remove())});var hd=document.createElement("div");function ta(e){let t=Pt(()=>document.querySelector(e),()=>hd)();return t||He(`Cannot find x-teleport element for selector: "${e}"`),t}var gl=()=>{};gl.inline=(e,{modifiers:t},{cleanup:i})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,i(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};pe("ignore",gl);pe("effect",Pt((e,{expression:t},{effect:i})=>{i(Se(e,t))}));function rs(e,t,i,s){let o=e,l=v=>s(v),f={},g=(v,w)=>S=>w(v,S);if(i.includes("dot")&&(t=pd(t)),i.includes("camel")&&(t=gd(t)),i.includes("passive")&&(f.passive=!0),i.includes("capture")&&(f.capture=!0),i.includes("window")&&(o=window),i.includes("document")&&(o=document),i.includes("debounce")){let v=i[i.indexOf("debounce")+1]||"invalid-wait",w=Nn(v.split("ms")[0])?Number(v.split("ms")[0]):250;l=Ja(l,w)}if(i.includes("throttle")){let v=i[i.indexOf("throttle")+1]||"invalid-wait",w=Nn(v.split("ms")[0])?Number(v.split("ms")[0]):250;l=za(l,w)}return i.includes("prevent")&&(l=g(l,(v,w)=>{w.preventDefault(),v(w)})),i.includes("stop")&&(l=g(l,(v,w)=>{w.stopPropagation(),v(w)})),i.includes("once")&&(l=g(l,(v,w)=>{v(w),o.removeEventListener(t,l,f)})),(i.includes("away")||i.includes("outside"))&&(o=document,l=g(l,(v,w)=>{e.contains(w.target)||w.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&v(w))})),i.includes("self")&&(l=g(l,(v,w)=>{w.target===e&&v(w)})),(md(t)||vl(t))&&(l=g(l,(v,w)=>{yd(w,i)||v(w)})),o.addEventListener(t,l,f),()=>{o.removeEventListener(t,l,f)}}function pd(e){return e.replace(/-/g,".")}function gd(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function Nn(e){return!Array.isArray(e)&&!isNaN(e)}function vd(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function md(e){return["keydown","keyup"].includes(e)}function vl(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function yd(e,t){let i=t.filter(l=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(l));if(i.includes("debounce")){let l=i.indexOf("debounce");i.splice(l,Nn((i[l+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.includes("throttle")){let l=i.indexOf("throttle");i.splice(l,Nn((i[l+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.length===0||i.length===1&&ia(e.key).includes(i[0]))return!1;let o=["ctrl","shift","alt","meta","cmd","super"].filter(l=>i.includes(l));return i=i.filter(l=>!o.includes(l)),!(o.length>0&&o.filter(f=>((f==="cmd"||f==="super")&&(f="meta"),e[`${f}Key`])).length===o.length&&(vl(e.type)||ia(e.key).includes(i[0])))}function ia(e){if(!e)return[];e=vd(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(i=>{if(t[i]===e)return i}).filter(i=>i)}pe("model",(e,{modifiers:t,expression:i},{effect:s,cleanup:o})=>{let l=e;t.includes("parent")&&(l=e.parentNode);let f=Se(l,i),g;typeof i=="string"?g=Se(l,`${i} = __placeholder`):typeof i=="function"&&typeof i()=="string"?g=Se(l,`${i()} = __placeholder`):g=()=>{};let v=()=>{let R;return f(P=>R=P),na(R)?R.get():R},w=R=>{let P;f(j=>P=j),na(P)?P.set(R):g(()=>{},{scope:{__placeholder:R}})};typeof i=="string"&&e.type==="radio"&&ve(()=>{e.hasAttribute("name")||e.setAttribute("name",i)});var S=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let N=Tt?()=>{}:rs(e,S,t,R=>{w(Fr(e,t,R,v()))});if(t.includes("fill")&&([void 0,null,""].includes(v())||e.type==="checkbox"&&Array.isArray(v())||e.tagName.toLowerCase()==="select"&&e.multiple)&&w(Fr(e,t,{target:e},v())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=N,o(()=>e._x_removeModelListeners.default()),e.form){let R=rs(e.form,"reset",[],P=>{vs(()=>e._x_model&&e._x_model.set(Fr(e,t,{target:e},v())))});o(()=>R())}e._x_model={get(){return v()},set(R){w(R)}},e._x_forceModelUpdate=R=>{R===void 0&&typeof i=="string"&&i.match(/\./)&&(R=""),window.fromModel=!0,ve(()=>Ba(e,"value",R)),delete window.fromModel},s(()=>{let R=v();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(R)})});function Fr(e,t,i,s){return ve(()=>{if(i instanceof CustomEvent&&i.detail!==void 0)return i.detail!==null&&i.detail!==void 0?i.detail:i.target.value;if(e.type==="checkbox")if(Array.isArray(s)){let o=null;return t.includes("number")?o=Ur(i.target.value):t.includes("boolean")?o=Dn(i.target.value):o=i.target.value,i.target.checked?s.includes(o)?s:s.concat([o]):s.filter(l=>!bd(l,o))}else return i.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(i.target.selectedOptions).map(o=>{let l=o.value||o.text;return Ur(l)}):t.includes("boolean")?Array.from(i.target.selectedOptions).map(o=>{let l=o.value||o.text;return Dn(l)}):Array.from(i.target.selectedOptions).map(o=>o.value||o.text);{let o;return e.type==="radio"?i.target.checked?o=i.target.value:o=s:o=i.target.value,t.includes("number")?Ur(o):t.includes("boolean")?Dn(o):t.includes("trim")?o.trim():o}}})}function Ur(e){let t=e?parseFloat(e):null;return xd(t)?t:e}function bd(e,t){return e==t}function xd(e){return!Array.isArray(e)&&!isNaN(e)}function na(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}pe("cloak",e=>queueMicrotask(()=>ve(()=>e.removeAttribute(ui("cloak")))));Na(()=>`[${ui("init")}]`);pe("init",Pt((e,{expression:t},{evaluate:i})=>typeof t=="string"?!!t.trim()&&i(t,{},!1):i(t,{},!1)));pe("text",(e,{expression:t},{effect:i,evaluateLater:s})=>{let o=s(t);i(()=>{o(l=>{ve(()=>{e.textContent=l})})})});pe("html",(e,{expression:t},{effect:i,evaluateLater:s})=>{let o=s(t);i(()=>{o(l=>{ve(()=>{e.innerHTML=l,e._x_ignoreSelf=!0,pt(e),delete e._x_ignoreSelf})})})});hs(Ca(":",Aa(ui("bind:"))));var ml=(e,{value:t,modifiers:i,expression:s,original:o},{effect:l,cleanup:f})=>{if(!t){let v={};Sf(v),Se(e,s)(S=>{Ga(e,S,o)},{scope:v});return}if(t==="key")return _d(e,s);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let g=Se(e,s);l(()=>g(v=>{v===void 0&&typeof s=="string"&&s.match(/\./)&&(v=""),ve(()=>Ba(e,t,v,i))})),f(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};ml.inline=(e,{value:t,modifiers:i,expression:s})=>{!t||(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:s,extract:!1})};pe("bind",ml);function _d(e,t){e._x_keyExpression=t}Ia(()=>`[${ui("data")}]`);pe("data",(e,{expression:t},{cleanup:i})=>{if(wd(e))return;t=t===""?"{}":t;let s={};zr(s,e);let o={};Cf(o,s);let l=Ut(e,t,{scope:o});(l===void 0||l===!0)&&(l={}),zr(l,e);let f=ai(l);va(f);let g=Ui(e,f);f.init&&Ut(e,f.init),i(()=>{f.destroy&&Ut(e,f.destroy),g()})});Hn((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function wd(e){return Tt?es?!0:e.hasAttribute("data-has-alpine-state"):!1}pe("show",(e,{modifiers:t,expression:i},{effect:s})=>{let o=Se(e,i);e._x_doHide||(e._x_doHide=()=>{ve(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{ve(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let l=()=>{e._x_doHide(),e._x_isShown=!1},f=()=>{e._x_doShow(),e._x_isShown=!0},g=()=>setTimeout(f),v=Qr(N=>N?f():l(),N=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,N,f,l):N?g():l()}),w,S=!0;s(()=>o(N=>{!S&&N===w||(t.includes("immediate")&&(N?g():l()),v(N),w=N,S=!1)}))});pe("for",(e,{expression:t},{effect:i,cleanup:s})=>{let o=Ed(t),l=Se(e,o.items),f=Se(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},i(()=>Sd(e,o,l,f)),s(()=>{Object.values(e._x_lookup).forEach(g=>g.remove()),delete e._x_prevKeys,delete e._x_lookup})});function Sd(e,t,i,s){let o=f=>typeof f=="object"&&!Array.isArray(f),l=e;i(f=>{Cd(f)&&f>=0&&(f=Array.from(Array(f).keys(),I=>I+1)),f===void 0&&(f=[]);let g=e._x_lookup,v=e._x_prevKeys,w=[],S=[];if(o(f))f=Object.entries(f).map(([I,k])=>{let U=ra(t,k,I,f);s(G=>{S.includes(G)&&He("Duplicate key on x-for",e),S.push(G)},{scope:dt({index:I},U)}),w.push(U)});else for(let I=0;I<f.length;I++){let k=ra(t,f[I],I,f);s(U=>{S.includes(U)&&He("Duplicate key on x-for",e),S.push(U)},{scope:dt({index:I},k)}),w.push(k)}let N=[],R=[],P=[],j=[];for(let I=0;I<v.length;I++){let k=v[I];S.indexOf(k)===-1&&P.push(k)}v=v.filter(I=>!P.includes(I));let z="template";for(let I=0;I<S.length;I++){let k=S[I],U=v.indexOf(k);if(U===-1)v.splice(I,0,k),N.push([z,I]);else if(U!==I){let G=v.splice(I,1)[0],oe=v.splice(U-1,1)[0];v.splice(I,0,oe),v.splice(U,0,G),R.push([G,oe])}else j.push(k);z=k}for(let I=0;I<P.length;I++){let k=P[I];g[k]._x_effects&&g[k]._x_effects.forEach(sa),g[k].remove(),g[k]=null,delete g[k]}for(let I=0;I<R.length;I++){let[k,U]=R[I],G=g[k],oe=g[U],Le=document.createElement("div");ve(()=>{oe||He('x-for ":key" is undefined or invalid',l,U,g),oe.after(Le),G.after(oe),oe._x_currentIfEl&&oe.after(oe._x_currentIfEl),Le.before(G),G._x_currentIfEl&&G.after(G._x_currentIfEl),Le.remove()}),oe._x_refreshXForScope(w[S.indexOf(U)])}for(let I=0;I<N.length;I++){let[k,U]=N[I],G=k==="template"?l:g[k];G._x_currentIfEl&&(G=G._x_currentIfEl);let oe=w[U],Le=S[U],c=document.importNode(l.content,!0).firstElementChild,Je=ai(oe);Ui(c,Je,l),c._x_refreshXForScope=ae=>{Object.entries(ae).forEach(([gt,ci])=>{Je[gt]=ci})},ve(()=>{G.after(c),Pt(()=>pt(c))()}),typeof Le=="object"&&He("x-for key cannot be an object, it must be a string or an integer",l),g[Le]=c}for(let I=0;I<j.length;I++)g[j[I]]._x_refreshXForScope(w[S.indexOf(j[I])]);l._x_prevKeys=S})}function Ed(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,i=/^\s*\(|\)\s*$/g,s=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,o=e.match(s);if(!o)return;let l={};l.items=o[2].trim();let f=o[1].replace(i,"").trim(),g=f.match(t);return g?(l.item=f.replace(t,"").trim(),l.index=g[1].trim(),g[2]&&(l.collection=g[2].trim())):l.item=f,l}function ra(e,t,i,s){let o={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(f=>f.trim()).forEach((f,g)=>{o[f]=t[g]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(f=>f.trim()).forEach(f=>{o[f]=t[f]}):o[e.item]=t,e.index&&(o[e.index]=i),e.collection&&(o[e.collection]=s),o}function Cd(e){return!Array.isArray(e)&&!isNaN(e)}function yl(){}yl.inline=(e,{expression:t},{cleanup:i})=>{let s=Mn(e);s._x_refs||(s._x_refs={}),s._x_refs[t]=e,i(()=>delete s._x_refs[t])};pe("ref",yl);pe("if",(e,{expression:t},{effect:i,cleanup:s})=>{e.tagName.toLowerCase()!=="template"&&He("x-if can only be used on a <template> tag",e);let o=Se(e,t),l=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let g=e.content.cloneNode(!0).firstElementChild;return Ui(g,{},e),ve(()=>{e.after(g),Pt(()=>pt(g))()}),e._x_currentIfEl=g,e._x_undoIf=()=>{At(g,v=>{v._x_effects&&v._x_effects.forEach(sa)}),g.remove(),delete e._x_currentIfEl},g},f=()=>{!e._x_undoIf||(e._x_undoIf(),delete e._x_undoIf)};i(()=>o(g=>{g?l():f()})),s(()=>e._x_undoIf&&e._x_undoIf())});pe("id",(e,{expression:t},{evaluate:i})=>{i(t).forEach(o=>fd(e,o))});Hn((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});hs(Ca("@",Aa(ui("on:"))));pe("on",Pt((e,{value:t,modifiers:i,expression:s},{cleanup:o})=>{let l=s?Se(e,s):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let f=rs(e,t,i,g=>{l(()=>{},{scope:{$event:g},params:[g]})});o(()=>f())}));qn("Collapse","collapse","collapse");qn("Intersect","intersect","intersect");qn("Focus","trap","focus");qn("Mask","mask","mask");function qn(e,t,i){pe(t,s=>He(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,s))}Wi.setEvaluator(_a);Wi.setReactivityEngine({reactive:Es,effect:Nf,release:Mf,raw:de});var Ad=Wi,Cs=Ad;window.Alpine=Cs;Cs.start();var Td=document.querySelector("meta[name='csrf-token']").getAttribute("content"),bl=new Uo("/live",po,{hooks:Wo,dom:{onBeforeElUpdated(e,t){e._x_dataStack&&window.Alpine.clone(e,t)}},params:{_csrf_token:Td},timeout:6e4});window.addEventListener("phx:toggle-dropdown",e=>{if(e.target.classList.contains("dropdown-toggle")){let t=document.querySelectorAll(".dropdown-menu-open");for(let i=0;i<t.length;i++)t[i].classList.add("transform","opacity-0","scale-95","hidden"),t[i].classList.remove("transform","opacity-100","scale-100","dropdown-menu-open");e.target.classList.remove("dropdown-toggle")}else e.target.classList.contains("scale-95")?(e.target.classList.remove("transform","opacity-0","scale-95","hidden"),e.target.classList.add("transform","opacity-100","scale-100","dropdown-menu-open"),e.target.previousElementSibling.querySelector("button").classList.add("dropdown-toggle")):e.target.classList.contains("dropdown-menu-open")&&(e.target.classList.add("transform","opacity-0","scale-95","hidden"),e.target.classList.remove("transform","opacity-100","scale-100","dropdown-menu-open"),e.target.previousElementSibling.querySelector("button").classList.remove("dropdown-toggle"))});window.addEventListener("phx:toggle-menu-state",e=>{e.target.classList.contains("bg-indigo-700")?(e.target.classList.remove("bg-indigo-700","text-white"),e.target.classList.add("text-white","hover:bg-indigo-500","hover:bg-opacity-75")):(e.target.classList.add("bg-indigo-700","text-white"),e.target.classList.remove("text-white","hover:bg-indigo-500","hover:bg-opacity-75"))});window.addEventListener("phx:table-dropdown",e=>{if(e.target.classList.contains("dropdown-toggle")){let t=document.querySelectorAll(".dropdown-menu-open");for(let i=0;i<t.length;i++)t[i].classList.add("hidden"),t[i].classList.remove("dropdown-menu-open");e.target.classList.remove("dropdown-toggle")}else e.target.classList.contains("hidden")?(e.target.classList.remove("hidden"),e.target.classList.add("dropdown-menu-open"),e.target.previousElementSibling.classList.add("dropdown-toggle")):e.target.classList.contains("dropdown-menu-open")&&(e.target.classList.add("hidden"),e.target.classList.remove("dropdown-menu-open"),e.target.previousElementSibling.classList.remove("dropdown-toggle"))});Wn.default.config({barColors:{0:"#29d"},shadowColor:"rgba(0, 0, 0, .3)"});window.addEventListener("phx:page-loading-start",e=>Wn.default.show());window.addEventListener("phx:page-loading-stop",e=>Wn.default.hide());bl.connect();window.liveSocket=bl;})();
/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */
/**
 * @license MIT
 * topbar 1.0.0, 2021-01-06
 * https://buunguyen.github.io/topbar
 * Copyright (c) 2021 Buu Nguyen
 */
