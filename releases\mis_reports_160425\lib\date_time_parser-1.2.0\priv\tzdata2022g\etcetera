# tzdb data for ships at sea and other miscellany

# This file is in the public domain, so clarified as of
# 2009-05-17 by <PERSON>.

# These entries are for uses not otherwise covered by the tz database.
# Their main practical use is for platforms like Android that lack
# support for POSIX-style TZ strings.  On such platforms these entries
# can be useful if the timezone database is wrong or if a ship or
# aircraft at sea is not in a timezone.

# Starting with POSIX 1003.1-2001, the entries below are all
# unnecessary as settings for the TZ environment variable.  E.g.,
# instead of TZ='Etc/GMT+4' one can use the POSIX setting TZ='<-04>+4'.
#
# Do not use a POSIX TZ setting like TZ='GMT+4', which is four hours
# behind GMT but uses the completely misleading abbreviation "GMT".

# The following zone is used by tzcode functions like gmtime,
# which load the "UTC" file to handle seconds properly.
Zone	Etc/UTC		0	-	UTC

# Functions like gmtime load the "GMT" file to handle leap seconds properly.
# Vanguard section, which works with most .zi parsers.
#Zone	GMT		0	-	GMT
# Rearguard section, for TZUpdater 2.3.2 and earlier.
Zone	Etc/GMT		0	-	GMT

# The following link uses older naming conventions,
# but it belongs here, not in the file 'backward',
# as it is needed for tzcode releases through 2022a,
# where functions like gmtime load "GMT" instead of the "Etc/UTC".
# We want this to work even on installations that omit 'backward'.
Link	Etc/GMT				GMT
# End of rearguard section.

# Be consistent with POSIX TZ settings in the Zone names,
# even though this is the opposite of what many people expect.
# POSIX has positive signs west of Greenwich, but many people expect
# positive signs east of Greenwich.  For example, TZ='Etc/GMT+4' uses
# the abbreviation "-04" and corresponds to 4 hours behind UT
# (i.e. west of Greenwich) even though many people would expect it to
# mean 4 hours ahead of UT (i.e. east of Greenwich).

# Earlier incarnations of this package were not POSIX-compliant,
# and had lines such as
#		Zone	GMT-12		-12	-	GMT-1200
# We did not want things to change quietly if someone accustomed to the old
# way does a
#		zic -l GMT-12
# so we moved the names into the Etc subdirectory.
# Also, the time zone abbreviations are now compatible with %z.

Zone	Etc/GMT-14	14	-	+14
Zone	Etc/GMT-13	13	-	+13
Zone	Etc/GMT-12	12	-	+12
Zone	Etc/GMT-11	11	-	+11
Zone	Etc/GMT-10	10	-	+10
Zone	Etc/GMT-9	9	-	+09
Zone	Etc/GMT-8	8	-	+08
Zone	Etc/GMT-7	7	-	+07
Zone	Etc/GMT-6	6	-	+06
Zone	Etc/GMT-5	5	-	+05
Zone	Etc/GMT-4	4	-	+04
Zone	Etc/GMT-3	3	-	+03
Zone	Etc/GMT-2	2	-	+02
Zone	Etc/GMT-1	1	-	+01
Zone	Etc/GMT+1	-1	-	-01
Zone	Etc/GMT+2	-2	-	-02
Zone	Etc/GMT+3	-3	-	-03
Zone	Etc/GMT+4	-4	-	-04
Zone	Etc/GMT+5	-5	-	-05
Zone	Etc/GMT+6	-6	-	-06
Zone	Etc/GMT+7	-7	-	-07
Zone	Etc/GMT+8	-8	-	-08
Zone	Etc/GMT+9	-9	-	-09
Zone	Etc/GMT+10	-10	-	-10
Zone	Etc/GMT+11	-11	-	-11
Zone	Etc/GMT+12	-12	-	-12
