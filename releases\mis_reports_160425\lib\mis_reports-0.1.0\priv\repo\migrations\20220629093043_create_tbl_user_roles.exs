defmodule MisReports.Repo.Migrations.CreateTblUserRoles do
  use Ecto.Migration

  def change do
    create table(:tbl_user_role) do
      add :role_desc, :string
      add :role_str, :string
      add :status, :string
      add :maker_id, references(:tbl_users, column: :id, on_delete: :nothing)
      add :checker_id, references(:tbl_users, column: :id, on_delete: :nothing)
      timestamps()
    end
  end
end
