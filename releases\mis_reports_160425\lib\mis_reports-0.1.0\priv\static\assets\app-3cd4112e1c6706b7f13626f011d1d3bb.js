(()=>{var oa=Object.create;var Ki=Object.defineProperty;var aa=Object.getOwnPropertyDescriptor;var la=Object.getOwnPropertyNames,ii=Object.getOwnPropertySymbols,ca=Object.getPrototypeOf,zi=Object.prototype.hasOwnProperty,vr=Object.prototype.propertyIsEnumerable;var mr=(e,t,i)=>t in e?Ki(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Fe=(e,t)=>{for(var i in t||(t={}))zi.call(t,i)&&mr(e,i,t[i]);if(ii)for(var i of ii(t))vr.call(t,i)&&mr(e,i,t[i]);return e};var _r=(e,t)=>{var i={};for(var n in e)zi.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(e!=null&&ii)for(var n of ii(e))t.indexOf(n)<0&&vr.call(e,n)&&(i[n]=e[n]);return i};var br=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var ua=(e,t,i,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of la(t))!zi.call(e,r)&&r!==i&&Ki(e,r,{get:()=>t[r],enumerable:!(n=aa(t,r))||n.enumerable});return e};var yr=(e,t,i)=>(i=e!=null?oa(ca(e)):{},ua(t||!e||!e.__esModule?Ki(i,"default",{value:e,enumerable:!0}):i,e));var Yr=br((Gr,wi)=>{(function(e,t){"use strict";(function(){for(var y=0,E=["ms","moz","webkit","o"],P=0;P<E.length&&!e.requestAnimationFrame;++P)e.requestAnimationFrame=e[E[P]+"RequestAnimationFrame"],e.cancelAnimationFrame=e[E[P]+"CancelAnimationFrame"]||e[E[P]+"CancelRequestAnimationFrame"];e.requestAnimationFrame||(e.requestAnimationFrame=function(D,T){var b=new Date().getTime(),O=Math.max(0,16-(b-y)),N=e.setTimeout(function(){D(b+O)},O);return y=b+O,N}),e.cancelAnimationFrame||(e.cancelAnimationFrame=function(D){clearTimeout(D)})})();var i,n,r,o,l,d=function(y,E,P){y.addEventListener?y.addEventListener(E,P,!1):y.attachEvent?y.attachEvent("on"+E,P):y["on"+E]=P},h={autoRun:!0,barThickness:3,barColors:{0:"rgba(26,  188, 156, .9)",".25":"rgba(52,  152, 219, .9)",".50":"rgba(241, 196, 15,  .9)",".75":"rgba(230, 126, 34,  .9)","1.0":"rgba(211, 84,  0,   .9)"},shadowBlur:10,shadowColor:"rgba(0,   0,   0,   .6)",className:null},m=function(){i.width=e.innerWidth,i.height=h.barThickness*5;var y=i.getContext("2d");y.shadowBlur=h.shadowBlur,y.shadowColor=h.shadowColor;var E=y.createLinearGradient(0,0,i.width,0);for(var P in h.barColors)E.addColorStop(P,h.barColors[P]);y.lineWidth=h.barThickness,y.beginPath(),y.moveTo(0,h.barThickness/2),y.lineTo(Math.ceil(o*i.width),h.barThickness/2),y.strokeStyle=E,y.stroke()},v=function(){i=t.createElement("canvas");var y=i.style;y.position="fixed",y.top=y.left=y.right=y.margin=y.padding=0,y.zIndex=100001,y.display="none",h.className&&i.classList.add(h.className),t.body.appendChild(i),d(e,"resize",m)},S={config:function(y){for(var E in y)h.hasOwnProperty(E)&&(h[E]=y[E])},show:function(){l||(l=!0,r!==null&&e.cancelAnimationFrame(r),i||v(),i.style.opacity=1,i.style.display="block",S.progress(0),h.autoRun&&function y(){n=e.requestAnimationFrame(y),S.progress("+"+.05*Math.pow(1-Math.sqrt(o),2))}())},progress:function(y){return typeof y=="undefined"||(typeof y=="string"&&(y=(y.indexOf("+")>=0||y.indexOf("-")>=0?o:0)+parseFloat(y)),o=y>1?1:y,m()),o},hide:function(){!l||(l=!1,n!=null&&(e.cancelAnimationFrame(n),n=null),function y(){if(S.progress("+.1")>=1&&(i.style.opacity-=.05,i.style.opacity<=.05)){i.style.display="none",r=null;return}r=e.requestAnimationFrame(y)}())}};typeof wi=="object"&&typeof wi.exports=="object"?wi.exports=S:typeof define=="function"&&define.amd?define(function(){return S}):this.topbar=S}).call(Gr,window,document)});var Oo=br((nr,rr)=>{(function(e,t){typeof nr=="object"&&typeof rr!="undefined"?rr.exports=t():typeof define=="function"&&define.amd?define(t):(e=typeof globalThis!="undefined"?globalThis:e||self,e.TomSelect=t())})(nr,function(){"use strict";function e(u,s){u.split(/\s+/).forEach(a=>{s(a)})}class t{constructor(){this._events=void 0,this._events={}}on(s,a){e(s,c=>{let f=this._events[c]||[];f.push(a),this._events[c]=f})}off(s,a){var c=arguments.length;if(c===0){this._events={};return}e(s,f=>{if(c===1){delete this._events[f];return}let g=this._events[f];g!==void 0&&(g.splice(g.indexOf(a),1),this._events[f]=g)})}trigger(s,...a){var c=this;e(s,f=>{let g=c._events[f];g!==void 0&&g.forEach(p=>{p.apply(c,a)})})}}function i(u){return u.plugins={},class extends u{constructor(...s){super(...s);this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(s,a){u.plugins[s]={name:s,fn:a}}initializePlugins(s){var a,c;let f=this,g=[];if(Array.isArray(s))s.forEach(p=>{typeof p=="string"?g.push(p):(f.plugins.settings[p.name]=p.options,g.push(p.name))});else if(s)for(a in s)s.hasOwnProperty(a)&&(f.plugins.settings[a]=s[a],g.push(a));for(;c=g.shift();)f.require(c)}loadPlugin(s){var a=this,c=a.plugins,f=u.plugins[s];if(!u.plugins.hasOwnProperty(s))throw new Error('Unable to find "'+s+'" plugin');c.requested[s]=!0,c.loaded[s]=f.fn.apply(a,[a.plugins.settings[s]||{}]),c.names.push(s)}require(s){var a=this,c=a.plugins;if(!a.plugins.loaded.hasOwnProperty(s)){if(c.requested[s])throw new Error('Plugin has circular dependency ("'+s+'")');a.loadPlugin(s)}return c.loaded[s]}}}let n=u=>(u=u.filter(Boolean),u.length<2?u[0]||"":h(u)==1?"["+u.join("")+"]":"(?:"+u.join("|")+")"),r=u=>{if(!l(u))return u.join("");let s="",a=0,c=()=>{a>1&&(s+="{"+a+"}")};return u.forEach((f,g)=>{if(f===u[g-1]){a++;return}c(),s+=f,a=1}),c(),s},o=u=>{let s=v(u);return n(s)},l=u=>new Set(u).size!==u.length,d=u=>(u+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),h=u=>u.reduce((s,a)=>Math.max(s,m(a)),0),m=u=>v(u).length,v=u=>Array.from(u);let S=u=>{if(u.length===1)return[[u]];let s=[],a=u.substring(1);return S(a).forEach(function(f){let g=f.slice(0);g[0]=u.charAt(0)+g[0],s.push(g),g=f.slice(0),g.unshift(u.charAt(0)),s.push(g)}),s};let y=[[0,65535]],E="[\u0300-\u036F\xB7\u02BE\u02BC]",P,D,T=3,b={},O={"/":"\u2044\u2215","0":"\u07C0",a:"\u2C65\u0250\u0251",aa:"\uA733",ae:"\xE6\u01FD\u01E3",ao:"\uA735",au:"\uA737",av:"\uA739\uA73B",ay:"\uA73D",b:"\u0180\u0253\u0183",c:"\uA73F\u0188\u023C\u2184",d:"\u0111\u0257\u0256\u1D05\u018C\uABB7\u0501\u0266",e:"\u025B\u01DD\u1D07\u0247",f:"\uA77C\u0192",g:"\u01E5\u0260\uA7A1\u1D79\uA77F\u0262",h:"\u0127\u2C68\u2C76\u0265",i:"\u0268\u0131",j:"\u0249\u0237",k:"\u0199\u2C6A\uA741\uA743\uA745\uA7A3",l:"\u0142\u019A\u026B\u2C61\uA749\uA747\uA781\u026D",m:"\u0271\u026F\u03FB",n:"\uA7A5\u019E\u0272\uA791\u1D0E\u043B\u0509",o:"\xF8\u01FF\u0254\u0275\uA74B\uA74D\u1D11",oe:"\u0153",oi:"\u01A3",oo:"\uA74F",ou:"\u0223",p:"\u01A5\u1D7D\uA751\uA753\uA755\u03C1",q:"\uA757\uA759\u024B",r:"\u024D\u027D\uA75B\uA7A7\uA783",s:"\xDF\u023F\uA7A9\uA785\u0282",t:"\u0167\u01AD\u0288\u2C66\uA787",th:"\xFE",tz:"\uA729",u:"\u0289",v:"\u028B\uA75F\u028C",vy:"\uA761",w:"\u2C73",y:"\u01B4\u024F\u1EFF",z:"\u01B6\u0225\u0240\u2C6C\uA763",hv:"\u0195"};for(let u in O){let s=O[u]||"";for(let a=0;a<s.length;a++){let c=s.substring(a,a+1);b[c]=u}}let N=new RegExp(Object.keys(b).join("|")+"|"+E,"gu"),V=u=>{P===void 0&&(P=St(u||y))},de=(u,s="NFKD")=>u.normalize(s),fe=u=>v(u).reduce((s,a)=>s+He(a),""),He=u=>(u=de(u).toLowerCase().replace(N,s=>b[s]||""),de(u,"NFC"));function*wt(u){for(let[s,a]of u)for(let c=s;c<=a;c++){let f=String.fromCharCode(c),g=fe(f);g!=f.toLowerCase()&&(g.length>T||g.length!=0&&(yield{folded:g,composed:f,code_point:c}))}}let je=u=>{let s={},a=(c,f)=>{let g=s[c]||new Set,p=new RegExp("^"+o(g)+"$","iu");f.match(p)||(g.add(d(f)),s[c]=g)};for(let c of wt(u))a(c.folded,c.folded),a(c.folded,c.composed);return s},St=u=>{let s=je(u),a={},c=[];for(let g in s){let p=s[g];p&&(a[g]=o(p)),g.length>1&&c.push(d(g))}c.sort((g,p)=>p.length-g.length);let f=n(c);return D=new RegExp("^"+f,"u"),a},re=(u,s=1)=>{let a=0;return u=u.map(c=>(P[c]&&(a+=c.length),P[c]||c)),a>=s?r(u):""},ct=(u,s=1)=>(s=Math.max(s,u.length-1),n(S(u).map(a=>re(a,s)))),xt=(u,s=!0)=>{let a=u.length>1?1:0;return n(u.map(c=>{let f=[],g=s?c.length():c.length()-1;for(let p=0;p<g;p++)f.push(ct(c.substrs[p]||"",a));return r(f)}))},At=(u,s)=>{for(let a of s){if(a.start!=u.start||a.end!=u.end||a.substrs.join("")!==u.substrs.join(""))continue;let c=u.parts,f=p=>{for(let _ of c){if(_.start===p.start&&_.substr===p.substr)return!1;if(!(p.length==1||_.length==1)&&(p.start<_.start&&p.end>_.start||_.start<p.start&&_.end>p.start))return!0}return!1};if(!(a.parts.filter(f).length>0))return!0}return!1};class ut{constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(s){s&&(this.parts.push(s),this.substrs.push(s.substr),this.start=Math.min(s.start,this.start),this.end=Math.max(s.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(s,a){let c=new ut,f=JSON.parse(JSON.stringify(this.parts)),g=f.pop();for(let A of f)c.add(A);let p=a.substr.substring(0,s-g.start),_=p.length;return c.add({start:g.start,end:g.start+_,length:_,substr:p}),c}}let Et=u=>{V(),u=fe(u);let s="",a=[new ut];for(let c=0;c<u.length;c++){let g=u.substring(c).match(D),p=u.substring(c,c+1),_=g?g[0]:null,A=[],x=new Set;for(let k of a){let C=k.last();if(!C||C.length==1||C.end<=c)if(_){let I=_.length;k.add({start:c,end:c+I,length:I,substr:_}),x.add("1")}else k.add({start:c,end:c+1,length:1,substr:p}),x.add("2");else if(_){let I=k.clone(c,C),X=_.length;I.add({start:c,end:c+X,length:X,substr:_}),A.push(I)}else x.add("3")}if(A.length>0){A=A.sort((k,C)=>k.length()-C.length());for(let k of A)At(k,a)||a.push(k);continue}if(c>0&&x.size==1&&!x.has("3")){s+=xt(a,!1);let k=new ut,C=a[0];C&&k.add(C.last()),a=[k]}}return s+=xt(a,!0),s};let M=(u,s)=>{if(!!u)return u[s]},F=(u,s)=>{if(!!u){for(var a,c=s.split(".");(a=c.shift())&&(u=u[a]););return u}},G=(u,s,a)=>{var c,f;return!u||(u=u+"",s.regex==null)||(f=u.search(s.regex),f===-1)?0:(c=s.string.length/u.length,f===0&&(c+=.5),c*a)},j=(u,s)=>{var a=u[s];if(typeof a=="function")return a;a&&!Array.isArray(a)&&(u[s]=[a])},$=(u,s)=>{if(Array.isArray(u))u.forEach(s);else for(var a in u)u.hasOwnProperty(a)&&s(u[a],a)},Ue=(u,s)=>typeof u=="number"&&typeof s=="number"?u>s?1:u<s?-1:0:(u=fe(u+"").toLowerCase(),s=fe(s+"").toLowerCase(),u>s?1:s>u?-1:0);class Ee{constructor(s,a){this.items=void 0,this.settings=void 0,this.items=s,this.settings=a||{diacritics:!0}}tokenize(s,a,c){if(!s||!s.length)return[];let f=[],g=s.split(/\s+/);var p;return c&&(p=new RegExp("^("+Object.keys(c).map(d).join("|")+"):(.*)$")),g.forEach(_=>{let A,x=null,k=null;p&&(A=_.match(p))&&(x=A[1],_=A[2]),_.length>0&&(this.settings.diacritics?k=Et(_)||null:k=d(_),k&&a&&(k="\\b"+k)),f.push({string:_,regex:k?new RegExp(k,"iu"):null,field:x})}),f}getScoreFunction(s,a){var c=this.prepareSearch(s,a);return this._getScoreFunction(c)}_getScoreFunction(s){let a=s.tokens,c=a.length;if(!c)return function(){return 0};let f=s.options.fields,g=s.weights,p=f.length,_=s.getAttrFn;if(!p)return function(){return 1};let A=function(){return p===1?function(x,k){let C=f[0].field;return G(_(k,C),x,g[C]||1)}:function(x,k){var C=0;if(x.field){let I=_(k,x.field);!x.regex&&I?C+=1/p:C+=G(I,x,1)}else $(g,(I,X)=>{C+=G(_(k,X),x,I)});return C/p}}();return c===1?function(x){return A(a[0],x)}:s.options.conjunction==="and"?function(x){var k,C=0;for(let I of a){if(k=A(I,x),k<=0)return 0;C+=k}return C/c}:function(x){var k=0;return $(a,C=>{k+=A(C,x)}),k/c}}getSortFunction(s,a){var c=this.prepareSearch(s,a);return this._getSortFunction(c)}_getSortFunction(s){var a,c=[];let f=this,g=s.options,p=!s.query&&g.sort_empty?g.sort_empty:g.sort;if(typeof p=="function")return p.bind(this);let _=function(k,C){return k==="$score"?C.score:s.getAttrFn(f.items[C.id],k)};if(p)for(let x of p)(s.query||x.field!=="$score")&&c.push(x);if(s.query){a=!0;for(let x of c)if(x.field==="$score"){a=!1;break}a&&c.unshift({field:"$score",direction:"desc"})}else c=c.filter(x=>x.field!=="$score");return c.length?function(x,k){var C,I;for(let X of c)if(I=X.field,C=(X.direction==="desc"?-1:1)*Ue(_(I,x),_(I,k)),C)return C;return 0}:null}prepareSearch(s,a){let c={};var f=Object.assign({},a);if(j(f,"sort"),j(f,"sort_empty"),f.fields){j(f,"fields");let g=[];f.fields.forEach(p=>{typeof p=="string"&&(p={field:p,weight:1}),g.push(p),c[p.field]="weight"in p?p.weight:1}),f.fields=g}return{options:f,query:s.toLowerCase().trim(),tokens:this.tokenize(s,f.respect_word_boundaries,c),total:0,items:[],weights:c,getAttrFn:f.nesting?F:M}}search(s,a){var c=this,f,g;g=this.prepareSearch(s,a),a=g.options,s=g.query;let p=a.score||c._getScoreFunction(g);s.length?$(c.items,(A,x)=>{f=p(A),(a.filter===!1||f>0)&&g.items.push({score:f,id:x})}):$(c.items,(A,x)=>{g.items.push({score:1,id:x})});let _=c._getSortFunction(g);return _&&g.items.sort(_),g.total=g.items.length,typeof a.limit=="number"&&(g.items=g.items.slice(0,a.limit)),g}}let pe=(u,s)=>{if(Array.isArray(u))u.forEach(s);else for(var a in u)u.hasOwnProperty(a)&&s(u[a],a)},ee=u=>{if(u.jquery)return u[0];if(u instanceof HTMLElement)return u;if(we(u)){var s=document.createElement("template");return s.innerHTML=u.trim(),s.content.firstChild}return document.querySelector(u)},we=u=>typeof u=="string"&&u.indexOf("<")>-1,dt=u=>u.replace(/['"\\]/g,"\\$&"),_e=(u,s)=>{var a=document.createEvent("HTMLEvents");a.initEvent(s,!0,!1),u.dispatchEvent(a)},Be=(u,s)=>{Object.assign(u.style,s)},se=(u,...s)=>{var a=sr(s);u=or(u),u.map(c=>{a.map(f=>{c.classList.add(f)})})},De=(u,...s)=>{var a=sr(s);u=or(u),u.map(c=>{a.map(f=>{c.classList.remove(f)})})},sr=u=>{var s=[];return pe(u,a=>{typeof a=="string"&&(a=a.trim().split(/[\11\12\14\15\40]/)),Array.isArray(a)&&(s=s.concat(a))}),s.filter(Boolean)},or=u=>(Array.isArray(u)||(u=[u]),u),Wt=(u,s,a)=>{if(!(a&&!a.contains(u)))for(;u&&u.matches;){if(u.matches(s))return u;u=u.parentNode}},ar=(u,s=0)=>s>0?u[u.length-1]:u[0],Lo=u=>Object.keys(u).length===0,Xt=(u,s)=>{if(!u)return-1;s=s||u.nodeName;for(var a=0;u=u.previousElementSibling;)u.matches(s)&&a++;return a},Y=(u,s)=>{pe(s,(a,c)=>{a==null?u.removeAttribute(c):u.setAttribute(c,""+a)})},Bi=(u,s)=>{u.parentNode&&u.parentNode.replaceChild(s,u)},Io=(u,s)=>{if(s===null)return;if(typeof s=="string"){if(!s.length)return;s=new RegExp(s,"i")}let a=g=>{var p=g.data.match(s);if(p&&g.data.length>0){var _=document.createElement("span");_.className="highlight";var A=g.splitText(p.index);A.splitText(p[0].length);var x=A.cloneNode(!0);return _.appendChild(x),Bi(A,_),1}return 0},c=g=>{g.nodeType===1&&g.childNodes&&!/(script|style)/i.test(g.tagName)&&(g.className!=="highlight"||g.tagName!=="SPAN")&&Array.from(g.childNodes).forEach(p=>{f(p)})},f=g=>g.nodeType===3?a(g):(c(g),0);f(u)},Po=u=>{var s=u.querySelectorAll("span.highlight");Array.prototype.forEach.call(s,function(a){var c=a.parentNode;c.replaceChild(a.firstChild,a),c.normalize()})},Ro=65,Do=13,lr=27,qi=37,Fo=38,cr=39,Mo=40,ur=8,No=46,Vi=9,Gt=(typeof navigator=="undefined"?!1:/Mac/.test(navigator.userAgent))?"metaKey":"ctrlKey";var dr={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,refreshThrottle:300,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(u){return u.length>0},render:{}};let be=u=>typeof u=="undefined"||u===null?null:Yt(u),Yt=u=>typeof u=="boolean"?u?"1":"0":u+"",Qt=u=>(u+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),$o=(u,s)=>s>0?setTimeout(u,s):(u.call(null),null),Ho=(u,s)=>{var a;return function(c,f){var g=this;a&&(g.loading=Math.max(g.loading-1,0),clearTimeout(a)),a=setTimeout(function(){a=null,g.loadedSearches[c]=!0,u.call(g,c,f)},s)}},hr=(u,s,a)=>{var c,f=u.trigger,g={};u.trigger=function(){var p=arguments[0];if(s.indexOf(p)!==-1)g[p]=arguments;else return f.apply(u,arguments)},a.apply(u,[]),u.trigger=f;for(c of s)c in g&&f.apply(u,g[c])},jo=u=>({start:u.selectionStart||0,length:(u.selectionEnd||0)-(u.selectionStart||0)}),J=(u,s=!1)=>{u&&(u.preventDefault(),s&&u.stopPropagation())},K=(u,s,a,c)=>{u.addEventListener(s,a,c)},Qe=(u,s)=>{if(!s||!s[u])return!1;var a=(s.altKey?1:0)+(s.ctrlKey?1:0)+(s.shiftKey?1:0)+(s.metaKey?1:0);return a===1},Ji=(u,s)=>{let a=u.getAttribute("id");return a||(u.setAttribute("id",s),s)},fr=u=>u.replace(/[\\"']/g,"\\$&"),Ze=(u,s)=>{s&&u.append(s)};function pr(u,s){var a=Object.assign({},dr,s),c=a.dataAttr,f=a.labelField,g=a.valueField,p=a.disabledField,_=a.optgroupField,A=a.optgroupLabelField,x=a.optgroupValueField,k=u.tagName.toLowerCase(),C=u.getAttribute("placeholder")||u.getAttribute("data-placeholder");if(!C&&!a.allowEmptyOption){let B=u.querySelector('option[value=""]');B&&(C=B.textContent)}var I={placeholder:C,options:[],optgroups:[],items:[],maxItems:null},X=()=>{var B,Q=I.options,z={},R=1;let te=0;var Ce=U=>{var q=Object.assign({},U.dataset),H=c&&q[c];return typeof H=="string"&&H.length&&(q=Object.assign(q,JSON.parse(H))),q},Zt=(U,q)=>{var H=be(U.value);if(H!=null&&!(!H&&!a.allowEmptyOption)){if(z.hasOwnProperty(H)){if(q){var oe=z[H][_];oe?Array.isArray(oe)?oe.push(q):z[H][_]=[oe,q]:z[H][_]=q}}else{var W=Ce(U);W[f]=W[f]||U.textContent,W[g]=W[g]||H,W[p]=W[p]||U.disabled,W[_]=W[_]||q,W.$option=U,W.$order=W.$order||++te,z[H]=W,Q.push(W)}U.selected&&I.items.push(H)}},ht=U=>{var q,H;H=Ce(U),H[A]=H[A]||U.getAttribute("label")||"",H[x]=H[x]||R++,H[p]=H[p]||U.disabled,H.$order=H.$order||++te,I.optgroups.push(H),q=H[x],pe(U.children,oe=>{Zt(oe,q)})};I.maxItems=u.hasAttribute("multiple")?null:1,pe(u.children,U=>{B=U.tagName.toLowerCase(),B==="optgroup"?ht(U):B==="option"&&Zt(U)})},L=()=>{let B=u.getAttribute(c);if(B)I.options=JSON.parse(B),pe(I.options,z=>{I.items.push(z[g])});else{var Q=u.value.trim()||"";if(!a.allowEmptyOption&&!Q.length)return;let z=Q.split(a.delimiter);pe(z,R=>{let te={};te[f]=R,te[g]=R,I.options.push(te)}),I.items=z}};return k==="select"?X():L(),Object.assign({},dr,I,s)}var gr=0;class ce extends i(t){constructor(s,a){super();this.control_input=void 0,this.wrapper=void 0,this.dropdown=void 0,this.control=void 0,this.dropdown_content=void 0,this.focus_node=void 0,this.order=0,this.settings=void 0,this.input=void 0,this.tabIndex=void 0,this.is_select_tag=void 0,this.rtl=void 0,this.inputId=void 0,this._destroy=void 0,this.sifter=void 0,this.isOpen=!1,this.isDisabled=!1,this.isReadOnly=!1,this.isRequired=void 0,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.currentResults=void 0,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],this.refreshTimeout=null,gr++;var c,f=ee(s);if(f.tomselect)throw new Error("Tom Select already initialized on this element");f.tomselect=this;var g=window.getComputedStyle&&window.getComputedStyle(f,null);c=g.getPropertyValue("direction");let p=pr(f,a);this.settings=p,this.input=f,this.tabIndex=f.tabIndex||0,this.is_select_tag=f.tagName.toLowerCase()==="select",this.rtl=/rtl/i.test(c),this.inputId=Ji(f,"tomselect-"+gr),this.isRequired=f.required,this.sifter=new Ee(this.options,{diacritics:p.diacritics}),p.mode=p.mode||(p.maxItems===1?"single":"multi"),typeof p.hideSelected!="boolean"&&(p.hideSelected=p.mode==="multi"),typeof p.hidePlaceholder!="boolean"&&(p.hidePlaceholder=p.mode!=="multi");var _=p.createFilter;typeof _!="function"&&(typeof _=="string"&&(_=new RegExp(_)),_ instanceof RegExp?p.createFilter=Q=>_.test(Q):p.createFilter=Q=>this.settings.duplicates||!this.options[Q]),this.initializePlugins(p.plugins),this.setupCallbacks(),this.setupTemplates();let A=ee("<div>"),x=ee("<div>"),k=this._render("dropdown"),C=ee('<div role="listbox" tabindex="-1">'),I=this.input.getAttribute("class")||"",X=p.mode;var L;if(se(A,p.wrapperClass,I,X),se(x,p.controlClass),Ze(A,x),se(k,p.dropdownClass,X),p.copyClassesToDropdown&&se(k,I),se(C,p.dropdownContentClass),Ze(k,C),ee(p.dropdownParent||A).appendChild(k),we(p.controlInput)){L=ee(p.controlInput);var B=["autocorrect","autocapitalize","autocomplete","spellcheck"];$(B,Q=>{f.getAttribute(Q)&&Y(L,{[Q]:f.getAttribute(Q)})}),L.tabIndex=-1,x.appendChild(L),this.focus_node=L}else p.controlInput?(L=ee(p.controlInput),this.focus_node=L):(L=ee("<input/>"),this.focus_node=x);this.wrapper=A,this.dropdown=k,this.dropdown_content=C,this.control=x,this.control_input=L,this.setup()}setup(){let s=this,a=s.settings,c=s.control_input,f=s.dropdown,g=s.dropdown_content,p=s.wrapper,_=s.control,A=s.input,x=s.focus_node,k={passive:!0},C=s.inputId+"-ts-dropdown";Y(g,{id:C}),Y(x,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":C});let I=Ji(x,s.inputId+"-ts-control"),X="label[for='"+dt(s.inputId)+"']",L=document.querySelector(X),B=s.focus.bind(s);if(L){K(L,"click",B),Y(L,{for:I});let R=Ji(L,s.inputId+"-ts-label");Y(x,{"aria-labelledby":R}),Y(g,{"aria-labelledby":R})}if(p.style.width=A.style.width,s.plugins.names.length){let R="plugin-"+s.plugins.names.join(" plugin-");se([p,f],R)}(a.maxItems===null||a.maxItems>1)&&s.is_select_tag&&Y(A,{multiple:"multiple"}),a.placeholder&&Y(c,{placeholder:a.placeholder}),!a.splitOn&&a.delimiter&&(a.splitOn=new RegExp("\\s*"+d(a.delimiter)+"+\\s*")),a.load&&a.loadThrottle&&(a.load=Ho(a.load,a.loadThrottle)),K(f,"mousemove",()=>{s.ignoreHover=!1}),K(f,"mouseenter",R=>{var te=Wt(R.target,"[data-selectable]",f);te&&s.onOptionHover(R,te)},{capture:!0}),K(f,"click",R=>{let te=Wt(R.target,"[data-selectable]");te&&(s.onOptionSelect(R,te),J(R,!0))}),K(_,"click",R=>{var te=Wt(R.target,"[data-ts-item]",_);if(te&&s.onItemSelect(R,te)){J(R,!0);return}c.value==""&&(s.onClick(),J(R,!0))}),K(x,"keydown",R=>s.onKeyDown(R)),K(c,"keypress",R=>s.onKeyPress(R)),K(c,"input",R=>s.onInput(R)),K(x,"blur",R=>s.onBlur(R)),K(x,"focus",R=>s.onFocus(R)),K(c,"paste",R=>s.onPaste(R));let Q=R=>{let te=R.composedPath()[0];if(!p.contains(te)&&!f.contains(te)){s.isFocused&&s.blur(),s.inputState();return}te==c&&s.isOpen?R.stopPropagation():J(R,!0)},z=()=>{s.isOpen&&s.positionDropdown()};K(document,"mousedown",Q),K(window,"scroll",z,k),K(window,"resize",z,k),this._destroy=()=>{document.removeEventListener("mousedown",Q),window.removeEventListener("scroll",z),window.removeEventListener("resize",z),L&&L.removeEventListener("click",B)},this.revertSettings={innerHTML:A.innerHTML,tabIndex:A.tabIndex},A.tabIndex=-1,A.insertAdjacentElement("afterend",s.wrapper),s.sync(!1),a.items=[],delete a.optgroups,delete a.options,K(A,"invalid",()=>{s.isValid&&(s.isValid=!1,s.isInvalid=!0,s.refreshState())}),s.updateOriginalInput(),s.refreshItems(),s.close(!1),s.inputState(),s.isSetup=!0,A.disabled?s.disable():A.readOnly?s.setReadOnly(!0):s.enable(),s.on("change",this.onChange),se(A,"tomselected","ts-hidden-accessible"),s.trigger("initialize"),a.preload===!0&&s.preload()}setupOptions(s=[],a=[]){this.addOptions(s),$(a,c=>{this.registerOptionGroup(c)})}setupTemplates(){var s=this,a=s.settings.labelField,c=s.settings.optgroupLabelField,f={optgroup:g=>{let p=document.createElement("div");return p.className="optgroup",p.appendChild(g.options),p},optgroup_header:(g,p)=>'<div class="optgroup-header">'+p(g[c])+"</div>",option:(g,p)=>"<div>"+p(g[a])+"</div>",item:(g,p)=>"<div>"+p(g[a])+"</div>",option_create:(g,p)=>'<div class="create">Add <strong>'+p(g.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};s.settings.render=Object.assign({},f,s.settings.render)}setupCallbacks(){var s,a,c={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(s in c)a=this.settings[c[s]],a&&this.on(s,a)}sync(s=!0){let a=this,c=s?pr(a.input,{delimiter:a.settings.delimiter}):a.settings;a.setupOptions(c.options,c.optgroups),a.setValue(c.items||[],!0),a.lastQuery=null}onClick(){var s=this;if(s.activeItems.length>0){s.clearActiveItems(),s.focus();return}s.isFocused&&s.isOpen?s.blur():s.focus()}onMouseDown(){}onChange(){_e(this.input,"input"),_e(this.input,"change")}onPaste(s){var a=this;if(a.isInputHidden||a.isLocked){J(s);return}!a.settings.splitOn||setTimeout(()=>{var c=a.inputValue();if(!!c.match(a.settings.splitOn)){var f=c.trim().split(a.settings.splitOn);$(f,g=>{be(g)&&(this.options[g]?a.addItem(g):a.createItem(g))})}},0)}onKeyPress(s){var a=this;if(a.isLocked){J(s);return}var c=String.fromCharCode(s.keyCode||s.which);if(a.settings.create&&a.settings.mode==="multi"&&c===a.settings.delimiter){a.createItem(),J(s);return}}onKeyDown(s){var a=this;if(a.ignoreHover=!0,a.isLocked){s.keyCode!==Vi&&J(s);return}switch(s.keyCode){case Ro:if(Qe(Gt,s)&&a.control_input.value==""){J(s),a.selectAll();return}break;case lr:a.isOpen&&(J(s,!0),a.close()),a.clearActiveItems();return;case Mo:if(!a.isOpen&&a.hasOptions)a.open();else if(a.activeOption){let c=a.getAdjacent(a.activeOption,1);c&&a.setActiveOption(c)}J(s);return;case Fo:if(a.activeOption){let c=a.getAdjacent(a.activeOption,-1);c&&a.setActiveOption(c)}J(s);return;case Do:a.canSelect(a.activeOption)?(a.onOptionSelect(s,a.activeOption),J(s)):(a.settings.create&&a.createItem()||document.activeElement==a.control_input&&a.isOpen)&&J(s);return;case qi:a.advanceSelection(-1,s);return;case cr:a.advanceSelection(1,s);return;case Vi:a.settings.selectOnTab&&(a.canSelect(a.activeOption)&&(a.onOptionSelect(s,a.activeOption),J(s)),a.settings.create&&a.createItem()&&J(s));return;case ur:case No:a.deleteSelection(s);return}a.isInputHidden&&!Qe(Gt,s)&&J(s)}onInput(s){if(this.isLocked)return;let a=this.inputValue();if(this.lastValue!==a){if(this.lastValue=a,a==""){this._onInput();return}this.refreshTimeout&&clearTimeout(this.refreshTimeout),this.refreshTimeout=$o(()=>{this.refreshTimeout=null,this._onInput()},this.settings.refreshThrottle)}}_onInput(){let s=this.lastValue;this.settings.shouldLoad.call(this,s)&&this.load(s),this.refreshOptions(),this.trigger("type",s)}onOptionHover(s,a){this.ignoreHover||this.setActiveOption(a,!1)}onFocus(s){var a=this,c=a.isFocused;if(a.isDisabled||a.isReadOnly){a.blur(),J(s);return}a.ignoreFocus||(a.isFocused=!0,a.settings.preload==="focus"&&a.preload(),c||a.trigger("focus"),a.activeItems.length||(a.inputState(),a.refreshOptions(!!a.settings.openOnFocus)),a.refreshState())}onBlur(s){if(document.hasFocus()!==!1){var a=this;if(!!a.isFocused){a.isFocused=!1,a.ignoreFocus=!1;var c=()=>{a.close(),a.setActiveItem(),a.setCaret(a.items.length),a.trigger("blur")};a.settings.create&&a.settings.createOnBlur?a.createItem(null,c):c()}}}onOptionSelect(s,a){var c,f=this;a.parentElement&&a.parentElement.matches("[data-disabled]")||(a.classList.contains("create")?f.createItem(null,()=>{f.settings.closeAfterSelect&&f.close()}):(c=a.dataset.value,typeof c!="undefined"&&(f.lastQuery=null,f.addItem(c),f.settings.closeAfterSelect&&f.close(),!f.settings.hideSelected&&s.type&&/click/.test(s.type)&&f.setActiveOption(a))))}canSelect(s){return!!(this.isOpen&&s&&this.dropdown_content.contains(s))}onItemSelect(s,a){var c=this;return!c.isLocked&&c.settings.mode==="multi"?(J(s),c.setActiveItem(a,s),!0):!1}canLoad(s){return!(!this.settings.load||this.loadedSearches.hasOwnProperty(s))}load(s){let a=this;if(!a.canLoad(s))return;se(a.wrapper,a.settings.loadingClass),a.loading++;let c=a.loadCallback.bind(a);a.settings.load.call(a,s,c)}loadCallback(s,a){let c=this;c.loading=Math.max(c.loading-1,0),c.lastQuery=null,c.clearActiveOption(),c.setupOptions(s,a),c.refreshOptions(c.isFocused&&!c.isInputHidden),c.loading||De(c.wrapper,c.settings.loadingClass),c.trigger("load",s,a)}preload(){var s=this.wrapper.classList;s.contains("preloaded")||(s.add("preloaded"),this.load(""))}setTextboxValue(s=""){var a=this.control_input,c=a.value!==s;c&&(a.value=s,_e(a,"update"),this.lastValue=s)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(s,a){var c=a?[]:["change"];hr(this,c,()=>{this.clear(a),this.addItems(s,a)})}setMaxItems(s){s===0&&(s=null),this.settings.maxItems=s,this.refreshState()}setActiveItem(s,a){var c=this,f,g,p,_,A,x;if(c.settings.mode!=="single"){if(!s){c.clearActiveItems(),c.isFocused&&c.inputState();return}if(f=a&&a.type.toLowerCase(),f==="click"&&Qe("shiftKey",a)&&c.activeItems.length){for(x=c.getLastActive(),p=Array.prototype.indexOf.call(c.control.children,x),_=Array.prototype.indexOf.call(c.control.children,s),p>_&&(A=p,p=_,_=A),g=p;g<=_;g++)s=c.control.children[g],c.activeItems.indexOf(s)===-1&&c.setActiveItemClass(s);J(a)}else f==="click"&&Qe(Gt,a)||f==="keydown"&&Qe("shiftKey",a)?s.classList.contains("active")?c.removeActiveItem(s):c.setActiveItemClass(s):(c.clearActiveItems(),c.setActiveItemClass(s));c.inputState(),c.isFocused||c.focus()}}setActiveItemClass(s){let a=this,c=a.control.querySelector(".last-active");c&&De(c,"last-active"),se(s,"active last-active"),a.trigger("item_select",s),a.activeItems.indexOf(s)==-1&&a.activeItems.push(s)}removeActiveItem(s){var a=this.activeItems.indexOf(s);this.activeItems.splice(a,1),De(s,"active")}clearActiveItems(){De(this.activeItems,"active"),this.activeItems=[]}setActiveOption(s,a=!0){s!==this.activeOption&&(this.clearActiveOption(),s&&(this.activeOption=s,Y(this.focus_node,{"aria-activedescendant":s.getAttribute("id")}),Y(s,{"aria-selected":"true"}),se(s,"active"),a&&this.scrollToOption(s)))}scrollToOption(s,a){if(!s)return;let c=this.dropdown_content,f=c.clientHeight,g=c.scrollTop||0,p=s.offsetHeight,_=s.getBoundingClientRect().top-c.getBoundingClientRect().top+g;_+p>f+g?this.scroll(_-f+p,a):_<g&&this.scroll(_,a)}scroll(s,a){let c=this.dropdown_content;a&&(c.style.scrollBehavior=a),c.scrollTop=s,c.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(De(this.activeOption,"active"),Y(this.activeOption,{"aria-selected":null})),this.activeOption=null,Y(this.focus_node,{"aria-activedescendant":null})}selectAll(){let s=this;if(s.settings.mode==="single")return;let a=s.controlChildren();!a.length||(s.inputState(),s.close(),s.activeItems=a,$(a,c=>{s.setActiveItemClass(c)}))}inputState(){var s=this;!s.control.contains(s.control_input)||(Y(s.control_input,{placeholder:s.settings.placeholder}),s.activeItems.length>0||!s.isFocused&&s.settings.hidePlaceholder&&s.items.length>0?(s.setTextboxValue(),s.isInputHidden=!0):(s.settings.hidePlaceholder&&s.items.length>0&&Y(s.control_input,{placeholder:""}),s.isInputHidden=!1),s.wrapper.classList.toggle("input-hidden",s.isInputHidden))}inputValue(){return this.control_input.value.trim()}focus(){var s=this;s.isDisabled||s.isReadOnly||(s.ignoreFocus=!0,s.control_input.offsetWidth?s.control_input.focus():s.focus_node.focus(),setTimeout(()=>{s.ignoreFocus=!1,s.onFocus()},0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(s){return this.sifter.getScoreFunction(s,this.getSearchOptions())}getSearchOptions(){var s=this.settings,a=s.sortField;return typeof s.sortField=="string"&&(a=[{field:s.sortField}]),{fields:s.searchField,conjunction:s.searchConjunction,sort:a,nesting:s.nesting}}search(s){var a,c,f=this,g=this.getSearchOptions();if(f.settings.score&&(c=f.settings.score.call(f,s),typeof c!="function"))throw new Error('Tom Select "score" setting must be a function that returns a function');return s!==f.lastQuery?(f.lastQuery=s,a=f.sifter.search(s,Object.assign(g,{score:c})),f.currentResults=a):a=Object.assign({},f.currentResults),f.settings.hideSelected&&(a.items=a.items.filter(p=>{let _=be(p.id);return!(_&&f.items.indexOf(_)!==-1)})),a}refreshOptions(s=!0){var a,c,f,g,p,_,A,x,k,C;let I={},X=[];var L=this,B=L.inputValue();let Q=B===L.lastQuery||B==""&&L.lastQuery==null;var z=L.search(B),R=null,te=L.settings.shouldOpen||!1,Ce=L.dropdown_content;Q&&(R=L.activeOption,R&&(k=R.closest("[data-group]"))),g=z.items.length,typeof L.settings.maxOptions=="number"&&(g=Math.min(g,L.settings.maxOptions)),g>0&&(te=!0);let Zt=(U,q)=>{let H=I[U];if(H!==void 0){let W=X[H];if(W!==void 0)return[H,W.fragment]}let oe=document.createDocumentFragment();return H=X.length,X.push({fragment:oe,order:q,optgroup:U}),[H,oe]};for(a=0;a<g;a++){let U=z.items[a];if(!U)continue;let q=U.id,H=L.options[q];if(H===void 0)continue;let oe=Yt(q),W=L.getOption(oe,!0);for(L.settings.hideSelected||W.classList.toggle("selected",L.items.includes(oe)),p=H[L.settings.optgroupField]||"",_=Array.isArray(p)?p:[p],c=0,f=_&&_.length;c<f;c++){p=_[c];let ei=H.$order,ti=L.optgroups[p];ti===void 0?p="":ei=ti.$order;let[ra,sa]=Zt(p,ei);c>0&&(W=W.cloneNode(!0),Y(W,{id:H.$id+"-clone-"+c,"aria-selected":null}),W.classList.add("ts-cloned"),De(W,"active"),L.activeOption&&L.activeOption.dataset.value==q&&k&&k.dataset.group===p.toString()&&(R=W)),sa.appendChild(W),p!=""&&(I[p]=ra)}}L.settings.lockOptgroupOrder&&X.sort((U,q)=>U.order-q.order),A=document.createDocumentFragment(),$(X,U=>{let q=U.fragment,H=U.optgroup;if(!q||!q.children.length)return;let oe=L.optgroups[H];if(oe!==void 0){let W=document.createDocumentFragment(),ei=L.render("optgroup_header",oe);Ze(W,ei),Ze(W,q);let ti=L.render("optgroup",{group:oe,options:W});Ze(A,ti)}else Ze(A,q)}),Ce.innerHTML="",Ze(Ce,A),L.settings.highlight&&(Po(Ce),z.query.length&&z.tokens.length&&$(z.tokens,U=>{Io(Ce,U.regex)}));var ht=U=>{let q=L.render(U,{input:B});return q&&(te=!0,Ce.insertBefore(q,Ce.firstChild)),q};if(L.loading?ht("loading"):L.settings.shouldLoad.call(L,B)?z.items.length===0&&ht("no_results"):ht("not_loading"),x=L.canCreate(B),x&&(C=ht("option_create")),L.hasOptions=z.items.length>0||x,te){if(z.items.length>0){if(!R&&L.settings.mode==="single"&&L.items[0]!=null&&(R=L.getOption(L.items[0])),!Ce.contains(R)){let U=0;C&&!L.settings.addPrecedence&&(U=1),R=L.selectable()[U]}}else C&&(R=C);s&&!L.isOpen&&(L.open(),L.scrollToOption(R,"auto")),L.setActiveOption(R)}else L.clearActiveOption(),s&&L.isOpen&&L.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(s,a=!1){let c=this;if(Array.isArray(s))return c.addOptions(s,a),!1;let f=be(s[c.settings.valueField]);return f===null||c.options.hasOwnProperty(f)?!1:(s.$order=s.$order||++c.order,s.$id=c.inputId+"-opt-"+s.$order,c.options[f]=s,c.lastQuery=null,a&&(c.userOptions[f]=a,c.trigger("option_add",f,s)),f)}addOptions(s,a=!1){$(s,c=>{this.addOption(c,a)})}registerOption(s){return this.addOption(s)}registerOptionGroup(s){var a=be(s[this.settings.optgroupValueField]);return a===null?!1:(s.$order=s.$order||++this.order,this.optgroups[a]=s,a)}addOptionGroup(s,a){var c;a[this.settings.optgroupValueField]=s,(c=this.registerOptionGroup(a))&&this.trigger("optgroup_add",c,a)}removeOptionGroup(s){this.optgroups.hasOwnProperty(s)&&(delete this.optgroups[s],this.clearCache(),this.trigger("optgroup_remove",s))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(s,a){let c=this;var f,g;let p=be(s),_=be(a[c.settings.valueField]);if(p===null)return;let A=c.options[p];if(A==null)return;if(typeof _!="string")throw new Error("Value must be set in option data");let x=c.getOption(p),k=c.getItem(p);if(a.$order=a.$order||A.$order,delete c.options[p],c.uncacheValue(_),c.options[_]=a,x){if(c.dropdown_content.contains(x)){let C=c._render("option",a);Bi(x,C),c.activeOption===x&&c.setActiveOption(C)}x.remove()}k&&(g=c.items.indexOf(p),g!==-1&&c.items.splice(g,1,_),f=c._render("item",a),k.classList.contains("active")&&se(f,"active"),Bi(k,f)),c.lastQuery=null}removeOption(s,a){let c=this;s=Yt(s),c.uncacheValue(s),delete c.userOptions[s],delete c.options[s],c.lastQuery=null,c.trigger("option_remove",s),c.removeItem(s,a)}clearOptions(s){let a=(s||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();let c={};$(this.options,(f,g)=>{a(f,g)&&(c[g]=f)}),this.options=this.sifter.items=c,this.lastQuery=null,this.trigger("option_clear")}clearFilter(s,a){return this.items.indexOf(a)>=0}getOption(s,a=!1){let c=be(s);if(c===null)return null;let f=this.options[c];if(f!=null){if(f.$div)return f.$div;if(a)return this._render("option",f)}return null}getAdjacent(s,a,c="option"){var f=this,g;if(!s)return null;c=="item"?g=f.controlChildren():g=f.dropdown_content.querySelectorAll("[data-selectable]");for(let p=0;p<g.length;p++)if(g[p]==s)return a>0?g[p+1]:g[p-1];return null}getItem(s){if(typeof s=="object")return s;var a=be(s);return a!==null?this.control.querySelector(`[data-value="${fr(a)}"]`):null}addItems(s,a){var c=this,f=Array.isArray(s)?s:[s];f=f.filter(p=>c.items.indexOf(p)===-1);let g=f[f.length-1];f.forEach(p=>{c.isPending=p!==g,c.addItem(p,a)})}addItem(s,a){var c=a?[]:["change","dropdown_close"];hr(this,c,()=>{var f,g;let p=this,_=p.settings.mode,A=be(s);if(!(A&&p.items.indexOf(A)!==-1&&(_==="single"&&p.close(),_==="single"||!p.settings.duplicates))&&!(A===null||!p.options.hasOwnProperty(A))&&(_==="single"&&p.clear(a),!(_==="multi"&&p.isFull()))){if(f=p._render("item",p.options[A]),p.control.contains(f)&&(f=f.cloneNode(!0)),g=p.isFull(),p.items.splice(p.caretPos,0,A),p.insertAtCaret(f),p.isSetup){if(!p.isPending&&p.settings.hideSelected){let x=p.getOption(A),k=p.getAdjacent(x,1);k&&p.setActiveOption(k)}!p.isPending&&!p.settings.closeAfterSelect&&p.refreshOptions(p.isFocused&&_!=="single"),p.settings.closeAfterSelect!=!1&&p.isFull()?p.close():p.isPending||p.positionDropdown(),p.trigger("item_add",A,f),p.isPending||p.updateOriginalInput({silent:a})}(!p.isPending||!g&&p.isFull())&&(p.inputState(),p.refreshState())}})}removeItem(s=null,a){let c=this;if(s=c.getItem(s),!s)return;var f,g;let p=s.dataset.value;f=Xt(s),s.remove(),s.classList.contains("active")&&(g=c.activeItems.indexOf(s),c.activeItems.splice(g,1),De(s,"active")),c.items.splice(f,1),c.lastQuery=null,!c.settings.persist&&c.userOptions.hasOwnProperty(p)&&c.removeOption(p,a),f<c.caretPos&&c.setCaret(c.caretPos-1),c.updateOriginalInput({silent:a}),c.refreshState(),c.positionDropdown(),c.trigger("item_remove",p,s)}createItem(s=null,a=()=>{}){arguments.length===3&&(a=arguments[2]),typeof a!="function"&&(a=()=>{});var c=this,f=c.caretPos,g;if(s=s||c.inputValue(),!c.canCreate(s))return a(),!1;c.lock();var p=!1,_=A=>{if(c.unlock(),!A||typeof A!="object")return a();var x=be(A[c.settings.valueField]);if(typeof x!="string")return a();c.setTextboxValue(),c.addOption(A,!0),c.setCaret(f),c.addItem(x),a(A),p=!0};return typeof c.settings.create=="function"?g=c.settings.create.call(this,s,_):g={[c.settings.labelField]:s,[c.settings.valueField]:s},p||_(g),!0}refreshItems(){var s=this;s.lastQuery=null,s.isSetup&&s.addItems(s.items),s.updateOriginalInput(),s.refreshState()}refreshState(){let s=this;s.refreshValidityState();let a=s.isFull(),c=s.isLocked;s.wrapper.classList.toggle("rtl",s.rtl);let f=s.wrapper.classList;f.toggle("focus",s.isFocused),f.toggle("disabled",s.isDisabled),f.toggle("readonly",s.isReadOnly),f.toggle("required",s.isRequired),f.toggle("invalid",!s.isValid),f.toggle("locked",c),f.toggle("full",a),f.toggle("input-active",s.isFocused&&!s.isInputHidden),f.toggle("dropdown-active",s.isOpen),f.toggle("has-options",Lo(s.options)),f.toggle("has-items",s.items.length>0)}refreshValidityState(){var s=this;!s.input.validity||(s.isValid=s.input.validity.valid,s.isInvalid=!s.isValid)}isFull(){return this.settings.maxItems!==null&&this.items.length>=this.settings.maxItems}updateOriginalInput(s={}){let a=this;var c,f;let g=a.input.querySelector('option[value=""]');if(a.is_select_tag){let A=function(x,k,C){return x||(x=ee('<option value="'+Qt(k)+'">'+Qt(C)+"</option>")),x!=g&&a.input.append(x),p.push(x),(x!=g||_>0)&&(x.selected=!0),x},p=[],_=a.input.querySelectorAll("option:checked").length;a.input.querySelectorAll("option:checked").forEach(x=>{x.selected=!1}),a.items.length==0&&a.settings.mode=="single"?A(g,"",""):a.items.forEach(x=>{if(c=a.options[x],f=c[a.settings.labelField]||"",p.includes(c.$option)){let k=a.input.querySelector(`option[value="${fr(x)}"]:not(:checked)`);A(k,x,f)}else c.$option=A(c.$option,x,f)})}else a.input.value=a.getValue();a.isSetup&&(s.silent||a.trigger("change",a.getValue()))}open(){var s=this;s.isLocked||s.isOpen||s.settings.mode==="multi"&&s.isFull()||(s.isOpen=!0,Y(s.focus_node,{"aria-expanded":"true"}),s.refreshState(),Be(s.dropdown,{visibility:"hidden",display:"block"}),s.positionDropdown(),Be(s.dropdown,{visibility:"visible",display:"block"}),s.focus(),s.trigger("dropdown_open",s.dropdown))}close(s=!0){var a=this,c=a.isOpen;s&&(a.setTextboxValue(),a.settings.mode==="single"&&a.items.length&&a.inputState()),a.isOpen=!1,Y(a.focus_node,{"aria-expanded":"false"}),Be(a.dropdown,{display:"none"}),a.settings.hideSelected&&a.clearActiveOption(),a.refreshState(),c&&a.trigger("dropdown_close",a.dropdown)}positionDropdown(){if(this.settings.dropdownParent==="body"){var s=this.control,a=s.getBoundingClientRect(),c=s.offsetHeight+a.top+window.scrollY,f=a.left+window.scrollX;Be(this.dropdown,{width:a.width+"px",top:c+"px",left:f+"px"})}}clear(s){var a=this;if(!!a.items.length){var c=a.controlChildren();$(c,f=>{a.removeItem(f,!0)}),a.inputState(),s||a.updateOriginalInput(),a.trigger("clear")}}insertAtCaret(s){let a=this,c=a.caretPos,f=a.control;f.insertBefore(s,f.children[c]||null),a.setCaret(c+1)}deleteSelection(s){var a,c,f,g,p=this;a=s&&s.keyCode===ur?-1:1,c=jo(p.control_input);let _=[];if(p.activeItems.length)g=ar(p.activeItems,a),f=Xt(g),a>0&&f++,$(p.activeItems,A=>_.push(A));else if((p.isFocused||p.settings.mode==="single")&&p.items.length){let A=p.controlChildren(),x;a<0&&c.start===0&&c.length===0?x=A[p.caretPos-1]:a>0&&c.start===p.inputValue().length&&(x=A[p.caretPos]),x!==void 0&&_.push(x)}if(!p.shouldDelete(_,s))return!1;for(J(s,!0),typeof f!="undefined"&&p.setCaret(f);_.length;)p.removeItem(_.pop());return p.inputState(),p.positionDropdown(),p.refreshOptions(!1),!0}shouldDelete(s,a){let c=s.map(f=>f.dataset.value);return!(!c.length||typeof this.settings.onDelete=="function"&&this.settings.onDelete(c,a)===!1)}advanceSelection(s,a){var c,f,g=this;g.rtl&&(s*=-1),!g.inputValue().length&&(Qe(Gt,a)||Qe("shiftKey",a)?(c=g.getLastActive(s),c?c.classList.contains("active")?f=g.getAdjacent(c,s,"item"):f=c:s>0?f=g.control_input.nextElementSibling:f=g.control_input.previousElementSibling,f&&(f.classList.contains("active")&&g.removeActiveItem(c),g.setActiveItemClass(f))):g.moveCaret(s))}moveCaret(s){}getLastActive(s){let a=this.control.querySelector(".last-active");if(a)return a;var c=this.control.querySelectorAll(".active");if(c)return ar(c,s)}setCaret(s){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.setLocked(!0)}unlock(){this.setLocked(!1)}setLocked(s=this.isReadOnly||this.isDisabled){this.isLocked=s,this.refreshState()}disable(){this.setDisabled(!0),this.close()}enable(){this.setDisabled(!1)}setDisabled(s){this.focus_node.tabIndex=s?-1:this.tabIndex,this.isDisabled=s,this.input.disabled=s,this.control_input.disabled=s,this.setLocked()}setReadOnly(s){this.isReadOnly=s,this.input.readOnly=s,this.control_input.readOnly=s,this.setLocked()}destroy(){var s=this,a=s.revertSettings;s.trigger("destroy"),s.off(),s.wrapper.remove(),s.dropdown.remove(),s.input.innerHTML=a.innerHTML,s.input.tabIndex=a.tabIndex,De(s.input,"tomselected","ts-hidden-accessible"),s._destroy(),delete s.input.tomselect}render(s,a){var c,f;let g=this;if(typeof this.settings.render[s]!="function"||(f=g.settings.render[s].call(this,a,Qt),!f))return null;if(f=ee(f),s==="option"||s==="option_create"?a[g.settings.disabledField]?Y(f,{"aria-disabled":"true"}):Y(f,{"data-selectable":""}):s==="optgroup"&&(c=a.group[g.settings.optgroupValueField],Y(f,{"data-group":c}),a.group[g.settings.disabledField]&&Y(f,{"data-disabled":""})),s==="option"||s==="item"){let p=Yt(a[g.settings.valueField]);Y(f,{"data-value":p}),s==="item"?(se(f,g.settings.itemClass),Y(f,{"data-ts-item":""})):(se(f,g.settings.optionClass),Y(f,{role:"option",id:a.$id}),a.$div=f,g.options[p]=a)}return f}_render(s,a){let c=this.render(s,a);if(c==null)throw"HTMLElement expected";return c}clearCache(){$(this.options,s=>{s.$div&&(s.$div.remove(),delete s.$div)})}uncacheValue(s){let a=this.getOption(s);a&&a.remove()}canCreate(s){return this.settings.create&&s.length>0&&this.settings.createFilter.call(this,s)}hook(s,a,c){var f=this,g=f[a];f[a]=function(){var p,_;return s==="after"&&(p=g.apply(f,arguments)),_=c.apply(f,arguments),s==="instead"?_:(s==="before"&&(p=g.apply(f,arguments)),p)}}}function Uo(){K(this.input,"change",()=>{this.sync()})}function Bo(u){var s=this,a=s.onOptionSelect;s.settings.hideSelected=!1;let c=Object.assign({className:"tomselect-checkbox",checkedClassNames:void 0,uncheckedClassNames:void 0},u);var f=function(_,A){A?(_.checked=!0,c.uncheckedClassNames&&_.classList.remove(...c.uncheckedClassNames),c.checkedClassNames&&_.classList.add(...c.checkedClassNames)):(_.checked=!1,c.checkedClassNames&&_.classList.remove(...c.checkedClassNames),c.uncheckedClassNames&&_.classList.add(...c.uncheckedClassNames))},g=function(_){setTimeout(()=>{var A=_.querySelector("input."+c.className);A instanceof HTMLInputElement&&f(A,_.classList.contains("selected"))},1)};s.hook("after","setupTemplates",()=>{var p=s.settings.render.option;s.settings.render.option=(_,A)=>{var x=ee(p.call(s,_,A)),k=document.createElement("input");c.className&&k.classList.add(c.className),k.addEventListener("click",function(I){J(I)}),k.type="checkbox";let C=be(_[s.settings.valueField]);return f(k,!!(C&&s.items.indexOf(C)>-1)),x.prepend(k),x}}),s.on("item_remove",p=>{var _=s.getOption(p);_&&(_.classList.remove("selected"),g(_))}),s.on("item_add",p=>{var _=s.getOption(p);_&&g(_)}),s.hook("instead","onOptionSelect",(p,_)=>{if(_.classList.contains("selected")){_.classList.remove("selected"),s.removeItem(_.dataset.value),s.refreshOptions(),J(p,!0);return}a.call(s,p,_),g(_)})}function qo(u){let s=this,a=Object.assign({className:"clear-button",title:"Clear All",html:c=>`<div class="${c.className}" title="${c.title}">&#10799;</div>`},u);s.on("initialize",()=>{var c=ee(a.html(a));c.addEventListener("click",f=>{s.isLocked||(s.clear(),s.settings.mode==="single"&&s.settings.allowEmptyOption&&s.addItem(""),f.preventDefault(),f.stopPropagation())}),s.control.appendChild(c)})}let Vo=(u,s)=>{var a;(a=u.parentNode)==null||a.insertBefore(s,u.nextSibling)},Jo=(u,s)=>{var a;(a=u.parentNode)==null||a.insertBefore(s,u)},Ko=(u,s)=>{do{var a;if(s=(a=s)==null?void 0:a.previousElementSibling,u==s)return!0}while(s&&s.previousElementSibling);return!1};function zo(){var u=this;if(u.settings.mode!=="multi")return;var s=u.lock,a=u.unlock;let c=!0,f;u.hook("after","setupTemplates",()=>{var g=u.settings.render.item;u.settings.render.item=(p,_)=>{let A=ee(g.call(u,p,_));Y(A,{draggable:"true"});let x=B=>{c||J(B),B.stopPropagation()},k=B=>{f=A,setTimeout(()=>{A.classList.add("ts-dragging")},0)},C=B=>{B.preventDefault(),A.classList.add("ts-drag-over"),X(A,f)},I=()=>{A.classList.remove("ts-drag-over")},X=(B,Q)=>{Q!==void 0&&(Ko(Q,A)?Vo(B,Q):Jo(B,Q))},L=()=>{var B;document.querySelectorAll(".ts-drag-over").forEach(z=>z.classList.remove("ts-drag-over")),(B=f)==null||B.classList.remove("ts-dragging"),f=void 0;var Q=[];u.control.querySelectorAll("[data-value]").forEach(z=>{if(z.dataset.value){let R=z.dataset.value;R&&Q.push(R)}}),u.setValue(Q)};return K(A,"mousedown",x),K(A,"dragstart",k),K(A,"dragenter",C),K(A,"dragover",C),K(A,"dragleave",I),K(A,"dragend",L),A}}),u.hook("instead","lock",()=>(c=!1,s.call(u))),u.hook("instead","unlock",()=>(c=!0,a.call(u)))}function Wo(u){let s=this,a=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:c=>'<div class="'+c.headerClass+'"><div class="'+c.titleRowClass+'"><span class="'+c.labelClass+'">'+c.title+'</span><a class="'+c.closeClass+'">&times;</a></div></div>'},u);s.on("initialize",()=>{var c=ee(a.html(a)),f=c.querySelector("."+a.closeClass);f&&f.addEventListener("click",g=>{J(g,!0),s.close()}),s.dropdown.insertBefore(c,s.dropdown.firstChild)})}function Xo(){var u=this;u.hook("instead","setCaret",s=>{u.settings.mode==="single"||!u.control.contains(u.control_input)?s=u.items.length:(s=Math.max(0,Math.min(u.items.length,s)),s!=u.caretPos&&!u.isPending&&u.controlChildren().forEach((a,c)=>{c<s?u.control_input.insertAdjacentElement("beforebegin",a):u.control.appendChild(a)})),u.caretPos=s}),u.hook("instead","moveCaret",s=>{if(!u.isFocused)return;let a=u.getLastActive(s);if(a){let c=Xt(a);u.setCaret(s>0?c+1:c),u.setActiveItem(),De(a,"last-active")}else u.setCaret(u.caretPos+s)})}function Go(){let u=this;u.settings.shouldOpen=!0,u.hook("before","setup",()=>{u.focus_node=u.control,se(u.control_input,"dropdown-input");let s=ee('<div class="dropdown-input-wrap">');s.append(u.control_input),u.dropdown.insertBefore(s,u.dropdown.firstChild);let a=ee('<input class="items-placeholder" tabindex="-1" />');a.placeholder=u.settings.placeholder||"",u.control.append(a)}),u.on("initialize",()=>{u.control_input.addEventListener("keydown",a=>{switch(a.keyCode){case lr:u.isOpen&&(J(a,!0),u.close()),u.clearActiveItems();return;case Vi:u.focus_node.tabIndex=-1;break}return u.onKeyDown.call(u,a)}),u.on("blur",()=>{u.focus_node.tabIndex=u.isDisabled?-1:u.tabIndex}),u.on("dropdown_open",()=>{u.control_input.focus()});let s=u.onBlur;u.hook("instead","onBlur",a=>{if(!(a&&a.relatedTarget==u.control_input))return s.call(u)}),K(u.control_input,"blur",()=>u.onBlur()),u.hook("before","close",()=>{!u.isOpen||u.focus_node.focus({preventScroll:!0})})})}function Yo(){var u=this;u.on("initialize",()=>{var s=document.createElement("span"),a=u.control_input;s.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",u.wrapper.appendChild(s);var c=["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"];for(let g of c)s.style[g]=a.style[g];var f=()=>{s.textContent=a.value,a.style.width=s.clientWidth+"px"};f(),u.on("update item_add item_remove",f),K(a,"input",f),K(a,"keyup",f),K(a,"blur",f),K(a,"update",f)})}function Qo(){var u=this,s=u.deleteSelection;this.hook("instead","deleteSelection",a=>u.activeItems.length?s.call(u,a):!1)}function Zo(){this.hook("instead","setActiveItem",()=>{}),this.hook("instead","selectAll",()=>{})}function ea(){var u=this,s=u.onKeyDown;u.hook("instead","onKeyDown",a=>{var c,f,g,p;if(!u.isOpen||!(a.keyCode===qi||a.keyCode===cr))return s.call(u,a);u.ignoreHover=!0,p=Wt(u.activeOption,"[data-group]"),c=Xt(u.activeOption,"[data-selectable]"),p&&(a.keyCode===qi?p=p.previousSibling:p=p.nextSibling,p&&(g=p.querySelectorAll("[data-selectable]"),f=g[Math.min(g.length-1,c)],f&&u.setActiveOption(f)))})}function ta(u){let s=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},u);var a=this;if(!!s.append){var c='<a href="javascript:void(0)" class="'+s.className+'" tabindex="-1" title="'+Qt(s.title)+'">'+s.label+"</a>";a.hook("after","setupTemplates",()=>{var f=a.settings.render.item;a.settings.render.item=(g,p)=>{var _=ee(f.call(a,g,p)),A=ee(c);return _.appendChild(A),K(A,"mousedown",x=>{J(x,!0)}),K(A,"click",x=>{a.isLocked||(J(x,!0),!a.isLocked&&(!a.shouldDelete([_],x)||(a.removeItem(_),a.refreshOptions(!1),a.inputState())))}),_}})}}function ia(u){let s=this,a=Object.assign({text:c=>c[s.settings.labelField]},u);s.on("item_remove",function(c){if(!!s.isFocused&&s.control_input.value.trim()===""){var f=s.options[c];f&&s.setTextboxValue(a.text.call(s,f))}})}function na(){let u=this,s=u.canLoad,a=u.clearActiveOption,c=u.loadCallback;var f={},g,p=!1,_,A=[];if(u.settings.shouldLoadMore||(u.settings.shouldLoadMore=()=>{if(g.clientHeight/(g.scrollHeight-g.scrollTop)>.9)return!0;if(u.activeOption){var I=u.selectable(),X=Array.from(I).indexOf(u.activeOption);if(X>=I.length-2)return!0}return!1}),!u.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";u.settings.sortField=[{field:"$order"},{field:"$score"}];let x=C=>typeof u.settings.maxOptions=="number"&&g.children.length>=u.settings.maxOptions?!1:!!(C in f&&f[C]),k=(C,I)=>u.items.indexOf(I)>=0||A.indexOf(I)>=0;u.setNextUrl=(C,I)=>{f[C]=I},u.getUrl=C=>{if(C in f){let I=f[C];return f[C]=!1,I}return u.clearPagination(),u.settings.firstUrl.call(u,C)},u.clearPagination=()=>{f={}},u.hook("instead","clearActiveOption",()=>{if(!p)return a.call(u)}),u.hook("instead","canLoad",C=>C in f?x(C):s.call(u,C)),u.hook("instead","loadCallback",(C,I)=>{if(!p)u.clearOptions(k);else if(_){let X=C[0];X!==void 0&&(_.dataset.value=X[u.settings.valueField])}c.call(u,C,I),p=!1}),u.hook("after","refreshOptions",()=>{let C=u.lastValue;var I;x(C)?(I=u.render("loading_more",{query:C}),I&&(I.setAttribute("data-selectable",""),_=I)):C in f&&!g.querySelector(".no-results")&&(I=u.render("no_more_results",{query:C})),I&&(se(I,u.settings.optionClass),g.append(I))}),u.on("initialize",()=>{A=Object.keys(u.options),g=u.dropdown_content,u.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},u.settings.render),g.addEventListener("scroll",()=>{!u.settings.shouldLoadMore.call(u)||!x(u.lastValue)||p||(p=!0,u.load.call(u,u.lastValue))})})}return ce.define("change_listener",Uo),ce.define("checkbox_options",Bo),ce.define("clear_button",qo),ce.define("drag_drop",zo),ce.define("dropdown_header",Wo),ce.define("caret_position",Xo),ce.define("dropdown_input",Go),ce.define("input_autogrow",Yo),ce.define("no_backspace_delete",Qo),ce.define("no_active_items",Zo),ce.define("optgroup_columns",ea),ce.define("remove_button",ta),ce.define("restore_on_backspace",ia),ce.define("virtual_scroll",na),ce})});(function(){var e=t();function t(){if(typeof window.CustomEvent=="function")return window.CustomEvent;function r(o,l){l=l||{bubbles:!1,cancelable:!1,detail:void 0};var d=document.createEvent("CustomEvent");return d.initCustomEvent(o,l.bubbles,l.cancelable,l.detail),d}return r.prototype=window.Event.prototype,r}function i(r,o){var l=document.createElement("input");return l.type="hidden",l.name=r,l.value=o,l}function n(r,o){var l=r.getAttribute("data-to"),d=i("_method",r.getAttribute("data-method")),h=i("_csrf_token",r.getAttribute("data-csrf")),m=document.createElement("form"),v=document.createElement("input"),S=r.getAttribute("target");m.method=r.getAttribute("data-method")==="get"?"get":"post",m.action=l,m.style.display="none",S?m.target=S:o&&(m.target="_blank"),m.appendChild(h),m.appendChild(d),document.body.appendChild(m),v.type="submit",m.appendChild(v),v.click()}window.addEventListener("click",function(r){var o=r.target;if(!r.defaultPrevented)for(;o&&o.getAttribute;){var l=new e("phoenix.link.click",{bubbles:!0,cancelable:!0});if(!o.dispatchEvent(l))return r.preventDefault(),r.stopImmediatePropagation(),!1;if(o.getAttribute("data-method"))return n(o,r.metaKey||r.shiftKey),r.preventDefault(),!1;o=o.parentNode}},!1),window.addEventListener("phoenix.link.click",function(r){var o=r.target.getAttribute("data-confirm");o&&!window.confirm(o)&&r.preventDefault()},!1)})();var kt=e=>typeof e=="function"?e:function(){return e},da=typeof self!="undefined"?self:null,Ct=typeof window!="undefined"?window:null,Ot=da||Ct||Ot,ha="2.0.0",ke={connecting:0,open:1,closing:2,closed:3},fa=1e4,pa=1e3,ge={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},Me={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},Xi={longpoll:"longpoll",websocket:"websocket"},ga={complete:4},ni=class{constructor(e,t,i,n){this.channel=e,this.event=t,this.payload=i||function(){return{}},this.receivedResp=null,this.timeout=n,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(e){this.timeout=e,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(e,t){return this.hasReceived(e)&&t(this.receivedResp.response),this.recHooks.push({status:e,callback:t}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:e,response:t,_ref:i}){this.recHooks.filter(n=>n.status===e).forEach(n=>n.callback(t))}cancelRefEvent(){!this.refEvent||this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,e=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=e,this.matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}trigger(e,t){this.channel.trigger(this.refEvent,{status:e,response:t})}},wr=class{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},ma=class{constructor(e,t,i){this.state=ge.closed,this.topic=e,this.params=kt(t||{}),this.socket=i,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new ni(this,Me.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new wr(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=ge.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=ge.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=ge.closed,this.socket.remove(this)}),this.onError(n=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,n),this.isJoining()&&this.joinPush.reset(),this.state=ge.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new ni(this,Me.leave,kt({}),this.timeout).send(),this.state=ge.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(Me.reply,(n,r)=>{this.trigger(this.replyEventName(r),n)})}join(e=this.timeout){if(this.joinedOnce)throw new Error("tried to join multiple times. 'join' can only be called a single time per channel instance");return this.timeout=e,this.joinedOnce=!0,this.rejoin(),this.joinPush}onClose(e){this.on(Me.close,e)}onError(e){return this.on(Me.error,t=>e(t))}on(e,t){let i=this.bindingRef++;return this.bindings.push({event:e,ref:i,callback:t}),i}off(e,t){this.bindings=this.bindings.filter(i=>!(i.event===e&&(typeof t=="undefined"||t===i.ref)))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,i=this.timeout){if(t=t||{},!this.joinedOnce)throw new Error(`tried to push '${e}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let n=new ni(this,e,function(){return t},i);return this.canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}leave(e=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=ge.leaving;let t=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(Me.close,"leave")},i=new ni(this,Me.leave,kt({}),e);return i.receive("ok",()=>t()).receive("timeout",()=>t()),i.send(),this.canPush()||i.trigger("ok",{}),i}onMessage(e,t,i){return t}isMember(e,t,i,n){return this.topic!==e?!1:n&&n!==this.joinRef()?(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:e,event:t,payload:i,joinRef:n}),!1):!0}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=ge.joining,this.joinPush.resend(e))}trigger(e,t,i,n){let r=this.onMessage(e,t,i,n);if(t&&!r)throw new Error("channel onMessage callbacks must return the payload, modified or unmodified");let o=this.bindings.filter(l=>l.event===e);for(let l=0;l<o.length;l++)o[l].callback(r,i,n||this.joinRef())}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===ge.closed}isErrored(){return this.state===ge.errored}isJoined(){return this.state===ge.joined}isJoining(){return this.state===ge.joining}isLeaving(){return this.state===ge.leaving}},si=class{static request(e,t,i,n,r,o,l){if(Ot.XDomainRequest){let d=new Ot.XDomainRequest;return this.xdomainRequest(d,e,t,n,r,o,l)}else{let d=new Ot.XMLHttpRequest;return this.xhrRequest(d,e,t,i,n,r,o,l)}}static xdomainRequest(e,t,i,n,r,o,l){return e.timeout=r,e.open(t,i),e.onload=()=>{let d=this.parseJSON(e.responseText);l&&l(d)},o&&(e.ontimeout=o),e.onprogress=()=>{},e.send(n),e}static xhrRequest(e,t,i,n,r,o,l,d){return e.open(t,i,!0),e.timeout=o,e.setRequestHeader("Content-Type",n),e.onerror=()=>d&&d(null),e.onreadystatechange=()=>{if(e.readyState===ga.complete&&d){let h=this.parseJSON(e.responseText);d(h)}},l&&(e.ontimeout=l),e.send(r),e}static parseJSON(e){if(!e||e==="")return null;try{return JSON.parse(e)}catch(t){return console&&console.log("failed to parse JSON response",e),null}}static serialize(e,t){let i=[];for(var n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;let r=t?`${t}[${n}]`:n,o=e[n];typeof o=="object"?i.push(this.serialize(o,r)):i.push(encodeURIComponent(r)+"="+encodeURIComponent(o))}return i.join("&")}static appendParams(e,t){if(Object.keys(t).length===0)return e;let i=e.match(/\?/)?"&":"?";return`${e}${i}${this.serialize(t)}`}},Wi=class{constructor(e){this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(e),this.readyState=ke.connecting,this.poll()}normalizeEndpoint(e){return e.replace("ws://","http://").replace("wss://","https://").replace(new RegExp("(.*)/"+Xi.websocket),"$1/"+Xi.longpoll)}endpointURL(){return si.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(e,t,i){this.close(e,t,i),this.readyState=ke.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===ke.open||this.readyState===ke.connecting}poll(){this.ajax("GET",null,()=>this.ontimeout(),e=>{if(e){var{status:t,token:i,messages:n}=e;this.token=i}else t=0;switch(t){case 200:n.forEach(r=>{setTimeout(()=>this.onmessage({data:r}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=ke.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw new Error(`unhandled poll status ${t}`)}})}send(e){this.ajax("POST",e,()=>this.onerror("timeout"),t=>{(!t||t.status!==200)&&(this.onerror(t&&t.status),this.closeAndRetry(1011,"internal server error",!1))})}close(e,t,i){for(let r of this.reqs)r.abort();this.readyState=ke.closed;let n=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:e,reason:t,wasClean:i});typeof CloseEvent!="undefined"?this.onclose(new CloseEvent("close",n)):this.onclose(n)}ajax(e,t,i,n){let r,o=()=>{this.reqs.delete(r),i()};r=si.request(e,this.endpointURL(),"application/json",t,this.timeout,o,l=>{this.reqs.delete(r),this.isActive()&&n(l)}),this.reqs.add(r)}};var ri={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(e,t){if(e.payload.constructor===ArrayBuffer)return t(this.binaryEncode(e));{let i=[e.join_ref,e.ref,e.topic,e.event,e.payload];return t(JSON.stringify(i))}},decode(e,t){if(e.constructor===ArrayBuffer)return t(this.binaryDecode(e));{let[i,n,r,o,l]=JSON.parse(e);return t({join_ref:i,ref:n,topic:r,event:o,payload:l})}},binaryEncode(e){let{join_ref:t,ref:i,event:n,topic:r,payload:o}=e,l=this.META_LENGTH+t.length+i.length+r.length+n.length,d=new ArrayBuffer(this.HEADER_LENGTH+l),h=new DataView(d),m=0;h.setUint8(m++,this.KINDS.push),h.setUint8(m++,t.length),h.setUint8(m++,i.length),h.setUint8(m++,r.length),h.setUint8(m++,n.length),Array.from(t,S=>h.setUint8(m++,S.charCodeAt(0))),Array.from(i,S=>h.setUint8(m++,S.charCodeAt(0))),Array.from(r,S=>h.setUint8(m++,S.charCodeAt(0))),Array.from(n,S=>h.setUint8(m++,S.charCodeAt(0)));var v=new Uint8Array(d.byteLength+o.byteLength);return v.set(new Uint8Array(d),0),v.set(new Uint8Array(o),d.byteLength),v.buffer},binaryDecode(e){let t=new DataView(e),i=t.getUint8(0),n=new TextDecoder;switch(i){case this.KINDS.push:return this.decodePush(e,t,n);case this.KINDS.reply:return this.decodeReply(e,t,n);case this.KINDS.broadcast:return this.decodeBroadcast(e,t,n)}},decodePush(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),o=t.getUint8(3),l=this.HEADER_LENGTH+this.META_LENGTH-1,d=i.decode(e.slice(l,l+n));l=l+n;let h=i.decode(e.slice(l,l+r));l=l+r;let m=i.decode(e.slice(l,l+o));l=l+o;let v=e.slice(l,e.byteLength);return{join_ref:d,ref:null,topic:h,event:m,payload:v}},decodeReply(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),o=t.getUint8(3),l=t.getUint8(4),d=this.HEADER_LENGTH+this.META_LENGTH,h=i.decode(e.slice(d,d+n));d=d+n;let m=i.decode(e.slice(d,d+r));d=d+r;let v=i.decode(e.slice(d,d+o));d=d+o;let S=i.decode(e.slice(d,d+l));d=d+l;let y=e.slice(d,e.byteLength),E={status:S,response:y};return{join_ref:h,ref:m,topic:v,event:Me.reply,payload:E}},decodeBroadcast(e,t,i){let n=t.getUint8(1),r=t.getUint8(2),o=this.HEADER_LENGTH+2,l=i.decode(e.slice(o,o+n));o=o+n;let d=i.decode(e.slice(o,o+r));o=o+r;let h=e.slice(o,e.byteLength);return{join_ref:null,ref:null,topic:l,event:d,payload:h}}},Sr=class{constructor(e,t={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=t.timeout||fa,this.transport=t.transport||Ot.WebSocket||Wi,this.establishedConnections=0,this.defaultEncoder=ri.encode.bind(ri),this.defaultDecoder=ri.decode.bind(ri),this.closeWasClean=!1,this.binaryType=t.binaryType||"arraybuffer",this.connectClock=1,this.transport!==Wi?(this.encode=t.encode||this.defaultEncoder,this.decode=t.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let i=null;Ct&&Ct.addEventListener&&(Ct.addEventListener("pagehide",n=>{this.conn&&(this.disconnect(),i=this.connectClock)}),Ct.addEventListener("pageshow",n=>{i===this.connectClock&&(i=null,this.connect())})),this.heartbeatIntervalMs=t.heartbeatIntervalMs||3e4,this.rejoinAfterMs=n=>t.rejoinAfterMs?t.rejoinAfterMs(n):[1e3,2e3,5e3][n-1]||1e4,this.reconnectAfterMs=n=>t.reconnectAfterMs?t.reconnectAfterMs(n):[10,50,100,150,200,250,500,1e3,2e3][n-1]||5e3,this.logger=t.logger||null,this.longpollerTimeout=t.longpollerTimeout||2e4,this.params=kt(t.params||{}),this.endPoint=`${e}/${Xi.websocket}`,this.vsn=t.vsn||ha,this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new wr(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs)}getLongPollTransport(){return Wi}replaceTransport(e){this.connectClock++,this.closeWasClean=!0,this.reconnectTimer.reset(),this.sendBuffer=[],this.conn&&(this.conn.close(),this.conn=null),this.transport=e}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let e=si.appendParams(si.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return e.charAt(0)!=="/"?e:e.charAt(1)==="/"?`${this.protocol()}:${e}`:`${this.protocol()}://${location.host}${e}`}disconnect(e,t,i){this.connectClock++,this.closeWasClean=!0,this.reconnectTimer.reset(),this.teardown(e,t,i)}connect(e){e&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=kt(e)),!this.conn&&(this.connectClock++,this.closeWasClean=!1,this.conn=new this.transport(this.endPointURL()),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=t=>this.onConnError(t),this.conn.onmessage=t=>this.onConnMessage(t),this.conn.onclose=t=>this.onConnClose(t))}log(e,t,i){this.logger(e,t,i)}hasLogger(){return this.logger!==null}onOpen(e){let t=this.makeRef();return this.stateChangeCallbacks.open.push([t,e]),t}onClose(e){let t=this.makeRef();return this.stateChangeCallbacks.close.push([t,e]),t}onError(e){let t=this.makeRef();return this.stateChangeCallbacks.error.push([t,e]),t}onMessage(e){let t=this.makeRef();return this.stateChangeCallbacks.message.push([t,e]),t}ping(e){if(!this.isConnected())return!1;let t=this.makeRef(),i=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:t});let n=this.onMessage(r=>{r.ref===t&&(this.off([n]),e(Date.now()-i))});return!0}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,e])=>e())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),pa,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(e,t,i){if(!this.conn)return e&&e();this.waitForBufferDone(()=>{this.conn&&(t?this.conn.close(t,i||""):this.conn.close()),this.waitForSocketClosed(()=>{this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),e&&e()})})}waitForBufferDone(e,t=1){if(t===5||!this.conn||!this.conn.bufferedAmount){e();return}setTimeout(()=>{this.waitForBufferDone(e,t+1)},150*t)}waitForSocketClosed(e,t=1){if(t===5||!this.conn||this.conn.readyState===ke.closed){e();return}setTimeout(()=>{this.waitForSocketClosed(e,t+1)},150*t)}onConnClose(e){let t=e&&e.code;this.hasLogger()&&this.log("transport","close",e),this.triggerChanError(),this.clearHeartbeats(),!this.closeWasClean&&t!==1e3&&this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,i])=>i(e))}onConnError(e){this.hasLogger()&&this.log("transport",e);let t=this.transport,i=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,n])=>{n(e,t,i)}),(t===this.transport||i>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(e=>{e.isErrored()||e.isLeaving()||e.isClosed()||e.trigger(Me.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case ke.connecting:return"connecting";case ke.open:return"open";case ke.closing:return"closing";default:return"closed"}}isConnected(){return this.connectionState()==="open"}remove(e){this.off(e.stateChangeRefs),this.channels=this.channels.filter(t=>t.joinRef()!==e.joinRef())}off(e){for(let t in this.stateChangeCallbacks)this.stateChangeCallbacks[t]=this.stateChangeCallbacks[t].filter(([i])=>e.indexOf(i)===-1)}channel(e,t={}){let i=new ma(e,t,this);return this.channels.push(i),i}push(e){if(this.hasLogger()){let{topic:t,event:i,payload:n,ref:r,join_ref:o}=e;this.log("push",`${t} ${i} (${o}, ${r})`,n)}this.isConnected()?this.encode(e,t=>this.conn.send(t)):this.sendBuffer.push(()=>this.encode(e,t=>this.conn.send(t)))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}sendHeartbeat(){this.pendingHeartbeatRef&&!this.isConnected()||(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}onConnMessage(e){this.decode(e.data,t=>{let{topic:i,event:n,payload:r,ref:o,join_ref:l}=t;o&&o===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${r.status||""} ${i} ${n} ${o&&"("+o+")"||""}`,r);for(let d=0;d<this.channels.length;d++){let h=this.channels[d];!h.isMember(i,n,r,l)||h.trigger(n,r,o,l)}for(let d=0;d<this.stateChangeCallbacks.message.length;d++){let[,h]=this.stateChangeCallbacks.message[d];h(t)}})}leaveOpenTopic(e){let t=this.channels.find(i=>i.topic===e&&(i.isJoined()||i.isJoining()));t&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${e}"`),t.leave())}};var qr="consecutive-reloads",va=10,_a=5e3,ba=1e4,ya=3e4,Vr=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading"],ve="data-phx-component",Gi="data-phx-link",wa="track-static",Sa="data-phx-link-state",Pe="data-phx-ref",et="data-phx-ref-src",Jr="track-uploads",tt="data-phx-upload-ref",dn="data-phx-preflighted-refs",xa="data-phx-done-refs",xr="drop-target",an="data-phx-active-refs",mi="phx:live-file:updated",ln="data-phx-skip",Ar="data-phx-prune",Er="page-loading",Cr="phx-connected",Yi="phx-loading",Qi="phx-no-feedback",kr="phx-error",ft="data-phx-parent-id",hn="data-phx-main",Mt="data-phx-root-id",Aa="trigger-action",_i="feedback-for",cn="phx-has-focused",Ea=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],Kr=["checkbox","radio"],bi="phx-has-submitted",Ke="data-phx-session",gt=`[${Ke}]`,Or="data-phx-sticky",Dt="data-phx-static",Zi="data-phx-readonly",oi="data-phx-disabled",un="disable-with",ai="data-phx-disable-with-restore",Tt="hook",Ca="debounce",ka="throttle",yi="update",en="stream",Oa="key",Oe="phxPrivate",Tr="auto-recover",li="phx:live-socket:debug",tn="phx:live-socket:profiling",nn="phx:live-socket:latency-sim",Ta="progress",Lr="mounted",La=1,Ia=200,Pa="phx-",Ra=3e4,Lt="debounce-trigger",ci="throttled",Ir="debounce-prev-key",Da={debounce:300,throttle:300},ui="d",Te="s",me="c",Pr="e",Rr="r",Dr="t",Fa="p",Ma="stream",Na=class{constructor(e,t,i){this.liveSocket=i,this.entry=e,this.offset=0,this.chunkSize=t,this.chunkTimer=null,this.uploadChannel=i.channel(`lvu:${e.ref}`,{token:e.metadata()})}error(e){clearTimeout(this.chunkTimer),this.uploadChannel.leave(),this.entry.error(e)}upload(){this.uploadChannel.onError(e=>this.error(e)),this.uploadChannel.join().receive("ok",e=>this.readNextChunk()).receive("error",e=>this.error(e))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let e=new window.FileReader,t=this.entry.file.slice(this.offset,this.chunkSize+this.offset);e.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return le("Read error: "+i.target.error)},e.readAsArrayBuffer(t)}pushChunk(e){!this.uploadChannel.isJoined()||this.uploadChannel.push("chunk",e).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))})}},le=(e,t)=>console.error&&console.error(e,t),Ve=e=>{let t=typeof e;return t==="number"||t==="string"&&/^(0|[1-9]\d*)$/.test(e)};function $a(){let e=new Set,t=document.querySelectorAll("*[id]");for(let i=0,n=t.length;i<n;i++)e.has(t[i].id)?console.error(`Multiple IDs detected: ${t[i].id}. Ensure unique element ids.`):e.add(t[i].id)}var Ha=(e,t,i,n)=>{e.liveSocket.isDebugEnabled()&&console.log(`${e.id} ${t}: ${i} - `,n)},rn=e=>typeof e=="function"?e:function(){return e},vi=e=>JSON.parse(JSON.stringify(e)),Ft=(e,t,i)=>{do{if(e.matches(`[${t}]`)&&!e.disabled)return e;e=e.parentElement||e.parentNode}while(e!==null&&e.nodeType===1&&!(i&&i.isSameNode(e)||e.matches(gt)));return null},It=e=>e!==null&&typeof e=="object"&&!(e instanceof Array),ja=(e,t)=>JSON.stringify(e)===JSON.stringify(t),Fr=e=>{for(let t in e)return!1;return!0},Je=(e,t)=>e&&t(e),Ua=function(e,t,i,n){e.forEach(r=>{new Na(r,i.config.chunk_size,n).upload()})},zr={canPushState(){return typeof history.pushState!="undefined"},dropLocal(e,t,i){return e.removeItem(this.localKey(t,i))},updateLocal(e,t,i,n,r){let o=this.getLocal(e,t,i),l=this.localKey(t,i),d=o===null?n:r(o);return e.setItem(l,JSON.stringify(d)),d},getLocal(e,t,i){return JSON.parse(e.getItem(this.localKey(t,i)))},updateCurrentState(e){!this.canPushState()||history.replaceState(e(history.state||{}),"",window.location.href)},pushState(e,t,i){if(this.canPushState()){if(i!==window.location.href){if(t.type=="redirect"&&t.scroll){let r=history.state||{};r.scroll=t.scroll,history.replaceState(r,"",window.location.href)}delete t.scroll,history[e+"State"](t,"",i||null);let n=this.getHashTargetEl(window.location.hash);n?n.scrollIntoView():t.type==="redirect"&&window.scroll(0,0)}}else this.redirect(i)},setCookie(e,t){document.cookie=`${e}=${t}`},getCookie(e){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${e}s*=s*([^;]*).*$)|^.*$`),"$1")},redirect(e,t){t&&zr.setCookie("__phoenix_flash__",t+"; max-age=60000; path=/"),window.location=e},localKey(e,t){return`${e}-${t}`},getHashTargetEl(e){let t=e.toString().substring(1);if(t!=="")return document.getElementById(t)||document.querySelector(`a[name="${t}"]`)}},Le=zr,Se={byId(e){return document.getElementById(e)||le(`no id found for ${e}`)},removeClass(e,t){e.classList.remove(t),e.classList.length===0&&e.removeAttribute("class")},all(e,t,i){if(!e)return[];let n=Array.from(e.querySelectorAll(t));return i?n.forEach(i):n},childNodeLength(e){let t=document.createElement("template");return t.innerHTML=e,t.content.childElementCount},isUploadInput(e){return e.type==="file"&&e.getAttribute(tt)!==null},findUploadInputs(e){return this.all(e,`input[type="file"][${tt}]`)},findComponentNodeList(e,t){return this.filterWithinSameLiveView(this.all(e,`[${ve}="${t}"]`),e)},isPhxDestroyed(e){return!!(e.id&&Se.private(e,"destroyed"))},wantsNewTab(e){return e.ctrlKey||e.shiftKey||e.metaKey||e.button&&e.button===1||e.target.getAttribute("target")==="_blank"},isUnloadableFormSubmit(e){return!e.defaultPrevented&&!this.wantsNewTab(e)},isNewPageHref(e,t){let i;try{i=new URL(e)}catch(n){try{i=new URL(e,t)}catch(r){return!0}}return i.host===t.host&&i.protocol===t.protocol&&i.pathname===t.pathname&&i.search===t.search?i.hash===""&&!i.href.endsWith("#"):!0},markPhxChildDestroyed(e){this.isPhxChild(e)&&e.setAttribute(Ke,""),this.putPrivate(e,"destroyed",!0)},findPhxChildrenInFragment(e,t){let i=document.createElement("template");return i.innerHTML=e,this.findPhxChildren(i.content,t)},isIgnored(e,t){return(e.getAttribute(t)||e.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(e,t,i){return e.getAttribute&&i.indexOf(e.getAttribute(t))>=0},findPhxSticky(e){return this.all(e,`[${Or}]`)},findPhxChildren(e,t){return this.all(e,`${gt}[${ft}="${t}"]`)},findParentCIDs(e,t){let i=new Set(t),n=t.reduce((r,o)=>{let l=`[${ve}="${o}"] [${ve}]`;return this.filterWithinSameLiveView(this.all(e,l),e).map(d=>parseInt(d.getAttribute(ve))).forEach(d=>r.delete(d)),r},i);return n.size===0?new Set(t):n},filterWithinSameLiveView(e,t){return t.querySelector(gt)?e.filter(i=>this.withinSameLiveView(i,t)):e},withinSameLiveView(e,t){for(;e=e.parentNode;){if(e.isSameNode(t))return!0;if(e.getAttribute(Ke)!==null)return!1}},private(e,t){return e[Oe]&&e[Oe][t]},deletePrivate(e,t){e[Oe]&&delete e[Oe][t]},putPrivate(e,t,i){e[Oe]||(e[Oe]={}),e[Oe][t]=i},updatePrivate(e,t,i,n){let r=this.private(e,t);r===void 0?this.putPrivate(e,t,n(i)):this.putPrivate(e,t,n(r))},copyPrivates(e,t){t[Oe]&&(e[Oe]=t[Oe])},putTitle(e){let t=document.querySelector("title");if(t){let{prefix:i,suffix:n}=t.dataset;document.title=`${i||""}${e}${n||""}`}else document.title=e},debounce(e,t,i,n,r,o,l,d){let h=e.getAttribute(i),m=e.getAttribute(r);h===""&&(h=n),m===""&&(m=o);let v=h||m;switch(v){case null:return d();case"blur":this.once(e,"debounce-blur")&&e.addEventListener("blur",()=>d());return;default:let S=parseInt(v),y=()=>m?this.deletePrivate(e,ci):d(),E=this.incCycle(e,Lt,y);if(isNaN(S))return le(`invalid throttle/debounce value: ${v}`);if(m){let D=!1;if(t.type==="keydown"){let T=this.private(e,Ir);this.putPrivate(e,Ir,t.key),D=T!==t.key}if(!D&&this.private(e,ci))return!1;d(),this.putPrivate(e,ci,!0),setTimeout(()=>{l()&&this.triggerCycle(e,Lt)},S)}else setTimeout(()=>{l()&&this.triggerCycle(e,Lt,E)},S);let P=e.form;P&&this.once(P,"bind-debounce")&&P.addEventListener("submit",()=>{Array.from(new FormData(P).entries(),([D])=>{let T=P.querySelector(`[name="${D}"]`);this.incCycle(T,Lt),this.deletePrivate(T,ci)})}),this.once(e,"bind-debounce")&&e.addEventListener("blur",()=>this.triggerCycle(e,Lt))}},triggerCycle(e,t,i){let[n,r]=this.private(e,t);i||(i=n),i===n&&(this.incCycle(e,t),r())},once(e,t){return this.private(e,t)===!0?!1:(this.putPrivate(e,t,!0),!0)},incCycle(e,t,i=function(){}){let[n]=this.private(e,t)||[0,i];return n++,this.putPrivate(e,t,[n,i]),n},discardError(e,t,i){let n=t.getAttribute&&t.getAttribute(i),r=n&&e.querySelector(`[id="${n}"], [name="${n}"], [name="${n}[]"]`);!r||this.private(r,cn)||this.private(r,bi)||t.classList.add(Qi)},resetForm(e,t){Array.from(e.elements).forEach(i=>{let n=`[${t}="${i.id}"],
                   [${t}="${i.name}"],
                   [${t}="${i.name.replace(/\[\]$/,"")}"]`;this.deletePrivate(i,cn),this.deletePrivate(i,bi),this.all(document,n,r=>{r.classList.add(Qi)})})},showError(e,t){(e.id||e.name)&&this.all(e.form,`[${t}="${e.id}"], [${t}="${e.name}"]`,i=>{this.removeClass(i,Qi)})},isPhxChild(e){return e.getAttribute&&e.getAttribute(ft)},isPhxSticky(e){return e.getAttribute&&e.getAttribute(Or)!==null},firstPhxChild(e){return this.isPhxChild(e)?e:this.all(e,`[${ft}]`)[0]},dispatchEvent(e,t,i={}){let r={bubbles:i.bubbles===void 0?!0:!!i.bubbles,cancelable:!0,detail:i.detail||{}},o=t==="click"?new MouseEvent("click",r):new CustomEvent(t,r);e.dispatchEvent(o)},cloneNode(e,t){if(typeof t=="undefined")return e.cloneNode(!0);{let i=e.cloneNode(!1);return i.innerHTML=t,i}},mergeAttrs(e,t,i={}){let n=i.exclude||[],r=i.isIgnored,o=t.attributes;for(let d=o.length-1;d>=0;d--){let h=o[d].name;n.indexOf(h)<0&&e.setAttribute(h,t.getAttribute(h))}let l=e.attributes;for(let d=l.length-1;d>=0;d--){let h=l[d].name;r?h.startsWith("data-")&&!t.hasAttribute(h)&&e.removeAttribute(h):t.hasAttribute(h)||e.removeAttribute(h)}},mergeFocusedInput(e,t){e instanceof HTMLSelectElement||Se.mergeAttrs(e,t,{exclude:["value"]}),t.readOnly?e.setAttribute("readonly",!0):e.removeAttribute("readonly")},hasSelectionRange(e){return e.setSelectionRange&&(e.type==="text"||e.type==="textarea")},restoreFocus(e,t,i){if(!Se.isTextualInput(e))return;let n=e.matches(":focus");e.readOnly&&e.blur(),n||e.focus(),this.hasSelectionRange(e)&&e.setSelectionRange(t,i)},isFormInput(e){return/^(?:input|select|textarea)$/i.test(e.tagName)&&e.type!=="button"},syncAttrsToProps(e){e instanceof HTMLInputElement&&Kr.indexOf(e.type.toLocaleLowerCase())>=0&&(e.checked=e.getAttribute("checked")!==null)},isTextualInput(e){return Ea.indexOf(e.type)>=0},isNowTriggerFormExternal(e,t){return e.getAttribute&&e.getAttribute(t)!==null},syncPendingRef(e,t,i){let n=e.getAttribute(Pe);if(n===null)return!0;let r=e.getAttribute(et);return Se.isFormInput(e)||e.getAttribute(i)!==null?(Se.isUploadInput(e)&&Se.mergeAttrs(e,t,{isIgnored:!0}),Se.putPrivate(e,Pe,t),!1):(Vr.forEach(o=>{e.classList.contains(o)&&t.classList.add(o)}),t.setAttribute(Pe,n),t.setAttribute(et,r),!0)},cleanChildNodes(e,t){if(Se.isPhxUpdate(e,t,["append","prepend"])){let i=[];e.childNodes.forEach(n=>{n.id||(n.nodeType===Node.TEXT_NODE&&n.nodeValue.trim()===""||le(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(n.outerHTML||n.nodeValue).trim()}"

`),i.push(n))}),i.forEach(n=>n.remove())}},replaceRootContainer(e,t,i){let n=new Set(["id",Ke,Dt,hn,Mt]);if(e.tagName.toLowerCase()===t.toLowerCase())return Array.from(e.attributes).filter(r=>!n.has(r.name.toLowerCase())).forEach(r=>e.removeAttribute(r.name)),Object.keys(i).filter(r=>!n.has(r.toLowerCase())).forEach(r=>e.setAttribute(r,i[r])),e;{let r=document.createElement(t);return Object.keys(i).forEach(o=>r.setAttribute(o,i[o])),n.forEach(o=>r.setAttribute(o,e.getAttribute(o))),r.innerHTML=e.innerHTML,e.replaceWith(r),r}},getSticky(e,t,i){let n=(Se.private(e,"sticky")||[]).find(([r])=>t===r);if(n){let[r,o,l]=n;return l}else return typeof i=="function"?i():i},deleteSticky(e,t){this.updatePrivate(e,"sticky",[],i=>i.filter(([n,r])=>n!==t))},putSticky(e,t,i){let n=i(e);this.updatePrivate(e,"sticky",[],r=>{let o=r.findIndex(([l])=>t===l);return o>=0?r[o]=[t,i,n]:r.push([t,i,n]),r})},applyStickyOperations(e){let t=Se.private(e,"sticky");!t||t.forEach(([i,n,r])=>this.putSticky(e,i,n))}},w=Se,sn=class{static isActive(e,t){let i=t._phxRef===void 0,r=e.getAttribute(an).split(",").indexOf(ae.genFileRef(t))>=0;return t.size>0&&(i||r)}static isPreflighted(e,t){return e.getAttribute(dn).split(",").indexOf(ae.genFileRef(t))>=0&&this.isActive(e,t)}constructor(e,t,i){this.ref=ae.genFileRef(t),this.fileEl=e,this.file=t,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(mi,this._onElUpdated)}metadata(){return this.meta}progress(e){this._progress=Math.floor(e),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{ae.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}cancel(){this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(e="failed"){this.fileEl.removeEventListener(mi,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:e}),ae.clearFiles(this.fileEl)}onDone(e){this._onDone=()=>{this.fileEl.removeEventListener(mi,this._onElUpdated),e()}}onElUpdated(){this.fileEl.getAttribute(an).split(",").indexOf(this.ref)===-1&&this.cancel()}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref}}uploader(e){if(this.meta.uploader){let t=e[this.meta.uploader]||le(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:t}}else return{name:"channel",callback:Ua}}zipPostFlight(e){this.meta=e.entries[this.ref],this.meta||le(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:e})}},Ba=0,ae=class{static genFileRef(e){let t=e._phxRef;return t!==void 0?t:(e._phxRef=(Ba++).toString(),e._phxRef)}static getEntryDataURL(e,t,i){let n=this.activeFiles(e).find(r=>this.genFileRef(r)===t);i(URL.createObjectURL(n))}static hasUploadsInProgress(e){let t=0;return w.findUploadInputs(e).forEach(i=>{i.getAttribute(dn)!==i.getAttribute(xa)&&t++}),t>0}static serializeUploads(e){let t=this.activeFiles(e),i={};return t.forEach(n=>{let r={path:e.name},o=e.getAttribute(tt);i[o]=i[o]||[],r.ref=this.genFileRef(n),r.last_modified=n.lastModified,r.name=n.name||r.ref,r.relative_path=n.webkitRelativePath,r.type=n.type,r.size=n.size,i[o].push(r)}),i}static clearFiles(e){e.value=null,e.removeAttribute(tt),w.putPrivate(e,"files",[])}static untrackFile(e,t){w.putPrivate(e,"files",w.private(e,"files").filter(i=>!Object.is(i,t)))}static trackFiles(e,t,i){if(e.getAttribute("multiple")!==null){let n=t.filter(r=>!this.activeFiles(e).find(o=>Object.is(o,r)));w.putPrivate(e,"files",this.activeFiles(e).concat(n)),e.value=null}else i&&i.files.length>0&&(e.files=i.files),w.putPrivate(e,"files",t)}static activeFileInputs(e){let t=w.findUploadInputs(e);return Array.from(t).filter(i=>i.files&&this.activeFiles(i).length>0)}static activeFiles(e){return(w.private(e,"files")||[]).filter(t=>sn.isActive(e,t))}static inputsAwaitingPreflight(e){let t=w.findUploadInputs(e);return Array.from(t).filter(i=>this.filesAwaitingPreflight(i).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(t=>!sn.isPreflighted(e,t))}constructor(e,t,i){this.view=t,this.onComplete=i,this._entries=Array.from(ae.filesAwaitingPreflight(e)||[]).map(n=>new sn(e,n,t)),this.numEntriesInProgress=this._entries.length}entries(){return this._entries}initAdapterUpload(e,t,i){this._entries=this._entries.map(r=>(r.zipPostFlight(e),r.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()}),r));let n=this._entries.reduce((r,o)=>{let{name:l,callback:d}=o.uploader(i.uploaders);return r[l]=r[l]||{callback:d,entries:[]},r[l].entries.push(o),r},{});for(let r in n){let{callback:o,entries:l}=n[r];o(l,t,e,i)}}},qa={focusMain(){let e=document.querySelector("main h1, main, h1");if(e){let t=e.tabIndex;e.tabIndex=-1,e.focus(),e.tabIndex=t}},anyOf(e,t){return t.find(i=>e instanceof i)},isFocusable(e,t){return e instanceof HTMLAnchorElement&&e.rel!=="ignore"||e instanceof HTMLAreaElement&&e.href!==void 0||!e.disabled&&this.anyOf(e,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||e instanceof HTMLIFrameElement||e.tabIndex>0||!t&&e.tabIndex===0&&e.getAttribute("tabindex")!==null&&e.getAttribute("aria-hidden")!=="true"},attemptFocus(e,t){if(this.isFocusable(e,t))try{e.focus()}catch(i){}return!!document.activeElement&&document.activeElement.isSameNode(e)},focusFirstInteractive(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t,!0)||this.focusFirstInteractive(t,!0))return!0;t=t.nextElementSibling}},focusFirst(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t)||this.focusFirst(t))return!0;t=t.nextElementSibling}},focusLast(e){let t=e.lastElementChild;for(;t;){if(this.attemptFocus(t)||this.focusLast(t))return!0;t=t.previousElementSibling}}},pt=qa,Va={LiveFileUpload:{activeRefs(){return this.el.getAttribute(an)},preflightedRefs(){return this.el.getAttribute(dn)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let e=this.preflightedRefs();this.preflightedWas!==e&&(this.preflightedWas=e,e===""&&this.__view.cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(mi))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(tt)),ae.getEntryDataURL(this.inputEl,this.ref,e=>{this.url=e,this.el.src=e})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",()=>pt.focusLast(this.el)),this.focusEnd.addEventListener("focus",()=>pt.focusFirst(this.el)),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&pt.focusFirst(this.el)}}},Ja=Va,Ka=class{constructor(e,t,i){let n=new Set,r=new Set([...t.children].map(l=>l.id)),o=[];Array.from(e.children).forEach(l=>{if(l.id&&(n.add(l.id),r.has(l.id))){let d=l.previousElementSibling&&l.previousElementSibling.id;o.push({elementId:l.id,previousElementId:d})}}),this.containerId=t.id,this.updateType=i,this.elementsToModify=o,this.elementIdsToAdd=[...r].filter(l=>!n.has(l))}perform(){let e=w.byId(this.containerId);this.elementsToModify.forEach(t=>{t.previousElementId?Je(document.getElementById(t.previousElementId),i=>{Je(document.getElementById(t.elementId),n=>{n.previousElementSibling&&n.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",n)})}):Je(document.getElementById(t.elementId),i=>{i.previousElementSibling==null||e.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(t=>{Je(document.getElementById(t),i=>e.insertAdjacentElement("afterbegin",i))})}},Mr=11;function za(e,t){var i=t.attributes,n,r,o,l,d;if(!(t.nodeType===Mr||e.nodeType===Mr)){for(var h=i.length-1;h>=0;h--)n=i[h],r=n.name,o=n.namespaceURI,l=n.value,o?(r=n.localName||r,d=e.getAttributeNS(o,r),d!==l&&(n.prefix==="xmlns"&&(r=n.name),e.setAttributeNS(o,r,l))):(d=e.getAttribute(r),d!==l&&e.setAttribute(r,l));for(var m=e.attributes,v=m.length-1;v>=0;v--)n=m[v],r=n.name,o=n.namespaceURI,o?(r=n.localName||r,t.hasAttributeNS(o,r)||e.removeAttributeNS(o,r)):t.hasAttribute(r)||e.removeAttribute(r)}}var di,Wa="http://www.w3.org/1999/xhtml",he=typeof document=="undefined"?void 0:document,Xa=!!he&&"content"in he.createElement("template"),Ga=!!he&&he.createRange&&"createContextualFragment"in he.createRange();function Ya(e){var t=he.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function Qa(e){di||(di=he.createRange(),di.selectNode(he.body));var t=di.createContextualFragment(e);return t.childNodes[0]}function Za(e){var t=he.createElement("body");return t.innerHTML=e,t.childNodes[0]}function el(e){return e=e.trim(),Xa?Ya(e):Ga?Qa(e):Za(e)}function hi(e,t){var i=e.nodeName,n=t.nodeName,r,o;return i===n?!0:(r=i.charCodeAt(0),o=n.charCodeAt(0),r<=90&&o>=97?i===n.toUpperCase():o<=90&&r>=97?n===i.toUpperCase():!1)}function tl(e,t){return!t||t===Wa?he.createElement(e):he.createElementNS(t,e)}function il(e,t){for(var i=e.firstChild;i;){var n=i.nextSibling;t.appendChild(i),i=n}return t}function on(e,t,i){e[i]!==t[i]&&(e[i]=t[i],e[i]?e.setAttribute(i,""):e.removeAttribute(i))}var Nr={OPTION:function(e,t){var i=e.parentNode;if(i){var n=i.nodeName.toUpperCase();n==="OPTGROUP"&&(i=i.parentNode,n=i&&i.nodeName.toUpperCase()),n==="SELECT"&&!i.hasAttribute("multiple")&&(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),i.selectedIndex=-1)}on(e,t,"selected")},INPUT:function(e,t){on(e,t,"checked"),on(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var i=t.value;e.value!==i&&(e.value=i);var n=e.firstChild;if(n){var r=n.nodeValue;if(r==i||!i&&r==e.placeholder)return;n.nodeValue=i}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var i=-1,n=0,r=e.firstChild,o,l;r;)if(l=r.nodeName&&r.nodeName.toUpperCase(),l==="OPTGROUP")o=r,r=o.firstChild;else{if(l==="OPTION"){if(r.hasAttribute("selected")){i=n;break}n++}r=r.nextSibling,!r&&o&&(r=o.nextSibling,o=null)}e.selectedIndex=i}}},Pt=1,$r=11,Hr=3,jr=8;function qe(){}function nl(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}function rl(e){return function(i,n,r){if(r||(r={}),typeof n=="string")if(i.nodeName==="#document"||i.nodeName==="HTML"||i.nodeName==="BODY"){var o=n;n=he.createElement("html"),n.innerHTML=o}else n=el(n);else n.nodeType===$r&&(n=n.firstElementChild);var l=r.getNodeKey||nl,d=r.onBeforeNodeAdded||qe,h=r.onNodeAdded||qe,m=r.onBeforeElUpdated||qe,v=r.onElUpdated||qe,S=r.onBeforeNodeDiscarded||qe,y=r.onNodeDiscarded||qe,E=r.onBeforeElChildrenUpdated||qe,P=r.skipFromChildren||qe,D=r.addChild||function(M,F){return M.appendChild(F)},T=r.childrenOnly===!0,b=Object.create(null),O=[];function N(M){O.push(M)}function V(M,F){if(M.nodeType===Pt)for(var G=M.firstChild;G;){var j=void 0;F&&(j=l(G))?N(j):(y(G),G.firstChild&&V(G,F)),G=G.nextSibling}}function de(M,F,G){S(M)!==!1&&(F&&F.removeChild(M),y(M),V(M,G))}function fe(M){if(M.nodeType===Pt||M.nodeType===$r)for(var F=M.firstChild;F;){var G=l(F);G&&(b[G]=F),fe(F),F=F.nextSibling}}fe(i);function He(M){h(M);for(var F=M.firstChild;F;){var G=F.nextSibling,j=l(F);if(j){var $=b[j];$&&hi(F,$)?(F.parentNode.replaceChild($,F),je($,F)):He(F)}else He(F);F=G}}function wt(M,F,G){for(;F;){var j=F.nextSibling;(G=l(F))?N(G):de(F,M,!0),F=j}}function je(M,F,G){var j=l(F);j&&delete b[j],!(!G&&(m(M,F)===!1||(e(M,F),v(M),E(M,F)===!1)))&&(M.nodeName!=="TEXTAREA"?St(M,F):Nr.TEXTAREA(M,F))}function St(M,F){var G=P(M),j=F.firstChild,$=M.firstChild,Ue,Ee,pe,ee,we;e:for(;j;){for(ee=j.nextSibling,Ue=l(j);!G&&$;){if(pe=$.nextSibling,j.isSameNode&&j.isSameNode($)){j=ee,$=pe;continue e}Ee=l($);var dt=$.nodeType,_e=void 0;if(dt===j.nodeType&&(dt===Pt?(Ue?Ue!==Ee&&((we=b[Ue])?pe===we?_e=!1:(M.insertBefore(we,$),Ee?N(Ee):de($,M,!0),$=we):_e=!1):Ee&&(_e=!1),_e=_e!==!1&&hi($,j),_e&&je($,j)):(dt===Hr||dt==jr)&&(_e=!0,$.nodeValue!==j.nodeValue&&($.nodeValue=j.nodeValue))),_e){j=ee,$=pe;continue e}Ee?N(Ee):de($,M,!0),$=pe}if(Ue&&(we=b[Ue])&&hi(we,j))G||D(M,we),je(we,j);else{var Be=d(j);Be!==!1&&(Be&&(j=Be),j.actualize&&(j=j.actualize(M.ownerDocument||he)),D(M,j),He(j))}j=ee,$=pe}wt(M,$,Ee);var se=Nr[M.nodeName];se&&se(M,F)}var re=i,ct=re.nodeType,xt=n.nodeType;if(!T){if(ct===Pt)xt===Pt?hi(i,n)||(y(i),re=il(i,tl(n.nodeName,n.namespaceURI))):re=n;else if(ct===Hr||ct===jr){if(xt===ct)return re.nodeValue!==n.nodeValue&&(re.nodeValue=n.nodeValue),re;re=n}}if(re===n)y(i);else{if(n.isSameNode&&n.isSameNode(re))return;if(je(re,n,T),O)for(var At=0,ut=O.length;At<ut;At++){var Et=b[O[At]];Et&&de(Et,Et.parentNode,!1)}}return!T&&re!==i&&i.parentNode&&(re.actualize&&(re=re.actualize(i.ownerDocument||he)),i.parentNode.replaceChild(re,i)),re}}var sl=rl(za),Ur=sl,fi=class{static patchEl(e,t,i){Ur(e,t,{childrenOnly:!1,onBeforeElUpdated:(n,r)=>{if(i&&i.isSameNode(n)&&w.isFormInput(n))return w.mergeFocusedInput(n,r),!1}})}constructor(e,t,i,n,r,o){this.view=e,this.liveSocket=e.liveSocket,this.container=t,this.id=i,this.rootID=e.root.id,this.html=n,this.streams=r,this.streamInserts={},this.targetCID=o,this.cidPatch=Ve(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]}}before(e,t){this.callbacks[`before${e}`].push(t)}after(e,t){this.callbacks[`after${e}`].push(t)}trackBefore(e,...t){this.callbacks[`before${e}`].forEach(i=>i(...t))}trackAfter(e,...t){this.callbacks[`after${e}`].forEach(i=>i(...t))}markPrunableContentForRemoval(){let e=this.liveSocket.binding(yi);w.all(this.container,`[${e}=${en}]`,t=>t.innerHTML=""),w.all(this.container,`[${e}=append] > *, [${e}=prepend] > *`,t=>{t.setAttribute(Ar,"")})}perform(){let{view:e,liveSocket:t,container:i,html:n}=this,r=this.isCIDPatch()?this.targetCIDContainer(n):i;if(this.isCIDPatch()&&!r)return;let o=t.getActiveElement(),{selectionStart:l,selectionEnd:d}=o&&w.hasSelectionRange(o)?o:{},h=t.binding(yi),m=t.binding(_i),v=t.binding(un),S=t.binding(Aa),y=[],E=[],P=[],D=null,T=t.time("premorph container prep",()=>this.buildDiffHTML(i,n,h,r));return this.trackBefore("added",i),this.trackBefore("updated",i,i),t.time("morphdom",()=>{this.streams.forEach(([b,O])=>{this.streamInserts=Object.assign(this.streamInserts,b),O.forEach(N=>{let V=i.querySelector(`[id="${N}"]`);V&&(this.maybePendingRemove(V)||(V.remove(),this.onNodeDiscarded(V)))})}),Ur(r,T,{childrenOnly:r.getAttribute(ve)===null,getNodeKey:b=>w.isPhxDestroyed(b)?null:b.id,skipFromChildren:b=>b.getAttribute(h)===en,addChild:(b,O)=>{let N=O.id?this.streamInserts[O.id]:void 0;if(N===void 0)return b.appendChild(O);if(N===0)b.insertAdjacentElement("afterbegin",O);else if(N===-1)b.appendChild(O);else if(N>0){let V=Array.from(b.children)[N];b.insertBefore(O,V)}},onBeforeNodeAdded:b=>(this.trackBefore("added",b),b),onNodeAdded:b=>{b instanceof HTMLImageElement&&b.srcset?b.srcset=b.srcset:b instanceof HTMLVideoElement&&b.autoplay&&b.play(),w.isNowTriggerFormExternal(b,S)&&(D=b),w.discardError(r,b,m),(w.isPhxChild(b)&&e.ownsElement(b)||w.isPhxSticky(b)&&e.ownsElement(b.parentNode))&&this.trackAfter("phxChildAdded",b),y.push(b)},onNodeDiscarded:b=>this.onNodeDiscarded(b),onBeforeNodeDiscarded:b=>b.getAttribute&&b.getAttribute(Ar)!==null?!0:!(b.parentElement!==null&&b.id&&w.isPhxUpdate(b.parentElement,h,[en,"append","prepend"])||this.maybePendingRemove(b)||this.skipCIDSibling(b)),onElUpdated:b=>{w.isNowTriggerFormExternal(b,S)&&(D=b),E.push(b),this.maybeReOrderStream(b)},onBeforeElUpdated:(b,O)=>{if(w.cleanChildNodes(O,h),this.skipCIDSibling(O)||w.isPhxSticky(b))return!1;if(w.isIgnored(b,h)||b.form&&b.form.isSameNode(D))return this.trackBefore("updated",b,O),w.mergeAttrs(b,O,{isIgnored:!0}),E.push(b),w.applyStickyOperations(b),!1;if(b.type==="number"&&b.validity&&b.validity.badInput)return!1;if(!w.syncPendingRef(b,O,v))return w.isUploadInput(b)&&(this.trackBefore("updated",b,O),E.push(b)),w.applyStickyOperations(b),!1;if(w.isPhxChild(O)){let V=b.getAttribute(Ke);return w.mergeAttrs(b,O,{exclude:[Dt]}),V!==""&&b.setAttribute(Ke,V),b.setAttribute(Mt,this.rootID),w.applyStickyOperations(b),!1}return w.copyPrivates(O,b),w.discardError(r,O,m),o&&b.isSameNode(o)&&w.isFormInput(b)&&b.type!=="hidden"?(this.trackBefore("updated",b,O),w.mergeFocusedInput(b,O),w.syncAttrsToProps(b),E.push(b),w.applyStickyOperations(b),!1):(w.isPhxUpdate(O,h,["append","prepend"])&&P.push(new Ka(b,O,O.getAttribute(h))),w.syncAttrsToProps(O),w.applyStickyOperations(O),this.trackBefore("updated",b,O),!0)}})}),t.isDebugEnabled()&&$a(),P.length>0&&t.time("post-morph append/prepend restoration",()=>{P.forEach(b=>b.perform())}),t.silenceEvents(()=>w.restoreFocus(o,l,d)),w.dispatchEvent(document,"phx:update"),y.forEach(b=>this.trackAfter("added",b)),E.forEach(b=>this.trackAfter("updated",b)),this.transitionPendingRemoves(),D&&(t.unload(),D.submit()),!0}onNodeDiscarded(e){(w.isPhxChild(e)||w.isPhxSticky(e))&&this.liveSocket.destroyViewByEl(e),this.trackAfter("discarded",e)}maybePendingRemove(e){return e.getAttribute&&e.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(e),!0):!1}maybeReOrderStream(e){let t=e.id?this.streamInserts[e.id]:void 0;if(t!==void 0){if(t===0)e.parentElement.insertBefore(e,e.parentElement.firstElementChild);else if(t>0){let i=Array.from(e.parentElement.children),n=i.indexOf(e);if(t>=i.length-1)e.parentElement.appendChild(e);else{let r=i[t];n>t?e.parentElement.insertBefore(e,r):e.parentElement.insertBefore(e,r.nextElementSibling)}}}}transitionPendingRemoves(){let{pendingRemoves:e,liveSocket:t}=this;e.length>0&&(t.transitionRemoves(e),t.requestDOMUpdate(()=>{e.forEach(i=>{let n=w.firstPhxChild(i);n&&t.destroyViewByEl(n),i.remove()}),this.trackAfter("transitionsDiscarded",e)}))}isCIDPatch(){return this.cidPatch}skipCIDSibling(e){return e.nodeType===Node.ELEMENT_NODE&&e.getAttribute(ln)!==null}targetCIDContainer(e){if(!this.isCIDPatch())return;let[t,...i]=w.findComponentNodeList(this.container,this.targetCID);return i.length===0&&w.childNodeLength(e)===1?t:t&&t.parentNode}buildDiffHTML(e,t,i,n){let r=this.isCIDPatch(),o=r&&n.getAttribute(ve)===this.targetCID.toString();if(!r||o)return t;{let l=null,d=document.createElement("template");l=w.cloneNode(n);let[h,...m]=w.findComponentNodeList(l,this.targetCID);return d.innerHTML=t,m.forEach(v=>v.remove()),Array.from(l.childNodes).forEach(v=>{v.id&&v.nodeType===Node.ELEMENT_NODE&&v.getAttribute(ve)!==this.targetCID.toString()&&(v.setAttribute(ln,""),v.innerHTML="")}),Array.from(d.content.childNodes).forEach(v=>l.insertBefore(v,h)),h.remove(),l.outerHTML}}indexOf(e,t){return Array.from(e.children).indexOf(t)}},Br=class{static extract(e){let{[Rr]:t,[Pr]:i,[Dr]:n}=e;return delete e[Rr],delete e[Pr],delete e[Dr],{diff:e,title:n,reply:t||null,events:i||[]}}constructor(e,t){this.viewId=e,this.rendered={},this.mergeDiff(t)}parentViewId(){return this.viewId}toString(e){let[t,i]=this.recursiveToString(this.rendered,this.rendered[me],e);return[t,i]}recursiveToString(e,t=e[me],i){i=i?new Set(i):null;let n={buffer:"",components:t,onlyCids:i,streams:new Set};return this.toOutputBuffer(e,null,n),[n.buffer,n.streams]}componentCIDs(e){return Object.keys(e[me]||{}).map(t=>parseInt(t))}isComponentOnlyDiff(e){return e[me]?Object.keys(e).length===1:!1}getComponent(e,t){return e[me][t]}mergeDiff(e){let t=e[me],i={};if(delete e[me],this.rendered=this.mutableMerge(this.rendered,e),this.rendered[me]=this.rendered[me]||{},t){let n=this.rendered[me];for(let r in t)t[r]=this.cachedFindComponent(r,t[r],n,t,i);for(let r in t)n[r]=t[r];e[me]=t}}cachedFindComponent(e,t,i,n,r){if(r[e])return r[e];{let o,l,d=t[Te];if(Ve(d)){let h;d>0?h=this.cachedFindComponent(d,n[d],i,n,r):h=i[-d],l=h[Te],o=this.cloneMerge(h,t),o[Te]=l}else o=t[Te]!==void 0?t:this.cloneMerge(i[e]||{},t);return r[e]=o,o}}mutableMerge(e,t){return t[Te]!==void 0?t:(this.doMutableMerge(e,t),e)}doMutableMerge(e,t){for(let i in t){let n=t[i],r=e[i];It(n)&&n[Te]===void 0&&It(r)?this.doMutableMerge(r,n):e[i]=n}}cloneMerge(e,t){let i=Fe(Fe({},e),t);for(let n in i){let r=t[n],o=e[n];It(r)&&r[Te]===void 0&&It(o)&&(i[n]=this.cloneMerge(o,r))}return i}componentToString(e){let[t,i]=this.recursiveCIDToString(this.rendered[me],e);return[t,i]}pruneCIDs(e){e.forEach(t=>delete this.rendered[me][t])}get(){return this.rendered}isNewFingerprint(e={}){return!!e[Te]}templateStatic(e,t){return typeof e=="number"?t[e]:e}toOutputBuffer(e,t,i){if(e[ui])return this.comprehensionToBuffer(e,t,i);let{[Te]:n}=e;n=this.templateStatic(n,t),i.buffer+=n[0];for(let r=1;r<n.length;r++)this.dynamicToBuffer(e[r-1],t,i),i.buffer+=n[r]}comprehensionToBuffer(e,t,i){let{[ui]:n,[Te]:r,[Ma]:o}=e,[l,d]=o||[{},[]];r=this.templateStatic(r,t);let h=t||e[Fa];for(let m=0;m<n.length;m++){let v=n[m];i.buffer+=r[0];for(let S=1;S<r.length;S++)this.dynamicToBuffer(v[S-1],h,i),i.buffer+=r[S]}o!==void 0&&(e[ui].length>0||d.length>0)&&(e[ui]=[],i.streams.add(o))}dynamicToBuffer(e,t,i){if(typeof e=="number"){let[n,r]=this.recursiveCIDToString(i.components,e,i.onlyCids);i.buffer+=n,i.streams=new Set([...i.streams,...r])}else It(e)?this.toOutputBuffer(e,t,i):i.buffer+=e}recursiveCIDToString(e,t,i){let n=e[t]||le(`no component for CID ${t}`,e),r=document.createElement("template"),[o,l]=this.recursiveToString(n,e,i);r.innerHTML=o;let d=r.content,h=i&&!i.has(t),[m,v]=Array.from(d.childNodes).reduce(([S,y],E,P)=>E.nodeType===Node.ELEMENT_NODE?E.getAttribute(ve)?[S,!0]:(E.setAttribute(ve,t),E.id||(E.id=`${this.parentViewId()}-${t}-${P}`),h&&(E.setAttribute(ln,""),E.innerHTML=""),[!0,y]):E.nodeValue.trim()!==""?(le(`only HTML element tags are allowed at the root of components.

got: "${E.nodeValue.trim()}"

within:
`,r.innerHTML.trim()),E.replaceWith(this.createSpan(E.nodeValue,t)),[!0,y]):(E.remove(),[S,y]),[!1,!1]);return!m&&!v?(le(`expected at least one HTML element tag inside a component, but the component is empty:
`,r.innerHTML.trim()),[this.createSpan("",t).outerHTML,l]):!m&&v?(le("expected at least one HTML element tag directly inside a component, but only subcomponents were found. A component must render at least one HTML tag directly inside itself.",r.innerHTML.trim()),[r.innerHTML,l]):[r.innerHTML,l]}createSpan(e,t){let i=document.createElement("span");return i.innerText=e,i.setAttribute(ve,t),i}},ol=1,Rt=class{static makeID(){return ol++}static elementID(e){return e.phxHookId}constructor(e,t,i){this.__view=e,this.liveSocket=e.liveSocket,this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,this.el=t,this.el.phxHookId=this.constructor.makeID();for(let n in this.__callbacks)this[n]=this.__callbacks[n]}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed()}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}pushEvent(e,t={},i=function(){}){return this.__view.pushHookEvent(null,e,t,i)}pushEventTo(e,t,i={},n=function(){}){return this.__view.withinTargets(e,(r,o)=>r.pushHookEvent(o,t,i,n))}handleEvent(e,t){let i=(n,r)=>r?e:t(n.detail);return window.addEventListener(`phx:${e}`,i),this.__listeners.add(i),i}removeHandleEvent(e){let t=e(null,!0);window.removeEventListener(`phx:${t}`,e),this.__listeners.delete(e)}upload(e,t){return this.__view.dispatchUploads(e,t)}uploadTo(e,t,i){return this.__view.withinTargets(e,n=>n.dispatchUploads(t,i))}__cleanup__(){this.__listeners.forEach(e=>this.removeHandleEvent(e))}},pi=null,al={exec(e,t,i,n,r){let[o,l]=r||[null,{}];(t.charAt(0)==="["?JSON.parse(t):[[o,l]]).forEach(([h,m])=>{h===o&&l.data&&(m.data=Object.assign(m.data||{},l.data)),this.filterToEls(n,m).forEach(v=>{this[`exec_${h}`](e,t,i,n,v,m)})})},isVisible(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length>0)},exec_exec(e,t,i,n,r,[o,l]){(l?w.all(document,l):[n]).forEach(h=>{let m=h.getAttribute(o);if(!m)throw new Error(`expected ${o} to contain JS command on "${l}"`);i.liveSocket.execJS(h,m,e)})},exec_dispatch(e,t,i,n,r,{to:o,event:l,detail:d,bubbles:h}){d=d||{},d.dispatcher=n,w.dispatchEvent(r,l,{detail:d,bubbles:h})},exec_push(e,t,i,n,r,o){if(!i.isConnected())return;let{event:l,data:d,target:h,page_loading:m,loading:v,value:S,dispatcher:y}=o,E={loading:v,value:S,target:h,page_loading:!!m},P=e==="change"&&y?y:n,D=h||P.getAttribute(i.binding("target"))||P;i.withinTargets(D,(T,b)=>{if(e==="change"){let{newCid:O,_target:N,callback:V}=o;N=N||(w.isFormInput(n)?n.name:void 0),N&&(E._target=N),T.pushInput(n,b,O,l||t,E,V)}else if(e==="submit"){let{submitter:O}=o;T.submitForm(n,b,l||t,O,E)}else T.pushEvent(e,n,b,l||t,d,E)})},exec_navigate(e,t,i,n,r,{href:o,replace:l}){i.liveSocket.historyRedirect(o,l?"replace":"push")},exec_patch(e,t,i,n,r,{href:o,replace:l}){i.liveSocket.pushHistoryPatch(o,l?"replace":"push",n)},exec_focus(e,t,i,n,r){window.requestAnimationFrame(()=>pt.attemptFocus(r))},exec_focus_first(e,t,i,n,r){window.requestAnimationFrame(()=>pt.focusFirstInteractive(r)||pt.focusFirst(r))},exec_push_focus(e,t,i,n,r){window.requestAnimationFrame(()=>pi=r||n)},exec_pop_focus(e,t,i,n,r){window.requestAnimationFrame(()=>{pi&&pi.focus(),pi=null})},exec_add_class(e,t,i,n,r,{names:o,transition:l,time:d}){this.addOrRemoveClasses(r,o,[],l,d,i)},exec_remove_class(e,t,i,n,r,{names:o,transition:l,time:d}){this.addOrRemoveClasses(r,[],o,l,d,i)},exec_transition(e,t,i,n,r,{time:o,transition:l}){this.addOrRemoveClasses(r,[],[],l,o,i)},exec_toggle(e,t,i,n,r,{display:o,ins:l,outs:d,time:h}){this.toggle(e,i,r,o,l,d,h)},exec_show(e,t,i,n,r,{display:o,transition:l,time:d}){this.show(e,i,r,o,l,d)},exec_hide(e,t,i,n,r,{display:o,transition:l,time:d}){this.hide(e,i,r,o,l,d)},exec_set_attr(e,t,i,n,r,{attr:[o,l]}){this.setOrRemoveAttrs(r,[[o,l]],[])},exec_remove_attr(e,t,i,n,r,{attr:o}){this.setOrRemoveAttrs(r,[],[o])},show(e,t,i,n,r,o){this.isVisible(i)||this.toggle(e,t,i,n,r,null,o)},hide(e,t,i,n,r,o){this.isVisible(i)&&this.toggle(e,t,i,n,null,r,o)},toggle(e,t,i,n,r,o,l){let[d,h,m]=r||[[],[],[]],[v,S,y]=o||[[],[],[]];if(d.length>0||v.length>0)if(this.isVisible(i)){let E=()=>{this.addOrRemoveClasses(i,S,d.concat(h).concat(m)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,v,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,y,S))})};i.dispatchEvent(new Event("phx:hide-start")),t.transition(l,E,()=>{this.addOrRemoveClasses(i,[],v.concat(y)),w.putSticky(i,"toggle",P=>P.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))})}else{if(e==="remove")return;let E=()=>{this.addOrRemoveClasses(i,h,v.concat(S).concat(y));let P=n||this.defaultDisplay(i);w.putSticky(i,"toggle",D=>D.style.display=P),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,d,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,m,h))})};i.dispatchEvent(new Event("phx:show-start")),t.transition(l,E,()=>{this.addOrRemoveClasses(i,[],d.concat(m)),i.dispatchEvent(new Event("phx:show-end"))})}else this.isVisible(i)?window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:hide-start")),w.putSticky(i,"toggle",E=>E.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:show-start"));let E=n||this.defaultDisplay(i);w.putSticky(i,"toggle",P=>P.style.display=E),i.dispatchEvent(new Event("phx:show-end"))})},addOrRemoveClasses(e,t,i,n,r,o){let[l,d,h]=n||[[],[],[]];if(l.length>0){let m=()=>this.addOrRemoveClasses(e,d.concat(l),[]),v=()=>this.addOrRemoveClasses(e,t.concat(h),i.concat(l).concat(d));return o.transition(r,m,v)}window.requestAnimationFrame(()=>{let[m,v]=w.getSticky(e,"classes",[[],[]]),S=t.filter(D=>m.indexOf(D)<0&&!e.classList.contains(D)),y=i.filter(D=>v.indexOf(D)<0&&e.classList.contains(D)),E=m.filter(D=>i.indexOf(D)<0).concat(S),P=v.filter(D=>t.indexOf(D)<0).concat(y);w.putSticky(e,"classes",D=>(D.classList.remove(...P),D.classList.add(...E),[E,P]))})},setOrRemoveAttrs(e,t,i){let[n,r]=w.getSticky(e,"attrs",[[],[]]),o=t.map(([h,m])=>h).concat(i),l=n.filter(([h,m])=>!o.includes(h)).concat(t),d=r.filter(h=>!o.includes(h)).concat(i);w.putSticky(e,"attrs",h=>(d.forEach(m=>h.removeAttribute(m)),l.forEach(([m,v])=>h.setAttribute(m,v)),[l,d]))},hasAllClasses(e,t){return t.every(i=>e.classList.contains(i))},isToggledOut(e,t){return!this.isVisible(e)||this.hasAllClasses(e,t)},filterToEls(e,{to:t}){return t?w.all(document,t):[e]},defaultDisplay(e){return{tr:"table-row",td:"table-cell"}[e.tagName.toLowerCase()]||"block"}},Ie=al,gi=(e,t,i=[])=>{let h=t,{submitter:n}=h,r=_r(h,["submitter"]),o=new FormData(e);n&&n.hasAttribute("name")&&n.form&&n.form===e&&o.append(n.name,n.value);let l=[];o.forEach((m,v,S)=>{m instanceof File&&l.push(v)}),l.forEach(m=>o.delete(m));let d=new URLSearchParams;for(let[m,v]of o.entries())(i.length===0||i.indexOf(m)>=0)&&d.append(m,v);for(let m in r)d.append(m,r[m]);return d.toString()},Wr=class{constructor(e,t,i,n,r){this.isDead=!1,this.liveSocket=t,this.flash=n,this.parent=i,this.root=i?i.root:this,this.el=e,this.id=this.el.id,this.ref=0,this.childJoins=0,this.loaderTimer=null,this.pendingDiffs=[],this.pruningCIDs=[],this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(o){o&&o()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.uploaders={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>({redirect:this.redirect?this.href:void 0,url:this.redirect?void 0:this.href||void 0,params:this.connectParams(r),session:this.getSession(),static:this.getStatic(),flash:this.flash}))}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(hn)}connectParams(e){let t=this.liveSocket.params(this.el),i=w.all(document,`[${this.binding(wa)}]`).map(n=>n.src||n.href).filter(n=>typeof n=="string");return i.length>0&&(t._track_static=i),t._mounts=this.joinCount,t._live_referer=e,t}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(Ke)}getStatic(){let e=this.el.getAttribute(Dt);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let t=()=>{e();for(let i in this.viewHooks)this.destroyHook(this.viewHooks[i])};w.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",t).receive("error",t).receive("timeout",t)}setContainerClasses(...e){this.el.classList.remove(Cr,Yi,kr),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let t in this.viewHooks)this.viewHooks[t].__disconnected();this.setContainerClasses(Yi)}}execAll(e){w.all(this.el,`[${e}]`,t=>this.liveSocket.execJS(t,t.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),this.setContainerClasses(Cr),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,t){this.liveSocket.log(this,e,t)}transition(e,t,i=function(){}){this.liveSocket.transition(e,t,i)}withinTargets(e,t){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,i=>t(i,e));if(Ve(e))w.findComponentNodeList(this.el,e).length===0?le(`no component found matching phx-target of ${e}`):t(this,parseInt(e));else{let i=Array.from(document.querySelectorAll(e));i.length===0&&le(`nothing found matching the phx-target selector "${e}"`),i.forEach(n=>this.liveSocket.owner(n,r=>t(r,n)))}}applyDiff(e,t,i){this.log(e,()=>["",vi(t)]);let{diff:n,reply:r,events:o,title:l}=Br.extract(t);i({diff:n,reply:r,events:o}),l&&window.requestAnimationFrame(()=>w.putTitle(l))}onJoin(e){let{rendered:t,container:i}=e;if(i){let[n,r]=i;this.el=w.replaceRootContainer(this.el,n,r)}this.childJoins=0,this.joinPending=!0,this.flash=null,Le.dropLocal(this.liveSocket.localStorage,window.location.pathname,qr),this.applyDiff("mount",t,({diff:n,events:r})=>{this.rendered=new Br(this.id,n);let[o,l]=this.renderContainer(null,"join");this.dropPendingRefs();let d=this.formsForRecovery(o);this.joinCount++,d.length>0?d.forEach(([h,m,v],S)=>{this.pushFormRecovery(h,v,y=>{S===d.length-1&&this.onJoinComplete(y,o,l,r)})}):this.onJoinComplete(e,o,l,r)})}dropPendingRefs(){w.all(document,`[${et}="${this.id}"][${Pe}]`,e=>{e.removeAttribute(Pe),e.removeAttribute(et)})}onJoinComplete({live_patch:e},t,i,n){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,t,i,n);w.findPhxChildrenInFragment(t,this.id).filter(o=>{let l=o.id&&this.el.querySelector(`[id="${o.id}"]`),d=l&&l.getAttribute(Dt);return d&&o.setAttribute(Dt,d),this.joinChild(o)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,n)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,t,i,n)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,n)])}attachTrueDocEl(){this.el=w.byId(this.id),this.el.setAttribute(Mt,this.root.id)}execNewMounted(){w.all(this.el,`[${this.binding(Tt)}], [data-phx-${Tt}]`,e=>{this.maybeAddNewHook(e)}),w.all(this.el,`[${this.binding(Lr)}]`,e=>this.maybeMounted(e))}applyJoinPatch(e,t,i,n){this.attachTrueDocEl();let r=new fi(this,this.el,this.id,t,i,null);if(r.markPrunableContentForRemoval(),this.performPatch(r,!1),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(n),this.applyPendingUpdates(),e){let{kind:o,to:l}=e;this.liveSocket.historyPatch(l,o)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,t){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,t]);let i=this.getHook(e),n=i&&w.isIgnored(e,this.binding(yi));if(i&&!e.isEqualNode(t)&&!(n&&ja(e.dataset,t.dataset)))return i.__beforeUpdate(),i}maybeMounted(e){let t=e.getAttribute(this.binding(Lr)),i=t&&w.private(e,"mounted");t&&!i&&(this.liveSocket.execJS(e,t),w.putPrivate(e,"mounted",!0))}maybeAddNewHook(e,t){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,t){let i=[],n=!1,r=new Set;return e.after("added",o=>{this.liveSocket.triggerDOM("onNodeAdded",[o]),this.maybeAddNewHook(o),o.getAttribute&&this.maybeMounted(o)}),e.after("phxChildAdded",o=>{w.isPhxSticky(o)?this.liveSocket.joinRootViews():n=!0}),e.before("updated",(o,l)=>{this.triggerBeforeUpdateHook(o,l)&&r.add(o.id)}),e.after("updated",o=>{r.has(o.id)&&this.getHook(o).__updated()}),e.after("discarded",o=>{o.nodeType===Node.ELEMENT_NODE&&i.push(o)}),e.after("transitionsDiscarded",o=>this.afterElementsRemoved(o,t)),e.perform(),this.afterElementsRemoved(i,t),n}afterElementsRemoved(e,t){let i=[];e.forEach(n=>{let r=w.all(n,`[${ve}]`),o=w.all(n,`[${this.binding(Tt)}]`);r.concat(n).forEach(l=>{let d=this.componentID(l);Ve(d)&&i.indexOf(d)===-1&&i.push(d)}),o.concat(n).forEach(l=>{let d=this.getHook(l);d&&this.destroyHook(d)})}),t&&this.maybePushComponentsDestroyed(i)}joinNewChildren(){w.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(ft)][e.id]}destroyDescendent(e){for(let t in this.root.children)for(let i in this.root.children[t])if(i===e)return this.root.children[t][i].destroy()}joinChild(e){if(!this.getChildById(e.id)){let i=new Wr(e,this.liveSocket,this);return this.root.children[this.id][i.id]=i,i.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,t])=>{e.isDestroyed()||t()}),this.pendingJoinOps=[]})}update(e,t){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:t});this.rendered.mergeDiff(e);let i=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{w.findParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(r=>{this.componentPatch(this.rendered.getComponent(e,r),r)&&(i=!0)})}):Fr(e)||this.liveSocket.time("full patch complete",()=>{let[n,r]=this.renderContainer(e,"update"),o=new fi(this,this.el,this.id,n,r,null);i=this.performPatch(o,!0)}),this.liveSocket.dispatchEvents(t),i&&this.joinNewChildren()}renderContainer(e,t){return this.liveSocket.time(`toString diff (${t})`,()=>{let i=this.el.tagName,n=e?this.rendered.componentCIDs(e).concat(this.pruningCIDs):null,[r,o]=this.rendered.toString(n);return[`<${i}>${r}</${i}>`,o]})}componentPatch(e,t){if(Fr(e))return!1;let[i,n]=this.rendered.componentToString(t),r=new fi(this,this.el,this.id,i,n,t);return this.performPatch(r,!0)}getHook(e){return this.viewHooks[Rt.elementID(e)]}addHook(e){if(Rt.elementID(e)||!e.getAttribute)return;let t=e.getAttribute(`data-phx-${Tt}`)||e.getAttribute(this.binding(Tt));if(t&&!this.ownsElement(e))return;let i=this.liveSocket.getHookCallbacks(t);if(i){e.id||le(`no DOM ID for hook "${t}". Hooks require a unique ID on each element.`,e);let n=new Rt(this,e,i);return this.viewHooks[Rt.elementID(n.el)]=n,n}else t!==null&&le(`unknown hook found for "${t}"`,e)}destroyHook(e){e.__destroyed(),e.__cleanup__(),delete this.viewHooks[Rt.elementID(e.el)]}applyPendingUpdates(){this.pendingDiffs.forEach(({diff:e,events:t})=>this.update(e,t)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates())}eachChild(e){let t=this.root.children[this.id]||{};for(let i in t)e(this.getChildById(i))}onChannel(e,t){this.liveSocket.onChannel(this.channel,e,i=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>t(i)]):this.liveSocket.requestDOMUpdate(()=>t(i))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:t,events:i})=>this.update(t,i))})}),this.onChannel("redirect",({to:e,flash:t})=>this.onRedirect({to:e,flash:t})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:t,kind:i,flash:n}=e,r=this.expandURL(t);this.liveSocket.historyRedirect(r,i,n)}onLivePatch(e){let{to:t,kind:i}=e;this.href=this.expandURL(t),this.liveSocket.historyPatch(t,i)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:t}){this.liveSocket.redirect(e,t)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=t=>{t=t||function(){},e?e(this.joinCount,t):t()},this.liveSocket.wrapPush(this,{timeout:!1},()=>this.channel.join().receive("ok",t=>{this.isDestroyed()||this.liveSocket.requestDOMUpdate(()=>this.onJoin(t))}).receive("error",t=>!this.isDestroyed()&&this.onJoinError(t)).receive("timeout",()=>!this.isDestroyed()&&this.onJoinError({reason:"timeout"})))}onJoinError(e){if(e.reason==="reload")return this.log("error",()=>[`failed mount with ${e.status}. Falling back to page request`,e]),this.onRedirect({to:this.href});if(e.reason==="unauthorized"||e.reason==="stale")return this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.onRedirect({to:this.href});if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);this.log("error",()=>["unable to join",e]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this)}onClose(e){if(!this.isDestroyed()){if(this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(Ia)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||this.displayError()}displayError(){this.isMain()&&w.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(Yi,kr),this.execAll(this.binding("disconnected"))}pushWithReply(e,t,i,n=function(){}){if(!this.isConnected())return;let[r,[o],l]=e?e():[null,[],{}],d=function(){};return(l.page_loading||o&&o.getAttribute(this.binding(Er))!==null)&&(d=this.liveSocket.withPageLoading({kind:"element",target:o})),typeof i.cid!="number"&&delete i.cid,this.liveSocket.wrapPush(this,{timeout:!0},()=>this.channel.push(t,i,Ra).receive("ok",h=>{let m=v=>{h.redirect&&this.onRedirect(h.redirect),h.live_patch&&this.onLivePatch(h.live_patch),h.live_redirect&&this.onLiveRedirect(h.live_redirect),r!==null&&this.undoRefs(r),d(),n(h,v)};h.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",h.diff,({diff:v,reply:S,events:y})=>{this.update(v,y),m(S)})}):m(null)}))}undoRefs(e){!this.isConnected()||w.all(document,`[${et}="${this.id}"][${Pe}="${e}"]`,t=>{let i=t.getAttribute(oi);t.removeAttribute(Pe),t.removeAttribute(et),t.getAttribute(Zi)!==null&&(t.readOnly=!1,t.removeAttribute(Zi)),i!==null&&(t.disabled=i==="true",t.removeAttribute(oi)),Vr.forEach(o=>w.removeClass(t,o));let n=t.getAttribute(ai);n!==null&&(t.innerText=n,t.removeAttribute(ai));let r=w.private(t,Pe);if(r){let o=this.triggerBeforeUpdateHook(t,r);fi.patchEl(t,r,this.liveSocket.getActiveElement()),o&&o.__updated(),w.deletePrivate(t,Pe)}})}putRef(e,t,i={}){let n=this.ref++,r=this.binding(un);return i.loading&&(e=e.concat(w.all(document,i.loading))),e.forEach(o=>{o.classList.add(`phx-${t}-loading`),o.setAttribute(Pe,n),o.setAttribute(et,this.el.id);let l=o.getAttribute(r);l!==null&&(o.getAttribute(ai)||o.setAttribute(ai,o.innerText),l!==""&&(o.innerText=l),o.setAttribute("disabled",""))}),[n,e,i]}componentID(e){let t=e.getAttribute&&e.getAttribute(ve);return t?parseInt(t):null}targetComponentID(e,t,i={}){if(Ve(t))return t;let n=e.getAttribute(this.binding("target"));return Ve(n)?parseInt(n):t&&(n!==null||i.target)?this.closestComponentID(t):null}closestComponentID(e){return Ve(e)?e:e?Je(e.closest(`[${ve}]`),t=>this.ownsElement(t)&&this.componentID(t)):null}pushHookEvent(e,t,i,n){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",t,i]),!1;let[r,o,l]=this.putRef([],"hook");return this.pushWithReply(()=>[r,o,l],"event",{type:"hook",event:t,value:i,cid:this.closestComponentID(e)},(d,h)=>n(h,r)),r}extractMeta(e,t,i){let n=this.binding("value-");for(let r=0;r<e.attributes.length;r++){t||(t={});let o=e.attributes[r].name;o.startsWith(n)&&(t[o.replace(n,"")]=e.getAttribute(o))}if(e.value!==void 0&&(t||(t={}),t.value=e.value,e.tagName==="INPUT"&&Kr.indexOf(e.type)>=0&&!e.checked&&delete t.value),i){t||(t={});for(let r in i)t[r]=i[r]}return t}pushEvent(e,t,i,n,r,o={}){this.pushWithReply(()=>this.putRef([t],e,o),"event",{type:e,event:n,value:this.extractMeta(t,r,o.value),cid:this.targetComponentID(t,i,o)})}pushFileProgress(e,t,i,n=function(){}){this.liveSocket.withinOwners(e.form,(r,o)=>{r.pushWithReply(null,"progress",{event:e.getAttribute(r.binding(Ta)),ref:e.getAttribute(tt),entry_ref:t,progress:i,cid:r.targetComponentID(e.form,o)},n)})}pushInput(e,t,i,n,r,o){let l,d=Ve(i)?i:this.targetComponentID(e.form,t),h=()=>this.putRef([e,e.form],"change",r),m;e.getAttribute(this.binding("change"))?m=gi(e.form,{_target:r._target},[e.name]):m=gi(e.form,{_target:r._target}),w.isUploadInput(e)&&e.files&&e.files.length>0&&ae.trackFiles(e,Array.from(e.files)),l=ae.serializeUploads(e);let v={type:"form",event:n,value:m,uploads:l,cid:d};this.pushWithReply(h,"event",v,S=>{if(w.showError(e,this.liveSocket.binding(_i)),w.isUploadInput(e)&&e.getAttribute("data-phx-auto-upload")!==null){if(ae.filesAwaitingPreflight(e).length>0){let[y,E]=h();this.uploadFiles(e.form,t,y,d,P=>{o&&o(S),this.triggerAwaitingSubmit(e.form)})}}else o&&o(S)})}triggerAwaitingSubmit(e){let t=this.getScheduledSubmit(e);if(t){let[i,n,r,o]=t;this.cancelSubmit(e),o()}}getScheduledSubmit(e){return this.formSubmits.find(([t,i,n,r])=>t.isSameNode(e))}scheduleSubmit(e,t,i,n){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,t,i,n])}cancelSubmit(e){this.formSubmits=this.formSubmits.filter(([t,i,n])=>t.isSameNode(e)?(this.undoRefs(i),!1):!0)}disableForm(e,t={}){let i=v=>!(Ft(v,`${this.binding(yi)}=ignore`,v.form)||Ft(v,"data-phx-update=ignore",v.form)),n=v=>v.hasAttribute(this.binding(un)),r=v=>v.tagName=="BUTTON",o=v=>["INPUT","TEXTAREA","SELECT"].includes(v.tagName),l=Array.from(e.elements),d=l.filter(n),h=l.filter(r).filter(i),m=l.filter(o).filter(i);return h.forEach(v=>{v.setAttribute(oi,v.disabled),v.disabled=!0}),m.forEach(v=>{v.setAttribute(Zi,v.readOnly),v.readOnly=!0,v.files&&(v.setAttribute(oi,v.disabled),v.disabled=!0)}),e.setAttribute(this.binding(Er),""),this.putRef([e].concat(d).concat(h).concat(m),"submit",t)}pushFormSubmit(e,t,i,n,r,o){let l=()=>this.disableForm(e,r),d=this.targetComponentID(e,t);if(ae.hasUploadsInProgress(e)){let[h,m]=l(),v=()=>this.pushFormSubmit(e,n,t,i,r,o);return this.scheduleSubmit(e,h,r,v)}else if(ae.inputsAwaitingPreflight(e).length>0){let[h,m]=l(),v=()=>[h,m,r];this.uploadFiles(e,t,h,d,S=>{let y=gi(e,{submitter:n});this.pushWithReply(v,"event",{type:"form",event:i,value:y,cid:d},o)})}else{let h=gi(e,{submitter:n});this.pushWithReply(l,"event",{type:"form",event:i,value:h,cid:d},o)}}uploadFiles(e,t,i,n,r){let o=this.joinCount,l=ae.activeFileInputs(e),d=l.length;l.forEach(h=>{let m=new ae(h,this,()=>{d--,d===0&&r()});this.uploaders[h]=m;let v=m.entries().map(y=>y.toPreflightPayload()),S={ref:h.getAttribute(tt),entries:v,cid:this.targetComponentID(h.form,t)};this.log("upload",()=>["sending preflight request",S]),this.pushWithReply(null,"allow_upload",S,y=>{if(this.log("upload",()=>["got preflight response",y]),y.error){this.undoRefs(i);let[E,P]=y.error;this.log("upload",()=>[`error for entry ${E}`,P])}else{let E=P=>{this.channel.onError(()=>{this.joinCount===o&&P()})};m.initAdapterUpload(y,E,this.liveSocket)}})})}dispatchUploads(e,t){let i=w.findUploadInputs(this.el).filter(n=>n.name===e);i.length===0?le(`no live file inputs found matching the name "${e}"`):i.length>1?le(`duplicate live file inputs found matching the name "${e}"`):w.dispatchEvent(i[0],Jr,{detail:{files:t}})}pushFormRecovery(e,t,i){this.liveSocket.withinOwners(e,(n,r)=>{let o=Array.from(e.elements).find(d=>w.isFormInput(d)&&d.type!=="hidden"&&!d.hasAttribute(this.binding("change"))),l=e.getAttribute(this.binding(Tr))||e.getAttribute(this.binding("change"));Ie.exec("change",l,n,o,["push",{_target:o.name,newCid:t,callback:i}])})}pushLinkPatch(e,t,i){let n=this.liveSocket.setPendingLink(e),r=t?()=>this.putRef([t],"click"):null,o=()=>this.liveSocket.redirect(window.location.href),l=this.pushWithReply(r,"live_patch",{url:e},d=>{this.liveSocket.requestDOMUpdate(()=>{d.link_redirect?this.liveSocket.replaceMain(e,null,i,n):(this.liveSocket.commitPendingLink(n)&&(this.href=e),this.applyPendingUpdates(),i&&i(n))})});l?l.receive("timeout",o):o()}formsForRecovery(e){if(this.joinCount===0)return[];let t=this.binding("change"),i=document.createElement("template");return i.innerHTML=e,w.all(this.el,`form[${t}]`).filter(n=>n.id&&this.ownsElement(n)).filter(n=>n.elements.length>0).filter(n=>n.getAttribute(this.binding(Tr))!=="ignore").map(n=>{let r=i.content.querySelector(`form[id="${n.id}"][${t}="${n.getAttribute(t)}"]`);return r?[n,r,this.targetComponentID(r)]:[n,null,null]}).filter(([n,r,o])=>r)}maybePushComponentsDestroyed(e){let t=e.filter(i=>w.findComponentNodeList(this.el,i).length===0);t.length>0&&(this.pruningCIDs.push(...t),this.pushWithReply(null,"cids_will_destroy",{cids:t},()=>{this.pruningCIDs=this.pruningCIDs.filter(n=>t.indexOf(n)!==-1);let i=t.filter(n=>w.findComponentNodeList(this.el,n).length===0);i.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:i},n=>{this.rendered.pruneCIDs(n.cids)})}))}ownsElement(e){let t=e.closest(gt);return e.getAttribute(ft)===this.id||t&&t.id===this.id||!t&&this.isDead}submitForm(e,t,i,n,r={}){w.putPrivate(e,bi,!0);let o=this.liveSocket.binding(_i),l=Array.from(e.elements);l.forEach(d=>w.putPrivate(d,bi,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,t,i,n,r,()=>{l.forEach(d=>w.showError(d,o)),this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}},Xr=class{constructor(e,t,i={}){if(this.unloaded=!1,!t||t.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new t(e,i),this.bindingPrefix=i.bindingPrefix||Pa,this.opts=i,this.params=rn(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(vi(Da),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=vi(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||La,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||va,this.reloadJitterMin=i.reloadJitterMin||_a,this.reloadJitterMax=i.reloadJitterMax||ba,this.failsafeJitter=i.failsafeJitter||ya,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.domCallbacks=Object.assign({onNodeAdded:rn(),onBeforeElUpdated:rn()},i.dom||{}),this.transitions=new ll,window.addEventListener("pagehide",n=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}isProfileEnabled(){return this.sessionStorage.getItem(tn)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(li)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(li)==="false"}enableDebug(){this.sessionStorage.setItem(li,"true")}enableProfiling(){this.sessionStorage.setItem(tn,"true")}disableDebug(){this.sessionStorage.setItem(li,"false")}disableProfiling(){this.sessionStorage.removeItem(tn)}enableLatencySim(e){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(nn,e)}disableLatencySim(){this.sessionStorage.removeItem(nn)}getLatencySim(){let e=this.sessionStorage.getItem(nn);return e?parseInt(e):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let e=()=>{this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?e():document.addEventListener("DOMContentLoaded",()=>e())}disconnect(e){clearTimeout(this.reloadWithJitterTimer),this.socket.disconnect(e)}replaceTransport(e){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(e),this.connect()}execJS(e,t,i=null){this.owner(e,n=>Ie.exec(i,t,n,e))}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(e,t){this.domCallbacks[e](...t)}time(e,t){if(!this.isProfileEnabled()||!console.time)return t();console.time(e);let i=t();return console.timeEnd(e),i}log(e,t,i){if(this.viewLogger){let[n,r]=i();this.viewLogger(e,t,n,r)}else if(this.isDebugEnabled()){let[n,r]=i();Ha(e,t,n,r)}}requestDOMUpdate(e){this.transitions.after(e)}transition(e,t,i=function(){}){this.transitions.addTransition(e,t,i)}onChannel(e,t,i){e.on(t,n=>{let r=this.getLatencySim();r?setTimeout(()=>i(n),r):i(n)})}wrapPush(e,t,i){let n=this.getLatencySim(),r=e.joinCount;if(!n)return this.isConnected()&&t.timeout?i().receive("timeout",()=>{e.joinCount===r&&!e.isDestroyed()&&this.reloadWithJitter(e,()=>{this.log(e,"timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}):i();let o={receives:[],receive(l,d){this.receives.push([l,d])}};return setTimeout(()=>{e.isDestroyed()||o.receives.reduce((l,[d,h])=>l.receive(d,h),i())},n),o}reloadWithJitter(e,t){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,n=this.reloadJitterMax,r=Math.floor(Math.random()*(n-i+1))+i,o=Le.updateLocal(this.localStorage,window.location.pathname,qr,0,l=>l+1);o>this.maxReloads&&(r=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{e.isDestroyed()||e.isConnected()||(e.destroy(),t?t():this.log(e,"join",()=>[`encountered ${o} consecutive reloads`]),o>this.maxReloads&&this.log(e,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},r)}getHookCallbacks(e){return e&&e.startsWith("Phoenix.")?Ja[e.split(".")[1]]:this.hooks[e]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(e){return`${this.getBindingPrefix()}${e}`}channel(e,t){return this.socket.channel(e,t)}joinDeadView(){let e=document.body;if(e&&!this.isPhxView(e)&&!this.isPhxView(document.firstElementChild)){let t=this.newRootView(e);t.setHref(this.getHref()),t.joinDead(),this.main||(this.main=t),window.requestAnimationFrame(()=>t.execNewMounted())}}joinRootViews(){let e=!1;return w.all(document,`${gt}:not([${ft}])`,t=>{if(!this.getRootById(t.id)){let i=this.newRootView(t);i.setHref(this.getHref()),i.join(),t.hasAttribute(hn)&&(this.main=i)}e=!0}),e}redirect(e,t){this.unload(),Le.redirect(e,t)}replaceMain(e,t,i=null,n=this.setPendingLink(e)){let r=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let o=w.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(o,t,r),this.main.setRedirect(e),this.transitionRemoves(),this.main.join((l,d)=>{l===1&&this.commitPendingLink(n)&&this.requestDOMUpdate(()=>{w.findPhxSticky(document).forEach(h=>o.appendChild(h)),this.outgoingMainEl.replaceWith(o),this.outgoingMainEl=null,i&&requestAnimationFrame(i),d()})})}transitionRemoves(e){let t=this.binding("remove");e=e||w.all(document,`[${t}]`),e.forEach(i=>{document.body.contains(i)&&this.execJS(i,i.getAttribute(t),"remove")})}isPhxView(e){return e.getAttribute&&e.getAttribute(Ke)!==null}newRootView(e,t,i){let n=new Wr(e,this,null,t,i);return this.roots[n.id]=n,n}owner(e,t){let i=Je(e.closest(gt),n=>this.getViewByEl(n))||this.main;i&&t(i)}withinOwners(e,t){this.owner(e,i=>t(i,e))}getViewByEl(e){let t=e.getAttribute(Mt);return Je(this.getRootById(t),i=>i.getDescendentByEl(e))}getRootById(e){return this.roots[e]}destroyAllViews(){for(let e in this.roots)this.roots[e].destroy(),delete this.roots[e];this.main=null}destroyViewByEl(e){let t=this.getRootById(e.getAttribute(Mt));t&&t.id===e.id?(t.destroy(),delete this.roots[t.id]):t&&t.destroyDescendent(e.id)}setActiveElement(e){if(this.activeElement===e)return;this.activeElement=e;let t=()=>{e===this.activeElement&&(this.activeElement=null),e.removeEventListener("mouseup",this),e.removeEventListener("touchend",this)};e.addEventListener("mouseup",t),e.addEventListener("touchend",t)}getActiveElement(){return document.activeElement===document.body?this.activeElement||document.activeElement:document.activeElement||document.body}dropActiveElement(e){this.prevActive&&e.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:e}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.socket.onClose(t=>{if(t&&t.code===1001)return this.unload();if(t&&t.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",t=>{t.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),e||this.bindNav(),this.bindClicks(),e||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(t,i,n,r,o,l)=>{let d=r.getAttribute(this.binding(Oa)),h=t.key&&t.key.toLowerCase();if(d&&d.toLowerCase()!==h)return;let m=Fe({key:t.key},this.eventMeta(i,t,r));Ie.exec(i,o,n,r,["push",{data:m}])}),this.bind({blur:"focusout",focus:"focusin"},(t,i,n,r,o,l)=>{if(!l){let d=Fe({key:t.key},this.eventMeta(i,t,r));Ie.exec(i,o,n,r,["push",{data:d}])}}),this.bind({blur:"blur",focus:"focus"},(t,i,n,r,o,l,d)=>{if(d==="window"){let h=this.eventMeta(i,t,r);Ie.exec(i,l,n,r,["push",{data:h}])}}),window.addEventListener("dragover",t=>t.preventDefault()),window.addEventListener("drop",t=>{t.preventDefault();let i=Je(Ft(t.target,this.binding(xr)),o=>o.getAttribute(this.binding(xr))),n=i&&document.getElementById(i),r=Array.from(t.dataTransfer.files||[]);!n||n.disabled||r.length===0||!(n.files instanceof FileList)||(ae.trackFiles(n,r,t.dataTransfer),n.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(Jr,t=>{let i=t.target;if(!w.isUploadInput(i))return;let n=Array.from(t.detail.files||[]).filter(r=>r instanceof File||r instanceof Blob);ae.trackFiles(i,n),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(e,t,i){let n=this.metadataCallbacks[e];return n?n(t,i):{}}setPendingLink(e){return this.linkRef++,this.pendingLink=e,this.linkRef}commitPendingLink(e){return this.linkRef!==e?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(e,t){for(let i in e){let n=e[i];this.on(n,r=>{let o=this.binding(i),l=this.binding(`window-${i}`),d=r.target.getAttribute&&r.target.getAttribute(o);d?this.debounce(r.target,r,n,()=>{this.withinOwners(r.target,h=>{t(r,i,h,r.target,d,null)})}):w.all(document,`[${l}]`,h=>{let m=h.getAttribute(l);this.debounce(h,r,n,()=>{this.withinOwners(h,v=>{t(r,i,v,h,m,"window")})})})})}}bindClicks(){window.addEventListener("click",e=>this.clickStartedAtTarget=e.target),this.bindClick("click","click",!1),this.bindClick("mousedown","capture-click",!0)}bindClick(e,t,i){let n=this.binding(t);window.addEventListener(e,r=>{let o=null;if(i)o=r.target.matches(`[${n}]`)?r.target:r.target.querySelector(`[${n}]`);else{let d=this.clickStartedAtTarget||r.target;o=Ft(d,n),this.dispatchClickAway(r,d),this.clickStartedAtTarget=null}let l=o&&o.getAttribute(n);if(!l){let d=r.target instanceof HTMLAnchorElement?r.target.getAttribute("href"):null;!i&&d!==null&&!w.wantsNewTab(r)&&w.isNewPageHref(d,window.location)&&this.unload();return}o.getAttribute("href")==="#"&&r.preventDefault(),this.debounce(o,r,"click",()=>{this.withinOwners(o,d=>{Ie.exec("click",l,d,o,["push",{data:this.eventMeta("click",r,o)}])})})},i)}dispatchClickAway(e,t){let i=this.binding("click-away");w.all(document,`[${i}]`,n=>{n.isSameNode(t)||n.contains(t)||this.withinOwners(e.target,r=>{let o=n.getAttribute(i);Ie.isVisible(n)&&Ie.exec("click",o,r,n,["push",{data:this.eventMeta("click",e,e.target)}])})})}bindNav(){if(!Le.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let e=null;window.addEventListener("scroll",t=>{clearTimeout(e),e=setTimeout(()=>{Le.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",t=>{if(!this.registerNewLocation(window.location))return;let{type:i,id:n,root:r,scroll:o}=t.state||{},l=window.location.href;this.requestDOMUpdate(()=>{this.main.isConnected()&&i==="patch"&&n===this.main.id?this.main.pushLinkPatch(l,null,()=>{this.maybeScroll(o)}):this.replaceMain(l,null,()=>{r&&this.replaceRootHistory(),this.maybeScroll(o)})})},!1),window.addEventListener("click",t=>{let i=Ft(t.target,Gi),n=i&&i.getAttribute(Gi);if(!n||!this.isConnected()||!this.main||w.wantsNewTab(t))return;let r=i.href,o=i.getAttribute(Sa);t.preventDefault(),t.stopImmediatePropagation(),this.pendingLink!==r&&this.requestDOMUpdate(()=>{if(n==="patch")this.pushHistoryPatch(r,o,i);else if(n==="redirect")this.historyRedirect(r,o);else throw new Error(`expected ${Gi} to be "patch" or "redirect", got: ${n}`);let l=i.getAttribute(this.binding("click"));l&&this.requestDOMUpdate(()=>this.execJS(i,l,"click"))})},!1)}maybeScroll(e){typeof e=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,e)})}dispatchEvent(e,t={}){w.dispatchEvent(window,`phx:${e}`,{detail:t})}dispatchEvents(e){e.forEach(([t,i])=>this.dispatchEvent(t,i))}withPageLoading(e,t){w.dispatchEvent(window,"phx:page-loading-start",{detail:e});let i=()=>w.dispatchEvent(window,"phx:page-loading-stop",{detail:e});return t?t(i):i}pushHistoryPatch(e,t,i){if(!this.isConnected())return Le.redirect(e);this.withPageLoading({to:e,kind:"patch"},n=>{this.main.pushLinkPatch(e,i,r=>{this.historyPatch(e,t,r),n()})})}historyPatch(e,t,i=this.setPendingLink(e)){!this.commitPendingLink(i)||(Le.pushState(t,{type:"patch",id:this.main.id},e),this.registerNewLocation(window.location))}historyRedirect(e,t,i){if(!this.isConnected())return Le.redirect(e,i);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:r,host:o}=window.location;e=`${r}//${o}${e}`}let n=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},r=>{this.replaceMain(e,i,()=>{Le.pushState(t,{type:"redirect",id:this.main.id,scroll:n},e),this.registerNewLocation(window.location),r()})})}replaceRootHistory(){Le.pushState("replace",{root:!0,type:"patch",id:this.main.id})}registerNewLocation(e){let{pathname:t,search:i}=this.currentLocation;return t+i===e.pathname+e.search?!1:(this.currentLocation=vi(e),!0)}bindForms(){let e=0,t=!1;this.on("submit",i=>{let n=i.target.getAttribute(this.binding("submit")),r=i.target.getAttribute(this.binding("change"));!t&&r&&!n&&(t=!0,i.preventDefault(),this.withinOwners(i.target,o=>{o.disableForm(i.target),window.requestAnimationFrame(()=>{w.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))},!0),this.on("submit",i=>{let n=i.target.getAttribute(this.binding("submit"));if(!n){w.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,r=>{Ie.exec("submit",n,r,i.target,["push",{submitter:i.submitter}])})},!1);for(let i of["change","input"])this.on(i,n=>{let r=this.binding("change"),o=n.target,l=o.getAttribute(r),d=o.form&&o.form.getAttribute(r),h=l||d;if(!h||o.type==="number"&&o.validity&&o.validity.badInput)return;let m=l?o:o.form,v=e;e++;let{at:S,type:y}=w.private(o,"prev-iteration")||{};S===v-1&&i!==y||(w.putPrivate(o,"prev-iteration",{at:v,type:i}),this.debounce(o,n,i,()=>{this.withinOwners(m,E=>{w.putPrivate(o,cn,!0),w.isTextualInput(o)||this.setActiveElement(o),Ie.exec("change",h,E,o,["push",{_target:n.target.name,dispatcher:m}])})}))},!1);this.on("reset",i=>{let n=i.target;w.resetForm(n,this.binding(_i));let r=Array.from(n.elements).find(o=>o.type==="reset");window.requestAnimationFrame(()=>{r.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(e,t,i,n){if(i==="blur"||i==="focusout")return n();let r=this.binding(Ca),o=this.binding(ka),l=this.defaults.debounce.toString(),d=this.defaults.throttle.toString();this.withinOwners(e,h=>{let m=()=>!h.isDestroyed()&&document.body.contains(e);w.debounce(e,t,r,l,o,d,m,()=>{n()})})}silenceEvents(e){this.silenced=!0,e(),this.silenced=!1}on(e,t){window.addEventListener(e,i=>{this.silenced||t(i)})}},ll=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(e=>{clearTimeout(e),this.transitions.delete(e)}),this.flushPendingOps()}after(e){this.size()===0?e():this.pushPendingOp(e)}addTransition(e,t,i){t();let n=setTimeout(()=>{this.transitions.delete(n),i(),this.flushPendingOps()},e);this.transitions.add(n)}pushPendingOp(e){this.pendingOps.push(e)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let e=this.pendingOps.shift();e&&(e(),this.flushPendingOps())}};var Ui=yr(Yr());var _n=!1,bn=!1,rt=[],yn=-1;function cl(e){ul(e)}function ul(e){rt.includes(e)||rt.push(e),dl()}function hs(e){let t=rt.indexOf(e);t!==-1&&t>yn&&rt.splice(t,1)}function dl(){!bn&&!_n&&(_n=!0,queueMicrotask(hl))}function hl(){_n=!1,bn=!0;for(let e=0;e<rt.length;e++)rt[e](),yn=e;rt.length=0,yn=-1,bn=!1}var _t,lt,bt,fs,wn=!0;function fl(e){wn=!1,e(),wn=!0}function pl(e){_t=e.reactive,bt=e.release,lt=t=>e.effect(t,{scheduler:i=>{wn?cl(i):i()}}),fs=e.raw}function Qr(e){lt=e}function gl(e){let t=()=>{};return[n=>{let r=lt(n);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(o=>o())}),e._x_effects.add(r),t=()=>{r!==void 0&&(e._x_effects.delete(r),bt(r))},r},()=>{t()}]}function ps(e,t){let i=!0,n,r=lt(()=>{let o=e();JSON.stringify(o),i?n=o:queueMicrotask(()=>{t(o,n),n=o}),i=!1});return()=>bt(r)}var gs=[],ms=[],vs=[];function ml(e){vs.push(e)}function Fn(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,ms.push(t))}function _s(e){gs.push(e)}function bs(e,t,i){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(i)}function ys(e,t){!e._x_attributeCleanups||Object.entries(e._x_attributeCleanups).forEach(([i,n])=>{(t===void 0||t.includes(i))&&(n.forEach(r=>r()),delete e._x_attributeCleanups[i])})}function vl(e){if(e._x_cleanups)for(;e._x_cleanups.length;)e._x_cleanups.pop()()}var Mn=new MutationObserver(jn),Nn=!1;function $n(){Mn.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Nn=!0}function ws(){_l(),Mn.disconnect(),Nn=!1}var Nt=[];function _l(){let e=Mn.takeRecords();Nt.push(()=>e.length>0&&jn(e));let t=Nt.length;queueMicrotask(()=>{if(Nt.length===t)for(;Nt.length>0;)Nt.shift()()})}function ne(e){if(!Nn)return e();ws();let t=e();return $n(),t}var Hn=!1,Ti=[];function bl(){Hn=!0}function yl(){Hn=!1,jn(Ti),Ti=[]}function jn(e){if(Hn){Ti=Ti.concat(e);return}let t=new Set,i=new Set,n=new Map,r=new Map;for(let o=0;o<e.length;o++)if(!e[o].target._x_ignoreMutationObserver&&(e[o].type==="childList"&&(e[o].addedNodes.forEach(l=>l.nodeType===1&&t.add(l)),e[o].removedNodes.forEach(l=>l.nodeType===1&&i.add(l))),e[o].type==="attributes")){let l=e[o].target,d=e[o].attributeName,h=e[o].oldValue,m=()=>{n.has(l)||n.set(l,[]),n.get(l).push({name:d,value:l.getAttribute(d)})},v=()=>{r.has(l)||r.set(l,[]),r.get(l).push(d)};l.hasAttribute(d)&&h===null?m():l.hasAttribute(d)?(v(),m()):v()}r.forEach((o,l)=>{ys(l,o)}),n.forEach((o,l)=>{gs.forEach(d=>d(l,o))});for(let o of i)t.has(o)||ms.forEach(l=>l(o));t.forEach(o=>{o._x_ignoreSelf=!0,o._x_ignore=!0});for(let o of t)i.has(o)||!o.isConnected||(delete o._x_ignoreSelf,delete o._x_ignore,vs.forEach(l=>l(o)),o._x_ignore=!0,o._x_ignoreSelf=!0);t.forEach(o=>{delete o._x_ignoreSelf,delete o._x_ignore}),t=null,i=null,n=null,r=null}function Ss(e){return Jt(mt(e))}function Vt(e,t,i){return e._x_dataStack=[t,...mt(i||e)],()=>{e._x_dataStack=e._x_dataStack.filter(n=>n!==t)}}function mt(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?mt(e.host):e.parentNode?mt(e.parentNode):[]}function Jt(e){return new Proxy({objects:e},wl)}var wl={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(i=>Object.prototype.hasOwnProperty.call(i,t)||Reflect.has(i,t))},get({objects:e},t,i){return t=="toJSON"?Sl:Reflect.get(e.find(n=>Reflect.has(n,t))||{},t,i)},set({objects:e},t,i,n){let r=e.find(l=>Object.prototype.hasOwnProperty.call(l,t))||e[e.length-1],o=Object.getOwnPropertyDescriptor(r,t);return(o==null?void 0:o.set)&&(o==null?void 0:o.get)?o.set.call(n,i)||!0:Reflect.set(r,t,i)}};function Sl(){return Reflect.ownKeys(this).reduce((t,i)=>(t[i]=Reflect.get(this,i),t),{})}function xs(e){let t=n=>typeof n=="object"&&!Array.isArray(n)&&n!==null,i=(n,r="")=>{Object.entries(Object.getOwnPropertyDescriptors(n)).forEach(([o,{value:l,enumerable:d}])=>{if(d===!1||l===void 0||typeof l=="object"&&l!==null&&l.__v_skip)return;let h=r===""?o:`${r}.${o}`;typeof l=="object"&&l!==null&&l._x_interceptor?n[o]=l.initialize(e,h,o):t(l)&&l!==n&&!(l instanceof Element)&&i(l,h)})};return i(e)}function As(e,t=()=>{}){let i={initialValue:void 0,_x_interceptor:!0,initialize(n,r,o){return e(this.initialValue,()=>xl(n,r),l=>Sn(n,r,l),r,o)}};return t(i),n=>{if(typeof n=="object"&&n!==null&&n._x_interceptor){let r=i.initialize.bind(i);i.initialize=(o,l,d)=>{let h=n.initialize(o,l,d);return i.initialValue=h,r(o,l,d)}}else i.initialValue=n;return i}}function xl(e,t){return t.split(".").reduce((i,n)=>i[n],e)}function Sn(e,t,i){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=i;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Sn(e[t[0]],t.slice(1),i)}}var Es={};function Ae(e,t){Es[e]=t}function xn(e,t){return Object.entries(Es).forEach(([i,n])=>{let r=null;function o(){if(r)return r;{let[l,d]=Is(t);return r=Fe({interceptor:As},l),Fn(t,d),r}}Object.defineProperty(e,`$${i}`,{get(){return n(t,o())},enumerable:!1})}),e}function Al(e,t,i,...n){try{return i(...n)}catch(r){qt(r,e,t)}}function qt(e,t,i=void 0){e=Object.assign(e!=null?e:{message:"No error message given."},{el:t,expression:i}),console.warn(`Alpine Expression Error: ${e.message}

${i?'Expression: "'+i+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var ki=!0;function Cs(e){let t=ki;ki=!1;let i=e();return ki=t,i}function st(e,t,i={}){let n;return ue(e,t)(r=>n=r,i),n}function ue(...e){return ks(...e)}var ks=Os;function El(e){ks=e}function Os(e,t){let i={};xn(i,e);let n=[i,...mt(e)],r=typeof t=="function"?Cl(n,t):Ol(n,t,e);return Al.bind(null,e,t,r)}function Cl(e,t){return(i=()=>{},{scope:n={},params:r=[]}={})=>{let o=t.apply(Jt([n,...e]),r);Li(i,o)}}var fn={};function kl(e,t){if(fn[e])return fn[e];let i=Object.getPrototypeOf(async function(){}).constructor,n=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,o=(()=>{try{let l=new i(["__self","scope"],`with (scope) { __self.result = ${n} }; __self.finished = true; return __self.result;`);return Object.defineProperty(l,"name",{value:`[Alpine] ${e}`}),l}catch(l){return qt(l,t,e),Promise.resolve()}})();return fn[e]=o,o}function Ol(e,t,i){let n=kl(t,i);return(r=()=>{},{scope:o={},params:l=[]}={})=>{n.result=void 0,n.finished=!1;let d=Jt([o,...e]);if(typeof n=="function"){let h=n(n,d).catch(m=>qt(m,i,t));n.finished?(Li(r,n.result,d,l,i),n.result=void 0):h.then(m=>{Li(r,m,d,l,i)}).catch(m=>qt(m,i,t)).finally(()=>n.result=void 0)}}}function Li(e,t,i,n,r){if(ki&&typeof t=="function"){let o=t.apply(i,n);o instanceof Promise?o.then(l=>Li(e,l,i,n)).catch(l=>qt(l,r,t)):e(o)}else typeof t=="object"&&t instanceof Promise?t.then(o=>e(o)):e(t)}var Un="x-";function yt(e=""){return Un+e}function Tl(e){Un=e}var Ii={};function ie(e,t){return Ii[e]=t,{before(i){if(!Ii[i]){console.warn(String.raw`Cannot find directive \`${i}\`. \`${e}\` will use the default order of execution`);return}let n=nt.indexOf(i);nt.splice(n>=0?n:nt.indexOf("DEFAULT"),0,e)}}}function Ll(e){return Object.keys(Ii).includes(e)}function Bn(e,t,i){if(t=Array.from(t),e._x_virtualDirectives){let o=Object.entries(e._x_virtualDirectives).map(([d,h])=>({name:d,value:h})),l=Ts(o);o=o.map(d=>l.find(h=>h.name===d.name)?{name:`x-bind:${d.name}`,value:`"${d.value}"`}:d),t=t.concat(o)}let n={};return t.map(Ds((o,l)=>n[o]=l)).filter(Ms).map(Rl(n,i)).sort(Dl).map(o=>Pl(e,o))}function Ts(e){return Array.from(e).map(Ds()).filter(t=>!Ms(t))}var An=!1,jt=new Map,Ls=Symbol();function Il(e){An=!0;let t=Symbol();Ls=t,jt.set(t,[]);let i=()=>{for(;jt.get(t).length;)jt.get(t).shift()();jt.delete(t)},n=()=>{An=!1,i()};e(i),n()}function Is(e){let t=[],i=d=>t.push(d),[n,r]=gl(e);return t.push(r),[{Alpine:zt,effect:n,cleanup:i,evaluateLater:ue.bind(ue,e),evaluate:st.bind(st,e)},()=>t.forEach(d=>d())]}function Pl(e,t){let i=()=>{},n=Ii[t.type]||i,[r,o]=Is(e);bs(e,t.original,o);let l=()=>{e._x_ignore||e._x_ignoreSelf||(n.inline&&n.inline(e,t,r),n=n.bind(n,e,t,r),An?jt.get(Ls).push(n):n())};return l.runCleanups=o,l}var Ps=(e,t)=>({name:i,value:n})=>(i.startsWith(e)&&(i=i.replace(e,t)),{name:i,value:n}),Rs=e=>e;function Ds(e=()=>{}){return({name:t,value:i})=>{let{name:n,value:r}=Fs.reduce((o,l)=>l(o),{name:t,value:i});return n!==t&&e(n,t),{name:n,value:r}}}var Fs=[];function qn(e){Fs.push(e)}function Ms({name:e}){return Ns().test(e)}var Ns=()=>new RegExp(`^${Un}([^:^.]+)\\b`);function Rl(e,t){return({name:i,value:n})=>{let r=i.match(Ns()),o=i.match(/:([a-zA-Z0-9\-_:]+)/),l=i.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],d=t||e[i]||i;return{type:r?r[1]:null,value:o?o[1]:null,modifiers:l.map(h=>h.replace(".","")),expression:n,original:d}}}var En="DEFAULT",nt=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",En,"teleport"];function Dl(e,t){let i=nt.indexOf(e.type)===-1?En:e.type,n=nt.indexOf(t.type)===-1?En:t.type;return nt.indexOf(i)-nt.indexOf(n)}function Ut(e,t,i={}){e.dispatchEvent(new CustomEvent(t,{detail:i,bubbles:!0,composed:!0,cancelable:!0}))}function We(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(r=>We(r,t));return}let i=!1;if(t(e,()=>i=!0),i)return;let n=e.firstElementChild;for(;n;)We(n,t,!1),n=n.nextElementSibling}function ye(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var Zr=!1;function Fl(){Zr&&ye("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Zr=!0,document.body||ye("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Ut(document,"alpine:init"),Ut(document,"alpine:initializing"),$n(),ml(t=>Ne(t,We)),Fn(t=>Vs(t)),_s((t,i)=>{Bn(t,i).forEach(n=>n())});let e=t=>!Ri(t.parentElement,!0);Array.from(document.querySelectorAll(js().join(","))).filter(e).forEach(t=>{Ne(t)}),Ut(document,"alpine:initialized"),setTimeout(()=>{$l()})}var Vn=[],$s=[];function Hs(){return Vn.map(e=>e())}function js(){return Vn.concat($s).map(e=>e())}function Us(e){Vn.push(e)}function Bs(e){$s.push(e)}function Ri(e,t=!1){return Kt(e,i=>{if((t?js():Hs()).some(r=>i.matches(r)))return!0})}function Kt(e,t){if(!!e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return Kt(e.parentElement,t)}}function Ml(e){return Hs().some(t=>e.matches(t))}var qs=[];function Nl(e){qs.push(e)}function Ne(e,t=We,i=()=>{}){Il(()=>{t(e,(n,r)=>{i(n,r),qs.forEach(o=>o(n,r)),Bn(n,n.attributes).forEach(o=>o()),n._x_ignore&&r()})})}function Vs(e,t=We){t(e,i=>{ys(i),vl(i)})}function $l(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,i,n])=>{Ll(i)||n.some(r=>{if(document.querySelector(r))return ye(`found "${r}", but missing ${t} plugin`),!0})})}var Cn=[],Jn=!1;function Kn(e=()=>{}){return queueMicrotask(()=>{Jn||setTimeout(()=>{kn()})}),new Promise(t=>{Cn.push(()=>{e(),t()})})}function kn(){for(Jn=!1;Cn.length;)Cn.shift()()}function Hl(){Jn=!0}function zn(e,t){return Array.isArray(t)?es(e,t.join(" ")):typeof t=="object"&&t!==null?jl(e,t):typeof t=="function"?zn(e,t()):es(e,t)}function es(e,t){let i=o=>o.split(" ").filter(Boolean),n=o=>o.split(" ").filter(l=>!e.classList.contains(l)).filter(Boolean),r=o=>(e.classList.add(...o),()=>{e.classList.remove(...o)});return t=t===!0?t="":t||"",r(n(t))}function jl(e,t){let i=d=>d.split(" ").filter(Boolean),n=Object.entries(t).flatMap(([d,h])=>h?i(d):!1).filter(Boolean),r=Object.entries(t).flatMap(([d,h])=>h?!1:i(d)).filter(Boolean),o=[],l=[];return r.forEach(d=>{e.classList.contains(d)&&(e.classList.remove(d),l.push(d))}),n.forEach(d=>{e.classList.contains(d)||(e.classList.add(d),o.push(d))}),()=>{l.forEach(d=>e.classList.add(d)),o.forEach(d=>e.classList.remove(d))}}function Di(e,t){return typeof t=="object"&&t!==null?Ul(e,t):Bl(e,t)}function Ul(e,t){let i={};return Object.entries(t).forEach(([n,r])=>{i[n]=e.style[n],n.startsWith("--")||(n=ql(n)),e.style.setProperty(n,r)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{Di(e,i)}}function Bl(e,t){let i=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",i||"")}}function ql(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function On(e,t=()=>{}){let i=!1;return function(){i?t.apply(this,arguments):(i=!0,e.apply(this,arguments))}}ie("transition",(e,{value:t,modifiers:i,expression:n},{evaluate:r})=>{typeof n=="function"&&(n=r(n)),n!==!1&&(!n||typeof n=="boolean"?Jl(e,i,t):Vl(e,n,t))});function Vl(e,t,i){Js(e,zn,""),{enter:r=>{e._x_transition.enter.during=r},"enter-start":r=>{e._x_transition.enter.start=r},"enter-end":r=>{e._x_transition.enter.end=r},leave:r=>{e._x_transition.leave.during=r},"leave-start":r=>{e._x_transition.leave.start=r},"leave-end":r=>{e._x_transition.leave.end=r}}[i](t)}function Jl(e,t,i){Js(e,Di);let n=!t.includes("in")&&!t.includes("out")&&!i,r=n||t.includes("in")||["enter"].includes(i),o=n||t.includes("out")||["leave"].includes(i);t.includes("in")&&!n&&(t=t.filter((b,O)=>O<t.indexOf("out"))),t.includes("out")&&!n&&(t=t.filter((b,O)=>O>t.indexOf("out")));let l=!t.includes("opacity")&&!t.includes("scale"),d=l||t.includes("opacity"),h=l||t.includes("scale"),m=d?0:1,v=h?$t(t,"scale",95)/100:1,S=$t(t,"delay",0)/1e3,y=$t(t,"origin","center"),E="opacity, transform",P=$t(t,"duration",150)/1e3,D=$t(t,"duration",75)/1e3,T="cubic-bezier(0.4, 0.0, 0.2, 1)";r&&(e._x_transition.enter.during={transformOrigin:y,transitionDelay:`${S}s`,transitionProperty:E,transitionDuration:`${P}s`,transitionTimingFunction:T},e._x_transition.enter.start={opacity:m,transform:`scale(${v})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),o&&(e._x_transition.leave.during={transformOrigin:y,transitionDelay:`${S}s`,transitionProperty:E,transitionDuration:`${D}s`,transitionTimingFunction:T},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:m,transform:`scale(${v})`})}function Js(e,t,i={}){e._x_transition||(e._x_transition={enter:{during:i,start:i,end:i},leave:{during:i,start:i,end:i},in(n=()=>{},r=()=>{}){Tn(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},n,r)},out(n=()=>{},r=()=>{}){Tn(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},n,r)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,i,n){let r=document.visibilityState==="visible"?requestAnimationFrame:setTimeout,o=()=>r(i);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(i):o():e._x_transition?e._x_transition.in(i):o();return}e._x_hidePromise=e._x_transition?new Promise((l,d)=>{e._x_transition.out(()=>{},()=>l(n)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>d({isFromCancelledTransition:!0}))}):Promise.resolve(n),queueMicrotask(()=>{let l=Ks(e);l?(l._x_hideChildren||(l._x_hideChildren=[]),l._x_hideChildren.push(e)):r(()=>{let d=h=>{let m=Promise.all([h._x_hidePromise,...(h._x_hideChildren||[]).map(d)]).then(([v])=>v==null?void 0:v());return delete h._x_hidePromise,delete h._x_hideChildren,m};d(e).catch(h=>{if(!h.isFromCancelledTransition)throw h})})})};function Ks(e){let t=e.parentNode;if(!!t)return t._x_hidePromise?t:Ks(t)}function Tn(e,t,{during:i,start:n,end:r}={},o=()=>{},l=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(i).length===0&&Object.keys(n).length===0&&Object.keys(r).length===0){o(),l();return}let d,h,m;Kl(e,{start(){d=t(e,n)},during(){h=t(e,i)},before:o,end(){d(),m=t(e,r)},after:l,cleanup(){h(),m()}})}function Kl(e,t){let i,n,r,o=On(()=>{ne(()=>{i=!0,n||t.before(),r||(t.end(),kn()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(l){this.beforeCancels.push(l)},cancel:On(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();o()}),finish:o},ne(()=>{t.start(),t.during()}),Hl(),requestAnimationFrame(()=>{if(i)return;let l=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,d=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;l===0&&(l=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),ne(()=>{t.before()}),n=!0,requestAnimationFrame(()=>{i||(ne(()=>{t.end()}),kn(),setTimeout(e._x_transitioning.finish,l+d),r=!0)})})}function $t(e,t,i){if(e.indexOf(t)===-1)return i;let n=e[e.indexOf(t)+1];if(!n||t==="scale"&&isNaN(n))return i;if(t==="duration"||t==="delay"){let r=n.match(/([0-9]+)ms/);if(r)return r[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[n,e[e.indexOf(t)+2]].join(" "):n}var Xe=!1;function Ye(e,t=()=>{}){return(...i)=>Xe?t(...i):e(...i)}function zl(e){return(...t)=>Xe&&e(...t)}var zs=[];function Fi(e){zs.push(e)}function Wl(e,t){zs.forEach(i=>i(e,t)),Xe=!0,Ws(()=>{Ne(t,(i,n)=>{n(i,()=>{})})}),Xe=!1}var Ln=!1;function Xl(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),Xe=!0,Ln=!0,Ws(()=>{Gl(t)}),Xe=!1,Ln=!1}function Gl(e){let t=!1;Ne(e,(n,r)=>{We(n,(o,l)=>{if(t&&Ml(o))return l();t=!0,r(o,l)})})}function Ws(e){let t=lt;Qr((i,n)=>{let r=t(i);return bt(r),()=>{}}),e(),Qr(t)}function Xs(e,t,i,n=[]){switch(e._x_bindings||(e._x_bindings=_t({})),e._x_bindings[t]=i,t=n.includes("camel")?rc(t):t,t){case"value":Yl(e,i);break;case"style":Zl(e,i);break;case"class":Ql(e,i);break;case"selected":case"checked":ec(e,t,i);break;default:Gs(e,t,i);break}}function Yl(e,t){if(e.type==="radio")e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=Oi(e.value)===t:e.checked=ts(e.value,t));else if(e.type==="checkbox")Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(i=>ts(i,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")nc(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function Ql(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=zn(e,t)}function Zl(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=Di(e,t)}function ec(e,t,i){Gs(e,t,i),ic(e,t,i)}function Gs(e,t,i){[null,void 0,!1].includes(i)&&sc(t)?e.removeAttribute(t):(Ys(t)&&(i=t),tc(e,t,i))}function tc(e,t,i){e.getAttribute(t)!=i&&e.setAttribute(t,i)}function ic(e,t,i){e[t]!==i&&(e[t]=i)}function nc(e,t){let i=[].concat(t).map(n=>n+"");Array.from(e.options).forEach(n=>{n.selected=i.includes(n.value)})}function rc(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function ts(e,t){return e==t}function Oi(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?Boolean(e):null}function Ys(e){return["disabled","checked","required","readonly","open","selected","autofocus","itemscope","multiple","novalidate","allowfullscreen","allowpaymentrequest","formnovalidate","autoplay","controls","loop","muted","playsinline","default","ismap","reversed","async","defer","nomodule"].includes(e)}function sc(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function oc(e,t,i){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:Qs(e,t,i)}function ac(e,t,i,n=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let r=e._x_inlineBindings[t];return r.extract=n,Cs(()=>st(e,r.expression))}return Qs(e,t,i)}function Qs(e,t,i){let n=e.getAttribute(t);return n===null?typeof i=="function"?i():i:n===""?!0:Ys(t)?!![t,"true"].includes(n):n}function Zs(e,t){var i;return function(){var n=this,r=arguments,o=function(){i=null,e.apply(n,r)};clearTimeout(i),i=setTimeout(o,t)}}function eo(e,t){let i;return function(){let n=this,r=arguments;i||(e.apply(n,r),i=!0,setTimeout(()=>i=!1,t))}}function to({get:e,set:t},{get:i,set:n}){let r=!0,o,l,d=lt(()=>{let h=e(),m=i();if(r)n(pn(h)),r=!1;else{let v=JSON.stringify(h),S=JSON.stringify(m);v!==o?n(pn(h)):v!==S&&t(pn(m))}o=JSON.stringify(e()),l=JSON.stringify(i())});return()=>{bt(d)}}function pn(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function lc(e){(Array.isArray(e)?e:[e]).forEach(i=>i(zt))}var it={},is=!1;function cc(e,t){if(is||(it=_t(it),is=!0),t===void 0)return it[e];it[e]=t,typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&it[e].init(),xs(it[e])}function uc(){return it}var io={};function dc(e,t){let i=typeof t!="function"?()=>t:t;return e instanceof Element?no(e,i()):(io[e]=i,()=>{})}function hc(e){return Object.entries(io).forEach(([t,i])=>{Object.defineProperty(e,t,{get(){return(...n)=>i(...n)}})}),e}function no(e,t,i){let n=[];for(;n.length;)n.pop()();let r=Object.entries(t).map(([l,d])=>({name:l,value:d})),o=Ts(r);return r=r.map(l=>o.find(d=>d.name===l.name)?{name:`x-bind:${l.name}`,value:`"${l.value}"`}:l),Bn(e,r,i).map(l=>{n.push(l.runCleanups),l()}),()=>{for(;n.length;)n.pop()()}}var ro={};function fc(e,t){ro[e]=t}function pc(e,t){return Object.entries(ro).forEach(([i,n])=>{Object.defineProperty(e,i,{get(){return(...r)=>n.bind(t)(...r)},enumerable:!1})}),e}var gc={get reactive(){return _t},get release(){return bt},get effect(){return lt},get raw(){return fs},version:"3.14.1",flushAndStopDeferringMutations:yl,dontAutoEvaluateFunctions:Cs,disableEffectScheduling:fl,startObservingMutations:$n,stopObservingMutations:ws,setReactivityEngine:pl,onAttributeRemoved:bs,onAttributesAdded:_s,closestDataStack:mt,skipDuringClone:Ye,onlyDuringClone:zl,addRootSelector:Us,addInitSelector:Bs,interceptClone:Fi,addScopeToNode:Vt,deferMutations:bl,mapAttributes:qn,evaluateLater:ue,interceptInit:Nl,setEvaluator:El,mergeProxies:Jt,extractProp:ac,findClosest:Kt,onElRemoved:Fn,closestRoot:Ri,destroyTree:Vs,interceptor:As,transition:Tn,setStyles:Di,mutateDom:ne,directive:ie,entangle:to,throttle:eo,debounce:Zs,evaluate:st,initTree:Ne,nextTick:Kn,prefixed:yt,prefix:Tl,plugin:lc,magic:Ae,store:cc,start:Fl,clone:Xl,cloneNode:Wl,bound:oc,$data:Ss,watch:ps,walk:We,data:fc,bind:dc},zt=gc;function so(e,t){let i=Object.create(null),n=e.split(",");for(let r=0;r<n.length;r++)i[n[r]]=!0;return t?r=>!!i[r.toLowerCase()]:r=>!!i[r]}var mc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",wu=so(mc+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected"),vc=Object.freeze({}),Su=Object.freeze([]),_c=Object.prototype.hasOwnProperty,Mi=(e,t)=>_c.call(e,t),ot=Array.isArray,Bt=e=>oo(e)==="[object Map]",bc=e=>typeof e=="string",Wn=e=>typeof e=="symbol",Ni=e=>e!==null&&typeof e=="object",yc=Object.prototype.toString,oo=e=>yc.call(e),ao=e=>oo(e).slice(8,-1),Xn=e=>bc(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,$i=e=>{let t=Object.create(null);return i=>t[i]||(t[i]=e(i))},wc=/-(\w)/g,xu=$i(e=>e.replace(wc,(t,i)=>i?i.toUpperCase():"")),Sc=/\B([A-Z])/g,Au=$i(e=>e.replace(Sc,"-$1").toLowerCase()),lo=$i(e=>e.charAt(0).toUpperCase()+e.slice(1)),Eu=$i(e=>e?`on${lo(e)}`:""),co=(e,t)=>e!==t&&(e===e||t===t),In=new WeakMap,Ht=[],Re,at=Symbol("iterate"),Pn=Symbol("Map key iterate");function xc(e){return e&&e._isEffect===!0}function Ac(e,t=vc){xc(e)&&(e=e.raw);let i=kc(e,t);return t.lazy||i(),i}function Ec(e){e.active&&(uo(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var Cc=0;function kc(e,t){let i=function(){if(!i.active)return e();if(!Ht.includes(i)){uo(i);try{return Tc(),Ht.push(i),Re=i,e()}finally{Ht.pop(),ho(),Re=Ht[Ht.length-1]}}};return i.id=Cc++,i.allowRecurse=!!t.allowRecurse,i._isEffect=!0,i.active=!0,i.raw=e,i.deps=[],i.options=t,i}function uo(e){let{deps:t}=e;if(t.length){for(let i=0;i<t.length;i++)t[i].delete(e);t.length=0}}var vt=!0,Gn=[];function Oc(){Gn.push(vt),vt=!1}function Tc(){Gn.push(vt),vt=!0}function ho(){let e=Gn.pop();vt=e===void 0?!0:e}function xe(e,t,i){if(!vt||Re===void 0)return;let n=In.get(e);n||In.set(e,n=new Map);let r=n.get(i);r||n.set(i,r=new Set),r.has(Re)||(r.add(Re),Re.deps.push(r),Re.options.onTrack&&Re.options.onTrack({effect:Re,target:e,type:t,key:i}))}function Ge(e,t,i,n,r,o){let l=In.get(e);if(!l)return;let d=new Set,h=v=>{v&&v.forEach(S=>{(S!==Re||S.allowRecurse)&&d.add(S)})};if(t==="clear")l.forEach(h);else if(i==="length"&&ot(e))l.forEach((v,S)=>{(S==="length"||S>=n)&&h(v)});else switch(i!==void 0&&h(l.get(i)),t){case"add":ot(e)?Xn(i)&&h(l.get("length")):(h(l.get(at)),Bt(e)&&h(l.get(Pn)));break;case"delete":ot(e)||(h(l.get(at)),Bt(e)&&h(l.get(Pn)));break;case"set":Bt(e)&&h(l.get(at));break}let m=v=>{v.options.onTrigger&&v.options.onTrigger({effect:v,target:e,key:i,type:t,newValue:n,oldValue:r,oldTarget:o}),v.options.scheduler?v.options.scheduler(v):v()};d.forEach(m)}var Lc=so("__proto__,__v_isRef,__isVue"),fo=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(Wn)),Ic=po(),Pc=po(!0),ns=Rc();function Rc(){let e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...i){let n=Z(this);for(let o=0,l=this.length;o<l;o++)xe(n,"get",o+"");let r=n[t](...i);return r===-1||r===!1?n[t](...i.map(Z)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...i){Oc();let n=Z(this)[t].apply(this,i);return ho(),n}}),e}function po(e=!1,t=!1){return function(n,r,o){if(r==="__v_isReactive")return!e;if(r==="__v_isReadonly")return e;if(r==="__v_raw"&&o===(e?t?Xc:_o:t?Wc:vo).get(n))return n;let l=ot(n);if(!e&&l&&Mi(ns,r))return Reflect.get(ns,r,o);let d=Reflect.get(n,r,o);return(Wn(r)?fo.has(r):Lc(r))||(e||xe(n,"get",r),t)?d:Rn(d)?!l||!Xn(r)?d.value:d:Ni(d)?e?bo(d):er(d):d}}var Dc=Fc();function Fc(e=!1){return function(i,n,r,o){let l=i[n];if(!e&&(r=Z(r),l=Z(l),!ot(i)&&Rn(l)&&!Rn(r)))return l.value=r,!0;let d=ot(i)&&Xn(n)?Number(n)<i.length:Mi(i,n),h=Reflect.set(i,n,r,o);return i===Z(o)&&(d?co(r,l)&&Ge(i,"set",n,r,l):Ge(i,"add",n,r)),h}}function Mc(e,t){let i=Mi(e,t),n=e[t],r=Reflect.deleteProperty(e,t);return r&&i&&Ge(e,"delete",t,void 0,n),r}function Nc(e,t){let i=Reflect.has(e,t);return(!Wn(t)||!fo.has(t))&&xe(e,"has",t),i}function $c(e){return xe(e,"iterate",ot(e)?"length":at),Reflect.ownKeys(e)}var Hc={get:Ic,set:Dc,deleteProperty:Mc,has:Nc,ownKeys:$c},jc={get:Pc,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},Yn=e=>Ni(e)?er(e):e,Qn=e=>Ni(e)?bo(e):e,Zn=e=>e,Hi=e=>Reflect.getPrototypeOf(e);function Si(e,t,i=!1,n=!1){e=e.__v_raw;let r=Z(e),o=Z(t);t!==o&&!i&&xe(r,"get",t),!i&&xe(r,"get",o);let{has:l}=Hi(r),d=n?Zn:i?Qn:Yn;if(l.call(r,t))return d(e.get(t));if(l.call(r,o))return d(e.get(o));e!==r&&e.get(t)}function xi(e,t=!1){let i=this.__v_raw,n=Z(i),r=Z(e);return e!==r&&!t&&xe(n,"has",e),!t&&xe(n,"has",r),e===r?i.has(e):i.has(e)||i.has(r)}function Ai(e,t=!1){return e=e.__v_raw,!t&&xe(Z(e),"iterate",at),Reflect.get(e,"size",e)}function rs(e){e=Z(e);let t=Z(this);return Hi(t).has.call(t,e)||(t.add(e),Ge(t,"add",e,e)),this}function ss(e,t){t=Z(t);let i=Z(this),{has:n,get:r}=Hi(i),o=n.call(i,e);o?mo(i,n,e):(e=Z(e),o=n.call(i,e));let l=r.call(i,e);return i.set(e,t),o?co(t,l)&&Ge(i,"set",e,t,l):Ge(i,"add",e,t),this}function os(e){let t=Z(this),{has:i,get:n}=Hi(t),r=i.call(t,e);r?mo(t,i,e):(e=Z(e),r=i.call(t,e));let o=n?n.call(t,e):void 0,l=t.delete(e);return r&&Ge(t,"delete",e,void 0,o),l}function as(){let e=Z(this),t=e.size!==0,i=Bt(e)?new Map(e):new Set(e),n=e.clear();return t&&Ge(e,"clear",void 0,void 0,i),n}function Ei(e,t){return function(n,r){let o=this,l=o.__v_raw,d=Z(l),h=t?Zn:e?Qn:Yn;return!e&&xe(d,"iterate",at),l.forEach((m,v)=>n.call(r,h(m),h(v),o))}}function Ci(e,t,i){return function(...n){let r=this.__v_raw,o=Z(r),l=Bt(o),d=e==="entries"||e===Symbol.iterator&&l,h=e==="keys"&&l,m=r[e](...n),v=i?Zn:t?Qn:Yn;return!t&&xe(o,"iterate",h?Pn:at),{next(){let{value:S,done:y}=m.next();return y?{value:S,done:y}:{value:d?[v(S[0]),v(S[1])]:v(S),done:y}},[Symbol.iterator](){return this}}}}function ze(e){return function(...t){{let i=t[0]?`on key "${t[0]}" `:"";console.warn(`${lo(e)} operation ${i}failed: target is readonly.`,Z(this))}return e==="delete"?!1:this}}function Uc(){let e={get(o){return Si(this,o)},get size(){return Ai(this)},has:xi,add:rs,set:ss,delete:os,clear:as,forEach:Ei(!1,!1)},t={get(o){return Si(this,o,!1,!0)},get size(){return Ai(this)},has:xi,add:rs,set:ss,delete:os,clear:as,forEach:Ei(!1,!0)},i={get(o){return Si(this,o,!0)},get size(){return Ai(this,!0)},has(o){return xi.call(this,o,!0)},add:ze("add"),set:ze("set"),delete:ze("delete"),clear:ze("clear"),forEach:Ei(!0,!1)},n={get(o){return Si(this,o,!0,!0)},get size(){return Ai(this,!0)},has(o){return xi.call(this,o,!0)},add:ze("add"),set:ze("set"),delete:ze("delete"),clear:ze("clear"),forEach:Ei(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=Ci(o,!1,!1),i[o]=Ci(o,!0,!1),t[o]=Ci(o,!1,!0),n[o]=Ci(o,!0,!0)}),[e,i,t,n]}var[Bc,qc,Vc,Jc]=Uc();function go(e,t){let i=t?e?Jc:Vc:e?qc:Bc;return(n,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(Mi(i,r)&&r in n?i:n,r,o)}var Kc={get:go(!1,!1)},zc={get:go(!0,!1)};function mo(e,t,i){let n=Z(i);if(n!==i&&t.call(e,n)){let r=ao(e);console.warn(`Reactive ${r} contains both the raw and reactive versions of the same object${r==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var vo=new WeakMap,Wc=new WeakMap,_o=new WeakMap,Xc=new WeakMap;function Gc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Yc(e){return e.__v_skip||!Object.isExtensible(e)?0:Gc(ao(e))}function er(e){return e&&e.__v_isReadonly?e:yo(e,!1,Hc,Kc,vo)}function bo(e){return yo(e,!0,jc,zc,_o)}function yo(e,t,i,n,r){if(!Ni(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;let o=r.get(e);if(o)return o;let l=Yc(e);if(l===0)return e;let d=new Proxy(e,l===2?n:i);return r.set(e,d),d}function Z(e){return e&&Z(e.__v_raw)||e}function Rn(e){return Boolean(e&&e.__v_isRef===!0)}Ae("nextTick",()=>Kn);Ae("dispatch",e=>Ut.bind(Ut,e));Ae("watch",(e,{evaluateLater:t,cleanup:i})=>(n,r)=>{let o=t(n),d=ps(()=>{let h;return o(m=>h=m),h},r);i(d)});Ae("store",uc);Ae("data",e=>Ss(e));Ae("root",e=>Ri(e));Ae("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=Jt(Qc(e))),e._x_refs_proxy));function Qc(e){let t=[];return Kt(e,i=>{i._x_refs&&t.push(i._x_refs)}),t}var gn={};function wo(e){return gn[e]||(gn[e]=0),++gn[e]}function Zc(e,t){return Kt(e,i=>{if(i._x_ids&&i._x_ids[t])return!0})}function eu(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=wo(t))}Ae("id",(e,{cleanup:t})=>(i,n=null)=>{let r=`${i}${n?`-${n}`:""}`;return tu(e,r,t,()=>{let o=Zc(e,i),l=o?o._x_ids[i]:wo(i);return n?`${i}-${l}-${n}`:`${i}-${l}`})});Fi((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function tu(e,t,i,n){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let r=n();return e._x_id[t]=r,i(()=>{delete e._x_id[t]}),r}Ae("el",e=>e);So("Focus","focus","focus");So("Persist","persist","persist");function So(e,t,i){Ae(t,n=>ye(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,n))}ie("modelable",(e,{expression:t},{effect:i,evaluateLater:n,cleanup:r})=>{let o=n(t),l=()=>{let v;return o(S=>v=S),v},d=n(`${t} = __placeholder`),h=v=>d(()=>{},{scope:{__placeholder:v}}),m=l();h(m),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let v=e._x_model.get,S=e._x_model.set,y=to({get(){return v()},set(E){S(E)}},{get(){return l()},set(E){h(E)}});r(y)})});ie("teleport",(e,{modifiers:t,expression:i},{cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&ye("x-teleport can only be used on a <template> tag",e);let r=ls(i),o=e.content.cloneNode(!0).firstElementChild;e._x_teleport=o,o._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),o.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(d=>{o.addEventListener(d,h=>{h.stopPropagation(),e.dispatchEvent(new h.constructor(h.type,h))})}),Vt(o,{},e);let l=(d,h,m)=>{m.includes("prepend")?h.parentNode.insertBefore(d,h):m.includes("append")?h.parentNode.insertBefore(d,h.nextSibling):h.appendChild(d)};ne(()=>{l(o,r,t),Ye(()=>{Ne(o),o._x_ignore=!0})()}),e._x_teleportPutBack=()=>{let d=ls(i);ne(()=>{l(e._x_teleport,d,t)})},n(()=>o.remove())});var iu=document.createElement("div");function ls(e){let t=Ye(()=>document.querySelector(e),()=>iu)();return t||ye(`Cannot find x-teleport element for selector: "${e}"`),t}var xo=()=>{};xo.inline=(e,{modifiers:t},{cleanup:i})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,i(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};ie("ignore",xo);ie("effect",Ye((e,{expression:t},{effect:i})=>{i(ue(e,t))}));function Dn(e,t,i,n){let r=e,o=h=>n(h),l={},d=(h,m)=>v=>m(h,v);if(i.includes("dot")&&(t=nu(t)),i.includes("camel")&&(t=ru(t)),i.includes("passive")&&(l.passive=!0),i.includes("capture")&&(l.capture=!0),i.includes("window")&&(r=window),i.includes("document")&&(r=document),i.includes("debounce")){let h=i[i.indexOf("debounce")+1]||"invalid-wait",m=Pi(h.split("ms")[0])?Number(h.split("ms")[0]):250;o=Zs(o,m)}if(i.includes("throttle")){let h=i[i.indexOf("throttle")+1]||"invalid-wait",m=Pi(h.split("ms")[0])?Number(h.split("ms")[0]):250;o=eo(o,m)}return i.includes("prevent")&&(o=d(o,(h,m)=>{m.preventDefault(),h(m)})),i.includes("stop")&&(o=d(o,(h,m)=>{m.stopPropagation(),h(m)})),i.includes("once")&&(o=d(o,(h,m)=>{h(m),r.removeEventListener(t,o,l)})),(i.includes("away")||i.includes("outside"))&&(r=document,o=d(o,(h,m)=>{e.contains(m.target)||m.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&h(m))})),i.includes("self")&&(o=d(o,(h,m)=>{m.target===e&&h(m)})),(ou(t)||Ao(t))&&(o=d(o,(h,m)=>{au(m,i)||h(m)})),r.addEventListener(t,o,l),()=>{r.removeEventListener(t,o,l)}}function nu(e){return e.replace(/-/g,".")}function ru(e){return e.toLowerCase().replace(/-(\w)/g,(t,i)=>i.toUpperCase())}function Pi(e){return!Array.isArray(e)&&!isNaN(e)}function su(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function ou(e){return["keydown","keyup"].includes(e)}function Ao(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function au(e,t){let i=t.filter(o=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(o));if(i.includes("debounce")){let o=i.indexOf("debounce");i.splice(o,Pi((i[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.includes("throttle")){let o=i.indexOf("throttle");i.splice(o,Pi((i[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(i.length===0||i.length===1&&cs(e.key).includes(i[0]))return!1;let r=["ctrl","shift","alt","meta","cmd","super"].filter(o=>i.includes(o));return i=i.filter(o=>!r.includes(o)),!(r.length>0&&r.filter(l=>((l==="cmd"||l==="super")&&(l="meta"),e[`${l}Key`])).length===r.length&&(Ao(e.type)||cs(e.key).includes(i[0])))}function cs(e){if(!e)return[];e=su(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(i=>{if(t[i]===e)return i}).filter(i=>i)}ie("model",(e,{modifiers:t,expression:i},{effect:n,cleanup:r})=>{let o=e;t.includes("parent")&&(o=e.parentNode);let l=ue(o,i),d;typeof i=="string"?d=ue(o,`${i} = __placeholder`):typeof i=="function"&&typeof i()=="string"?d=ue(o,`${i()} = __placeholder`):d=()=>{};let h=()=>{let y;return l(E=>y=E),us(y)?y.get():y},m=y=>{let E;l(P=>E=P),us(E)?E.set(y):d(()=>{},{scope:{__placeholder:y}})};typeof i=="string"&&e.type==="radio"&&ne(()=>{e.hasAttribute("name")||e.setAttribute("name",i)});var v=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let S=Xe?()=>{}:Dn(e,v,t,y=>{m(mn(e,t,y,h()))});if(t.includes("fill")&&([void 0,null,""].includes(h())||e.type==="checkbox"&&Array.isArray(h())||e.tagName.toLowerCase()==="select"&&e.multiple)&&m(mn(e,t,{target:e},h())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=S,r(()=>e._x_removeModelListeners.default()),e.form){let y=Dn(e.form,"reset",[],E=>{Kn(()=>e._x_model&&e._x_model.set(mn(e,t,{target:e},h())))});r(()=>y())}e._x_model={get(){return h()},set(y){m(y)}},e._x_forceModelUpdate=y=>{y===void 0&&typeof i=="string"&&i.match(/\./)&&(y=""),window.fromModel=!0,ne(()=>Xs(e,"value",y)),delete window.fromModel},n(()=>{let y=h();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(y)})});function mn(e,t,i,n){return ne(()=>{if(i instanceof CustomEvent&&i.detail!==void 0)return i.detail!==null&&i.detail!==void 0?i.detail:i.target.value;if(e.type==="checkbox")if(Array.isArray(n)){let r=null;return t.includes("number")?r=vn(i.target.value):t.includes("boolean")?r=Oi(i.target.value):r=i.target.value,i.target.checked?n.includes(r)?n:n.concat([r]):n.filter(o=>!lu(o,r))}else return i.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(i.target.selectedOptions).map(r=>{let o=r.value||r.text;return vn(o)}):t.includes("boolean")?Array.from(i.target.selectedOptions).map(r=>{let o=r.value||r.text;return Oi(o)}):Array.from(i.target.selectedOptions).map(r=>r.value||r.text);{let r;return e.type==="radio"?i.target.checked?r=i.target.value:r=n:r=i.target.value,t.includes("number")?vn(r):t.includes("boolean")?Oi(r):t.includes("trim")?r.trim():r}}})}function vn(e){let t=e?parseFloat(e):null;return cu(t)?t:e}function lu(e,t){return e==t}function cu(e){return!Array.isArray(e)&&!isNaN(e)}function us(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}ie("cloak",e=>queueMicrotask(()=>ne(()=>e.removeAttribute(yt("cloak")))));Bs(()=>`[${yt("init")}]`);ie("init",Ye((e,{expression:t},{evaluate:i})=>typeof t=="string"?!!t.trim()&&i(t,{},!1):i(t,{},!1)));ie("text",(e,{expression:t},{effect:i,evaluateLater:n})=>{let r=n(t);i(()=>{r(o=>{ne(()=>{e.textContent=o})})})});ie("html",(e,{expression:t},{effect:i,evaluateLater:n})=>{let r=n(t);i(()=>{r(o=>{ne(()=>{e.innerHTML=o,e._x_ignoreSelf=!0,Ne(e),delete e._x_ignoreSelf})})})});qn(Ps(":",Rs(yt("bind:"))));var Eo=(e,{value:t,modifiers:i,expression:n,original:r},{effect:o,cleanup:l})=>{if(!t){let h={};hc(h),ue(e,n)(v=>{no(e,v,r)},{scope:h});return}if(t==="key")return uu(e,n);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let d=ue(e,n);o(()=>d(h=>{h===void 0&&typeof n=="string"&&n.match(/\./)&&(h=""),ne(()=>Xs(e,t,h,i))})),l(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};Eo.inline=(e,{value:t,modifiers:i,expression:n})=>{!t||(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:n,extract:!1})};ie("bind",Eo);function uu(e,t){e._x_keyExpression=t}Us(()=>`[${yt("data")}]`);ie("data",(e,{expression:t},{cleanup:i})=>{if(du(e))return;t=t===""?"{}":t;let n={};xn(n,e);let r={};pc(r,n);let o=st(e,t,{scope:r});(o===void 0||o===!0)&&(o={}),xn(o,e);let l=_t(o);xs(l);let d=Vt(e,l);l.init&&st(e,l.init),i(()=>{l.destroy&&st(e,l.destroy),d()})});Fi((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function du(e){return Xe?Ln?!0:e.hasAttribute("data-has-alpine-state"):!1}ie("show",(e,{modifiers:t,expression:i},{effect:n})=>{let r=ue(e,i);e._x_doHide||(e._x_doHide=()=>{ne(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{ne(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let o=()=>{e._x_doHide(),e._x_isShown=!1},l=()=>{e._x_doShow(),e._x_isShown=!0},d=()=>setTimeout(l),h=On(S=>S?l():o(),S=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,S,l,o):S?d():o()}),m,v=!0;n(()=>r(S=>{!v&&S===m||(t.includes("immediate")&&(S?d():o()),h(S),m=S,v=!1)}))});ie("for",(e,{expression:t},{effect:i,cleanup:n})=>{let r=fu(t),o=ue(e,r.items),l=ue(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},i(()=>hu(e,r,o,l)),n(()=>{Object.values(e._x_lookup).forEach(d=>d.remove()),delete e._x_prevKeys,delete e._x_lookup})});function hu(e,t,i,n){let r=l=>typeof l=="object"&&!Array.isArray(l),o=e;i(l=>{pu(l)&&l>=0&&(l=Array.from(Array(l).keys(),T=>T+1)),l===void 0&&(l=[]);let d=e._x_lookup,h=e._x_prevKeys,m=[],v=[];if(r(l))l=Object.entries(l).map(([T,b])=>{let O=ds(t,b,T,l);n(N=>{v.includes(N)&&ye("Duplicate key on x-for",e),v.push(N)},{scope:Fe({index:T},O)}),m.push(O)});else for(let T=0;T<l.length;T++){let b=ds(t,l[T],T,l);n(O=>{v.includes(O)&&ye("Duplicate key on x-for",e),v.push(O)},{scope:Fe({index:T},b)}),m.push(b)}let S=[],y=[],E=[],P=[];for(let T=0;T<h.length;T++){let b=h[T];v.indexOf(b)===-1&&E.push(b)}h=h.filter(T=>!E.includes(T));let D="template";for(let T=0;T<v.length;T++){let b=v[T],O=h.indexOf(b);if(O===-1)h.splice(T,0,b),S.push([D,T]);else if(O!==T){let N=h.splice(T,1)[0],V=h.splice(O-1,1)[0];h.splice(T,0,V),h.splice(O,0,N),y.push([N,V])}else P.push(b);D=b}for(let T=0;T<E.length;T++){let b=E[T];d[b]._x_effects&&d[b]._x_effects.forEach(hs),d[b].remove(),d[b]=null,delete d[b]}for(let T=0;T<y.length;T++){let[b,O]=y[T],N=d[b],V=d[O],de=document.createElement("div");ne(()=>{V||ye('x-for ":key" is undefined or invalid',o,O,d),V.after(de),N.after(V),V._x_currentIfEl&&V.after(V._x_currentIfEl),de.before(N),N._x_currentIfEl&&N.after(N._x_currentIfEl),de.remove()}),V._x_refreshXForScope(m[v.indexOf(O)])}for(let T=0;T<S.length;T++){let[b,O]=S[T],N=b==="template"?o:d[b];N._x_currentIfEl&&(N=N._x_currentIfEl);let V=m[O],de=v[O],fe=document.importNode(o.content,!0).firstElementChild,He=_t(V);Vt(fe,He,o),fe._x_refreshXForScope=wt=>{Object.entries(wt).forEach(([je,St])=>{He[je]=St})},ne(()=>{N.after(fe),Ye(()=>Ne(fe))()}),typeof de=="object"&&ye("x-for key cannot be an object, it must be a string or an integer",o),d[de]=fe}for(let T=0;T<P.length;T++)d[P[T]]._x_refreshXForScope(m[v.indexOf(P[T])]);o._x_prevKeys=v})}function fu(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,i=/^\s*\(|\)\s*$/g,n=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,r=e.match(n);if(!r)return;let o={};o.items=r[2].trim();let l=r[1].replace(i,"").trim(),d=l.match(t);return d?(o.item=l.replace(t,"").trim(),o.index=d[1].trim(),d[2]&&(o.collection=d[2].trim())):o.item=l,o}function ds(e,t,i,n){let r={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(l=>l.trim()).forEach((l,d)=>{r[l]=t[d]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(l=>l.trim()).forEach(l=>{r[l]=t[l]}):r[e.item]=t,e.index&&(r[e.index]=i),e.collection&&(r[e.collection]=n),r}function pu(e){return!Array.isArray(e)&&!isNaN(e)}function Co(){}Co.inline=(e,{expression:t},{cleanup:i})=>{let n=Ri(e);n._x_refs||(n._x_refs={}),n._x_refs[t]=e,i(()=>delete n._x_refs[t])};ie("ref",Co);ie("if",(e,{expression:t},{effect:i,cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&ye("x-if can only be used on a <template> tag",e);let r=ue(e,t),o=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let d=e.content.cloneNode(!0).firstElementChild;return Vt(d,{},e),ne(()=>{e.after(d),Ye(()=>Ne(d))()}),e._x_currentIfEl=d,e._x_undoIf=()=>{We(d,h=>{h._x_effects&&h._x_effects.forEach(hs)}),d.remove(),delete e._x_currentIfEl},d},l=()=>{!e._x_undoIf||(e._x_undoIf(),delete e._x_undoIf)};i(()=>r(d=>{d?o():l()})),n(()=>e._x_undoIf&&e._x_undoIf())});ie("id",(e,{expression:t},{evaluate:i})=>{i(t).forEach(r=>eu(e,r))});Fi((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});qn(Ps("@",Rs(yt("on:"))));ie("on",Ye((e,{value:t,modifiers:i,expression:n},{cleanup:r})=>{let o=n?ue(e,n):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let l=Dn(e,t,i,d=>{o(()=>{},{scope:{$event:d},params:[d]})});r(()=>l())}));ji("Collapse","collapse","collapse");ji("Intersect","intersect","intersect");ji("Focus","trap","focus");ji("Mask","mask","mask");function ji(e,t,i){ie(t,n=>ye(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${i}`,n))}zt.setEvaluator(Os);zt.setReactivityEngine({reactive:er,effect:Ac,release:Ec,raw:Z});var gu=zt,tr=gu;var $e={},ir=null;$e.Dropdown={mounted(){let e=this.el.querySelector("#template-name"),t=this.el.querySelector("#dropdown-menu"),i=this.el.querySelector("#dropdown-toggle"),n=-1,r=(m,v)=>{let S;return function(...y){clearTimeout(S),S=setTimeout(()=>m.apply(this,y),v)}};i.addEventListener("click",()=>{t.classList.toggle("hidden"),t.classList.contains("hidden")||e.focus()});let o=()=>{let m=t.querySelector(".no-results");m||(m=document.createElement("li"),m.classList.add("no-results","cursor-default","select-none","relative","py-2","pl-3","pr-9","text-gray-500"),m.textContent="No results found",t.querySelector("ul").appendChild(m)),m.classList.remove("hidden")},l=()=>{let m=t.querySelector(".no-results");m&&m.classList.add("hidden")},d=r(m=>{let v=m.target.value.toLowerCase(),S=!1;this.el.querySelectorAll("#dropdown-menu li:not(.no-results)").forEach((E,P)=>{E.textContent.toLowerCase().includes(v)?(E.classList.remove("hidden"),S=!0):E.classList.add("hidden")}),S?(t.classList.remove("hidden"),l()):(t.classList.remove("hidden"),o())},300);e.addEventListener("keyup",d),e.addEventListener("keydown",m=>{let v=Array.from(t.querySelectorAll("li:not(.no-results):not(.hidden)"));switch(m.key){case"ArrowDown":m.preventDefault(),v.length>0&&(n=(n+1)%v.length,h(v[n]));break;case"ArrowUp":m.preventDefault(),v.length>0&&(n=(n-1+v.length)%v.length,h(v[n]));break;case"Enter":m.preventDefault(),n>-1&&v[n]&&v[n].click();break;case"Escape":m.preventDefault(),t.classList.add("hidden");break}}),e.addEventListener("focus",()=>{t.classList.remove("hidden")}),t.querySelectorAll("li").forEach((m,v)=>{m.addEventListener("click",S=>{S.stopPropagation();let y=m.getAttribute("phx-value-value"),E=m.getAttribute("phx-value-name");e.value=E,t.classList.add("hidden"),this.pushEvent("select_report_type",{name:E,value:y}),e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))})});let h=m=>{t.querySelectorAll("li").forEach(S=>S.classList.remove("bg-indigo-600","text-white")),m&&(m.scrollIntoView({block:"nearest"}),m.classList.add("bg-indigo-600","text-white"))}}};$e.Dropdown2={mounted(){let e=this.el.querySelector("#select_gl_code"),t=this.el.querySelector("#dropdown-menu"),i=this.el.querySelector("#dropdown-toggle");console.log("Dropdown2 hook mounted"),i.addEventListener("click",n=>{n.stopPropagation(),console.log("Dropdown icon clicked"),t.classList.contains("hidden")?(t.classList.remove("hidden"),e.focus(),console.log("Dropdown opened")):(t.classList.add("hidden"),console.log("Dropdown closed")),this.requeryListItems()}),this.requeryListItems=()=>{this.el.querySelectorAll("#dropdown-menu li:not(.no-results)").forEach(r=>{r.addEventListener("click",o=>{o.stopPropagation(),console.log("Dropdown item clicked");let l=r.getAttribute("phx-value-value"),d=r.getAttribute("phx-value-name");e.value=d,t.classList.add("hidden"),this.pushEvent("select_gl_code",{name:d,value:l});let h=new Event("input",{bubbles:!0}),m=new Event("change",{bubbles:!0});e.dispatchEvent(h),e.dispatchEvent(m)})})},e.addEventListener("keyup",n=>{let r=n.target.value.toLowerCase(),o=!1;this.el.querySelectorAll("#dropdown-menu li:not(.no-results)").forEach(d=>{let h=d.textContent.toLowerCase(),m=d.getAttribute("phx-value-value").toLowerCase();h.includes(r)||m.includes(r)?(d.classList.remove("hidden"),o=!0):d.classList.add("hidden")}),o?(t.classList.remove("hidden"),this.hideNoResultsMessage()):(t.classList.remove("hidden"),this.showNoResultsMessage()),this.requeryListItems()}),this.showNoResultsMessage=()=>{let n=t.querySelector(".no-results");n||(n=document.createElement("li"),n.classList.add("no-results","cursor-default","select-none","relative","py-2","pl-3","pr-9","text-gray-500"),n.textContent="No results found",t.querySelector("ul").appendChild(n)),n.classList.remove("hidden")},this.hideNoResultsMessage=()=>{let n=t.querySelector(".no-results");n&&n.classList.add("hidden")},e.addEventListener("focus",()=>{e.value.length>0&&t.classList.remove("hidden")}),document.addEventListener("click",n=>{!this.el.contains(n.target)&&!i.contains(n.target)&&t.classList.add("hidden")})}};$e.Notification={alert(e,t){var i=document.getElementById(e+"-notification"),n=document.getElementById(e+"-notification-panel"),r=document.getElementById(e+"-notification-msg");n.classList.remove("translate-y-2","opacity-0","sm:translate-y-0","sm:translate-x-2"),n.classList.add("translate-y-0","opacity-100","sm:translate-x-0"),i.classList.remove("hidden"),r.textContent=t},mounted(){this.handleEvent("notification",({message:e})=>{var t=document.getElementById("modal-close");t&&t.click(),e.info?(this.alert("info",e.info),e.push_to&&this.pushEvent(e.push_to,e.params)):this.alert("error",e.error)})}};$e.HandleConfirmation={params(){return this.el.dataset},mounted(){this.el.addEventListener("click",e=>{this.params().isconfirm=="YES"&&(this.el.disabled=!0,document.getElementById("modal-confirm-msg").classList.add("hidden"),document.getElementById("modal-processing-msg").classList.remove("hidden"),this.pushEvent(ir.event,ir))})}};$e.Confirm={params(){return this.el.dataset},mounted(){this.el.addEventListener("click",e=>{ir=this.params()})}};$e.PrintPage={mounted(){var e=window.open("","PRINT","height=400,width=600");e.document.write("<html><head><title>"+document.title+"</title>"),e.document.write("</head><body >"),e.document.write("<h1>"+document.title+"</h1>"),e.document.write(document.getElementById(elem).innerHTML),e.document.write("</body></html>"),e.document.close(),e.focus(),e.print(),e.close()}};$e.UserRoleCheckboxes={mounted(){this.updateCheckboxes()},updated(){this.updateCheckboxes()},updateCheckboxes(){this.el.querySelectorAll("[data-select-val]").forEach(t=>{let i=t.getAttribute("data-select-val");t.checked=i==="Y"})}};$e.live_select;var ko=$e;var $u=yr(Oo());window.Alpine=tr;tr.start();var mu=document.querySelector("meta[name='csrf-token']").getAttribute("content"),To=new Xr("/live",Sr,{hooks:ko,dom:{onBeforeElUpdated(e,t){e._x_dataStack&&window.Alpine.clone(e,t)}},params:{_csrf_token:mu},timeout:6e4});window.addEventListener("phx:toggle-dropdown",e=>{if(e.target.classList.contains("dropdown-toggle")){let t=document.querySelectorAll(".dropdown-menu-open");for(let i=0;i<t.length;i++)t[i].classList.add("transform","opacity-0","scale-95","hidden"),t[i].classList.remove("transform","opacity-100","scale-100","dropdown-menu-open");e.target.classList.remove("dropdown-toggle")}else e.target.classList.contains("scale-95")?(e.target.classList.remove("transform","opacity-0","scale-95","hidden"),e.target.classList.add("transform","opacity-100","scale-100","dropdown-menu-open"),e.target.previousElementSibling.querySelector("button").classList.add("dropdown-toggle")):e.target.classList.contains("dropdown-menu-open")&&(e.target.classList.add("transform","opacity-0","scale-95","hidden"),e.target.classList.remove("transform","opacity-100","scale-100","dropdown-menu-open"),e.target.previousElementSibling.querySelector("button").classList.remove("dropdown-toggle"))});window.addEventListener("phx:toggle-menu-state",e=>{e.target.classList.contains("bg-indigo-700")?(e.target.classList.remove("bg-indigo-700","text-white"),e.target.classList.add("text-white","hover:bg-indigo-500","hover:bg-opacity-75")):(e.target.classList.add("bg-indigo-700","text-white"),e.target.classList.remove("text-white","hover:bg-indigo-500","hover:bg-opacity-75"))});window.addEventListener("phx:table-dropdown",e=>{if(e.target.classList.contains("dropdown-toggle")){let t=document.querySelectorAll(".dropdown-menu-open");for(let i=0;i<t.length;i++)t[i].classList.add("hidden"),t[i].classList.remove("dropdown-menu-open");e.target.classList.remove("dropdown-toggle")}else e.target.classList.contains("hidden")?(e.target.classList.remove("hidden"),e.target.classList.add("dropdown-menu-open"),e.target.previousElementSibling.classList.add("dropdown-toggle")):e.target.classList.contains("dropdown-menu-open")&&(e.target.classList.add("hidden"),e.target.classList.remove("dropdown-menu-open"),e.target.previousElementSibling.classList.remove("dropdown-toggle"))});Ui.default.config({barColors:{0:"#29d"},shadowColor:"rgba(0, 0, 0, .3)"});window.addEventListener("phx:page-loading-start",e=>Ui.default.show());window.addEventListener("phx:page-loading-stop",e=>Ui.default.hide());To.connect();initLiveReact();window.liveSocket=To;})();
/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */
/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */
/**
 * @license MIT
 * topbar 1.0.0, 2021-01-06
 * https://buunguyen.github.io/topbar
 * Copyright (c) 2021 Buu Nguyen
 */
