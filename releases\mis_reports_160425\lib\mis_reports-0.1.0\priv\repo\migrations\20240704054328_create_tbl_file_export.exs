defmodule MisReports.Repo.Migrations.CreateTblFileExport do
  use Ecto.Migration

  def change do
    create table(:tbl_file_export) do
      add :report_date, :date
      add :status, :string
      add :auth_status, :string
      add :uuid, :string
      add :checker_id, references(:tbl_users, on_delete: :nothing)
      add :maker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create unique_index(:tbl_file_export, [:report_date])
    create index(:tbl_file_export, [:checker_id])
    create index(:tbl_file_export, [:maker_id])
  end
end
